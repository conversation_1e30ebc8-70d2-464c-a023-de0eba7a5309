<?php

/*
Plugin Name: Advanced Custom Fields: Divider
Plugin URI: PLUGIN_URL
Description: An divider field that lets you group multiple fields under an divider.
Version: 1.0.0
Author: <PERSON><PERSON><PERSON>aj
Author URI: https://www.github.com/kreshnik
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
*/




// 1. set text domain
// Reference: https://codex.wordpress.org/Function_Reference/load_plugin_textdomain
load_plugin_textdomain( 'acf-divider', false, dirname( plugin_basename(__FILE__) ) . '/lang/' );




// 2. Include field type for ACF5
// $version = 5 and can be ignored until ACF6 exists
function include_field_types_divider( $version ) {

	include_once('acf-divider-v5.php');

}

add_action('acf/include_field_types', 'include_field_types_divider');




// 3. Include field type for ACF4
function register_fields_divider() {

	include_once('acf-divider-v4.php');

}

add_action('acf/register_fields', 'register_fields_divider');




?>