# SAP Inventory API - Batch Update Enhancement

## Overview
Enhanced the SAP Inventory plugin to support batch updates while maintaining backward compatibility with single material updates. The API now accepts both single material updates and batch updates of multiple materials in a single request.

## Request Formats

### 1. Single Material Update (Original Format - Backward Compatible)
```json
{
  "inventory": {
    "materialNumber": "M12345",
    "stock": {
      "US": {
        "quantity": 100,
        "allowBackorders": false
      },
      "EU": {
        "quantity": 200,
        "allowBackorders": true
      }
    }
  }
}
```

### 2. Batch Material Update (New Format)
```json
{
  "inventories": [
    {
      "materialNumber": "M12345",
      "stock": {
        "US": {
          "quantity": 150,
          "allowBackorders": false
        },
        "EU": {
          "quantity": 250,
          "allowBackorders": true
        },
        "CA": {
          "quantity": 75,
          "allowBackorders": false
        }
      }
    },
    {
      "materialNumber": "M67890",
      "stock": {
        "US": {
          "quantity": 300,
          "allowBackorders": true
        },
        "EU": {
          "quantity": 400,
          "allowBackorders": false
        }
      }
    }
  ]
}
```

## Response Formats

### Single Material Response
```json
{
  "success": true,
  "is_batch": false,
  "total_materials_processed": 1,
  "total_materials_requested": 1,
  "total_regions_updated": 2,
  "product_id": 123,
  "material_number": "M12345",
  "updated_regions": [
    {
      "region": "US",
      "stock_quantity": 100,
      "allow_backorders": false,
      "stock_meta_key": "_stock_us",
      "backorders_meta_key": "_backorders_us",
      "backorders_updated": true,
      "final_stock_value": "100",
      "final_backorders_value": "no"
    },
    {
      "region": "EU",
      "stock_quantity": 200,
      "allow_backorders": true,
      "stock_meta_key": "_stock_eu",
      "backorders_meta_key": "_backorders_eu",
      "backorders_updated": true,
      "final_stock_value": "200",
      "final_backorders_value": "yes"
    }
  ],
  "processed_materials": [
    {
      "product_id": 123,
      "material_number": "M12345",
      "updated_regions": [...],
      "total_regions_updated": 2,
      "errors": []
    }
  ],
  "errors": []
}
```

### Batch Material Response
```json
{
  "success": true,
  "is_batch": true,
  "total_materials_processed": 2,
  "total_materials_requested": 2,
  "total_regions_updated": 5,
  "processed_materials": [
    {
      "product_id": 123,
      "material_number": "M12345",
      "updated_regions": [
        {
          "region": "US",
          "stock_quantity": 150,
          "allow_backorders": false
        },
        {
          "region": "EU",
          "stock_quantity": 250,
          "allow_backorders": true
        },
        {
          "region": "CA",
          "stock_quantity": 75,
          "allow_backorders": false
        }
      ],
      "total_regions_updated": 3,
      "errors": []
    },
    {
      "product_id": 456,
      "material_number": "M67890",
      "updated_regions": [
        {
          "region": "US",
          "stock_quantity": 300,
          "allow_backorders": true
        },
        {
          "region": "EU",
          "stock_quantity": 400,
          "allow_backorders": false
        }
      ],
      "total_regions_updated": 2,
      "errors": []
    }
  ],
  "errors": []
}
```

## Key Features

### 1. Backward Compatibility
- Existing single material update requests continue to work unchanged
- Response format for single requests maintains all original fields
- No breaking changes to existing integrations

### 2. Batch Processing
- Process multiple materials in a single API call
- Reduces API overhead and improves performance
- Each material is processed independently
- Partial success handling - some materials can succeed while others fail

### 3. Enhanced Logging
- Detailed logging for both single and batch requests
- Individual material processing logs
- Request correlation with unique IDs
- Performance metrics for batch operations

### 4. Error Handling
- Individual error tracking per material
- Comprehensive error reporting
- Partial success scenarios handled gracefully
- Detailed error messages with context

## Implementation Details

### New Functions Added
1. **`process_single_inventory_item()`** - Handles individual material processing
2. Enhanced main endpoint to detect and route single vs batch requests
3. Improved logging for batch operations

### Request Detection Logic
```php
$is_batch_request = isset( $data['inventories'] ) && is_array( $data['inventories'] );
$is_single_request = isset( $data['inventory'] ) && is_array( $data['inventory'] );
```

### Unified Processing
- Single requests are converted to batch format internally
- All processing uses the same `process_single_inventory_item()` function
- Consistent error handling and logging across both formats

## Performance Considerations

### Benefits of Batch Updates
- **Reduced API Calls**: Update multiple materials in one request
- **Lower Overhead**: Single authentication and validation per batch
- **Better Performance**: Reduced network latency and server processing time
- **Atomic Logging**: Complete batch operation logged together

### Recommended Batch Sizes
- **Small Batches**: 1-10 materials - Optimal for most use cases
- **Medium Batches**: 10-50 materials - Good for regular updates
- **Large Batches**: 50+ materials - Use with caution, monitor performance

## Error Scenarios

### Partial Success Example
```json
{
  "success": true,
  "partial_success": true,
  "is_batch": true,
  "total_materials_processed": 2,
  "total_materials_requested": 3,
  "total_regions_updated": 4,
  "processed_materials": [...],
  "errors": [
    "Item 2: Product not found for materialNumber: M99999"
  ]
}
```

### Complete Failure Example
```json
{
  "success": false,
  "is_batch": true,
  "total_materials_processed": 0,
  "total_materials_requested": 2,
  "total_regions_updated": 0,
  "processed_materials": [],
  "errors": [
    "Item 0: Product not found for materialNumber: M11111",
    "Item 1: Product not found for materialNumber: M22222"
  ]
}
```

## Testing
A comprehensive test script `test-inventory-batch.php` has been created that demonstrates:
1. Single material updates (backward compatibility)
2. Small batch updates (2-3 materials)
3. Large batch updates (10+ materials)
4. Error handling scenarios
5. Expected response formats

## Migration Guide
No migration is required. Existing integrations will continue to work as before. To use batch updates:

1. Change request format from `"inventory": {...}` to `"inventories": [...]`
2. Update response parsing to handle the new batch response format
3. Implement error handling for partial success scenarios
4. Consider optimal batch sizes for your use case

## Monitoring and Logs
All batch operations are logged to `wp-content/logs/APIlogs.log` with:
- Batch size and processing summary
- Individual material processing results
- Performance metrics (execution time)
- Error details and context
- Request correlation IDs for debugging
