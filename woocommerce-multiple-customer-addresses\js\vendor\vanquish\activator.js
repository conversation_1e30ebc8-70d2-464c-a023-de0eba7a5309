"use strict";
jQuery(document).ready(function()
{
	jQuery(document).on('click', '#activation_button', vanquish_on_activation_click);
});
function vanquish_on_activation_click(event)
{
	event.stopPropagation();
	event.preventDefault();
	
	if(jQuery('#input_purchase_code').val() == "" || jQuery('#input_buyer').val()  == "")
	{
		alert(vanquish_activator_settings.empty_fields_error);
		return false;
	}
	//UI
	jQuery('#activation_button, .input_field').prop('disabled', true);
	vanquish_set_status_message(vanquish_activator_settings.status_default_message);
	
	
	var formData = new FormData();
	formData.append('purchase_code', jQuery('#input_purchase_code').val());	
	formData.append('buyer', jQuery('#input_buyer').val());				
	formData.append('domain', jQuery('#domain').val());				
	formData.append('item_id', jQuery('#item_id').val());	
	
	jQuery.ajax({
		url: vanquish_activator_settings.verifier_url+"?"+(new URLSearchParams(formData).toString()),
		type: 'GET',
		dataType: 'jsonp',
		jsonpCallback: "activation",
		crossDomain: true,
	
		async: true,
		success: function (data) 
		{			
			
			var result = data;
			switch(result.code)
			{
				case "ok": vanquish_set_status_message(vanquish_activator_settings.purchase_code_valid); 
						   vanquish_activation_complete({id: jQuery('#item_id').val(), domain: jQuery('#domain').val(), purchase_code: jQuery('#input_purchase_code').val()}); 
						   return;
						   break;
				case "db_connection_error": ;
				case "db_connection_error_select": ;
				case "db_connection_error_update": vanquish_set_status_message(vanquish_activator_settings.db_error); break;
				case "invalid_buyer": vanquish_set_status_message(vanquish_activator_settings.buyer_invalid); break;
				case "invalid_item_id": vanquish_set_status_message(vanquish_activator_settings.purchase_code_invalid); break;
				case "invalid_purchase_code": vanquish_set_status_message(vanquish_activator_settings.purchase_code_invalid); break;
				case "max_num_domain_reached": vanquish_set_status_message(vanquish_activator_settings.num_domain_reached+result.domains); break;
			}
			
			//UI
			jQuery('#activation_button, .input_field').prop('disabled', false);
			
		},
		error: function (data) 
		{
			
		},
		cache: false,
		contentType: false,
		processData: false
	});
	
	return false;
}
function vanquish_set_status_message(message)
{
	jQuery('#status').html(message);
	jQuery('#status').fadeIn();
}
function vanquish_activation_complete(args)
{
	var formData = new FormData();
	formData.append('action', "vanquish_activation_"+args.id);	
	formData.append('id', args.id);	
	formData.append('domain', args.domain);	
	formData.append('purchase_code', args.purchase_code);	
	formData.append('security', vanquish_activator_settings.security);	
	
	jQuery.ajax({
		url: ajaxurl,
		type: 'POST',
		data: formData,
		async: true,
		success: function (data) 
		{
			location.reload();
		},
		error: function (data) 
		{
			
		},
		cache: false,
		contentType: false,
		processData: false
	});
}