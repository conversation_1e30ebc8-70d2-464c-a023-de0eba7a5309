# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-05-22T11:47:45+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: da_DK\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr ""

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn how to fix this"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""

#: includes/api/api-template.php:376 includes/api/api-template.php:430
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:242
#: includes/api/api-template.php:934
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#. %3$s - Link to show more details about the error
#: includes/admin/views/escaped-html-notice.php:19
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s. %3$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:14
msgid "Please contact your site administrator or developer for more details."
msgstr ""

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr ""

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:11
msgid "Show&nbsp;details"
msgstr ""

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:34
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr ""

#: includes/admin/views/global/navigation.php:223
msgid "Renew ACF PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr ""

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr ""

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr ""

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr ""

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:56
msgid "(Duplicated from %s)"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-field-group.js:2782
#: assets/build/js/acf-field-group.js:3272
msgid "This Field"
msgstr ""

#: includes/admin/admin.php:311
msgid "ACF PRO"
msgstr ""

#: includes/admin/admin.php:309
msgid "Feedback"
msgstr ""

#: includes/admin/admin.php:307
msgid "Support"
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:282
msgid "is developed and maintained by"
msgstr ""

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr ""

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr ""

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:387
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-user.php:82
msgid "Select Multiple"
msgstr ""

#: includes/admin/views/global/navigation.php:235
msgid "WP Engine logo"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1136
msgid "The capability name for assigning terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1135
msgid "Assign Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1119
msgid "The capability name for deleting terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1118
msgid "Delete Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1102
msgid "The capability name for editing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1101
msgid "Edit Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1085
msgid "The capability name for managing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1084
msgid "Manage Terms Capability"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:891
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr ""

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
msgid "Learn More"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:432
#: includes/fields/class-acf-field-post_object.php:350
#: includes/fields/class-acf-field-relationship.php:553
msgid "Any post status"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr ""

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr ""

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr ""

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:23
msgid "WYSIWYG Editor"
msgstr ""

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""

#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:25
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:25
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:24
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""

#: includes/fields/class-acf-field-text.php:24
msgid "A basic text input, useful for storing single string values."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:26
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:25
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:24
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:25
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:19
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:24
msgid "An input for providing a password using a masked field."
msgstr ""

#: includes/fields/class-acf-field-page_link.php:424
#: includes/fields/class-acf-field-post_object.php:342
#: includes/fields/class-acf-field-relationship.php:545
msgid "Filter by Post Status"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:25
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:25
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:24
msgid "An input limited to numerical values."
msgstr ""

#: includes/fields/class-acf-field-message.php:26
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:25
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:25
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""

#: includes/fields/class-acf-field-group.php:25
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:25
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:25
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""

#: includes/fields/class-acf-field-email.php:24
msgid "A text input specifically designed for storing email addresses."
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:25
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:25
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:25
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:26
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:27
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""

#: includes/fields.php:456
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:446
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""

#: includes/fields.php:436
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:426
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:423
msgctxt "noun"
msgid "Clone"
msgstr ""

#: includes/admin/views/global/navigation.php:86 includes/fields.php:338
msgid "PRO"
msgstr ""

#: includes/fields.php:336 includes/fields.php:393
msgid "Advanced"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:85
msgid "JSON (newer)"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Original"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:55
msgid "Invalid post ID."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid post type selected for review."
msgstr ""

#: includes/admin/views/global/navigation.php:186
msgid "More"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr ""

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr ""

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
msgid "No search results for '%s'"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr ""

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Expose this post type in the REST API."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1055
msgid "Customize the query variable name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid "Customize the slug used in the URL"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:924
msgid "Permalinks for this taxonomy are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:921
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:913
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:896
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:895
msgid "Show Admin Column"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:882
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:881
msgid "Quick Edit"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:868
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:867
msgid "Tag Cloud"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:824
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:823
msgid "Meta Box Sanitization Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:805
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "No Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Custom Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:757
msgid "Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Categories Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:766
msgid "Tags Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Controller Class"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1256
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1255
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1190
msgid "Namespace Route"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1172
msgid "The base URL for the post type REST API URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1236
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1171
msgid "Base URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1222
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1154
msgid "Show In REST API"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1200
msgid "Customize the query variable name."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1199
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1054
msgid "Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1177
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1032
msgid "No Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1176
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1031
msgid "Custom Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1173
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1172
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1003
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1146
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1002
msgid "Publicly Queryable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1125
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1124
msgid "Archive Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1111
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "Archive"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1089
msgid "Pagination"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1072
msgid "RSS feed URL for the post type items."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Feed URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1052
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1033
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1032
#: includes/admin/views/acf-taxonomy/advanced-settings.php:940
msgid "URL Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1016
msgid "Permalinks for this post type are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1015
#: includes/admin/views/acf-taxonomy/advanced-settings.php:923
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1007
#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1006
#: includes/admin/views/acf-taxonomy/advanced-settings.php:914
msgid "Custom Permalink"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1005
#: includes/admin/views/acf-post-type/advanced-settings.php:1175
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1003
#: includes/admin/views/acf-post-type/advanced-settings.php:1013
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1001
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Permalink Rewrite"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:987
msgid "Delete items by a user when that user is deleted."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:986
msgid "Delete With User"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:972
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:971
msgid "Can Export"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:940
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:939
msgid "Plural Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:921
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:920
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:906
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:905
msgid "Rename Capabilities"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:890
msgid "Exclude From Search"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:877
#: includes/admin/views/acf-taxonomy/advanced-settings.php:854
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:876
#: includes/admin/views/acf-taxonomy/advanced-settings.php:853
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:858
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:857
msgid "Show In Admin Bar"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:826
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:825
msgid "Custom Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:805
msgid "Menu Icon"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:787
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:786
msgid "Menu Position"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:768
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:767
msgid "Admin Menu Parent"
msgstr ""

#. translators: %s = "dashicon class name", link to the WordPress dashicon
#. documentation.
#: includes/admin/views/acf-post-type/advanced-settings.php:755
msgid ""
"The icon used for the post type menu item in the admin dashboard. Can be a "
"URL or %s to use for the icon."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:750
msgid "Dashicon class name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:289
msgid "Nothing to import"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:284
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:275
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:259
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:241
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:230
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:206
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:121
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:110
#: includes/admin/tools/class-acf-admin-tool-import.php:126
msgid "Import from Custom Post Type UI"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomy.php:127
#: assets/build/js/acf-internal-post-type.js:182
#: assets/build/js/acf-internal-post-type.js:256
msgid "Category"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:125
#: assets/build/js/acf-internal-post-type.js:179
#: assets/build/js/acf-internal-post-type.js:253
msgid "Tag"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr ""

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:81
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:126
#: assets/build/js/acf-internal-post-type.js:176
#: assets/build/js/acf-internal-post-type.js:250
msgid "Pages"
msgstr ""

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-field-group.js:1146
#: assets/build/js/acf-field-group.js:1367
msgid "Type to search..."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:101
#: assets/build/js/acf-field-group.js:1173
#: assets/build/js/acf-field-group.js:2336
#: assets/build/js/acf-field-group.js:1413
#: assets/build/js/acf-field-group.js:2745
msgid "PRO Only"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-internal-post-type.js:308
#: assets/build/js/acf-internal-post-type.js:417
msgid "Field groups linked successfully."
msgstr ""

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:45 includes/admin/admin.php:311
msgid "ACF"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr ""

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr ""

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr ""

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr ""

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr ""

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr ""

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr ""

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr ""

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr ""

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:249
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:1010
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Værdien af ACF shortcode vises ikke i preview]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:539
msgid "Close Modal"
msgstr "Luk modal"

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:1688
#: assets/build/js/acf-field-group.js:2016
msgid "Field moved to other group"
msgstr "Felt er flyttet til en anden gruppe"

#: includes/admin/post-types/admin-field-group.php:91
#: assets/build/js/acf.js:1437 assets/build/js/acf.js:1517
msgid "Close modal"
msgstr "Luk modal"

#: includes/fields/class-acf-field-tab.php:118
msgid "Start a new group of tabs at this tab."
msgstr "Start en ny gruppe af tabs med denne tab."

#: includes/fields/class-acf-field-tab.php:117
msgid "New Tab Group"
msgstr "Ny tab gruppe"

#: includes/fields/class-acf-field-select.php:429
#: includes/fields/class-acf-field-true_false.php:190
msgid "Use a stylized checkbox using select2"
msgstr "Brug en stylet checkbox med select2"

#: includes/fields/class-acf-field-radio.php:253
msgid "Save Other Choice"
msgstr "Gem andre valg"

#: includes/fields/class-acf-field-radio.php:242
msgid "Allow Other Choice"
msgstr "Tillad Andet valg"

#: includes/fields/class-acf-field-checkbox.php:425
msgid "Add Toggle All"
msgstr "Tilføj \"Vælg alle\""

#: includes/fields/class-acf-field-checkbox.php:384
msgid "Save Custom Values"
msgstr "Gem brugerdefineret værdier"

#: includes/fields/class-acf-field-checkbox.php:373
msgid "Allow Custom Values"
msgstr "Tillad brugerdefinerede værdier"

#: includes/fields/class-acf-field-checkbox.php:137
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: includes/admin/views/global/navigation.php:250
msgid "Updates"
msgstr "Opdateringer"

#: includes/admin/views/global/navigation.php:176
msgid "Advanced Custom Fields logo"
msgstr "Advanced Custom Fields logo"

#: includes/admin/views/global/form-top.php:89
msgid "Save Changes"
msgstr "Gem ændringer"

#: includes/admin/views/global/form-top.php:76
msgid "Field Group Title"
msgstr "Feltgruppe titel"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Tilføj titel"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Ny til ACF? Tag et kig på vores <a href=\"%s\" target=\"_blank\">kom godt i "
"gang guide</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:252
msgid "Options Pages"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/global/navigation.php:212
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr ""

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:85
msgid "Add Field"
msgstr ""

#: includes/acf-field-group-functions.php:496 includes/fields.php:391
msgid "Presentation"
msgstr ""

#: includes/fields.php:390
msgid "Validation"
msgstr ""

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:389
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:69
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr ""

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:468
#: includes/admin/admin-internal-post-type-list.php:494
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:468
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:464
#: includes/admin/admin-internal-post-type-list.php:493
msgid "Activate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:464
msgid "Activate this item"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:88
#: assets/build/js/acf-field-group.js:2841
#: assets/build/js/acf-field-group.js:3349
msgid "Move field group to trash?"
msgstr ""

#: acf.php:490 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:274
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr ""

#: acf.php:548
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:546
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""

#: includes/fields/class-acf-field-user.php:549
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-user.php:540
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:379
msgid "Invalid request."
msgstr ""

#: includes/fields/class-acf-field-select.php:646
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:645
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-post_object.php:629
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-post_object.php:620
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:453
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:160
msgid "Enable Transparency"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:179
msgid "RGBA Array"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:94
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:93
#: includes/fields/class-acf-field-color_picker.php:178
msgid "Hex String"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:274
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr ""

#: includes/fields/class-acf-field-email.php:168
msgid "'%s' is not a valid email address"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Color value"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select default color"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear color"
msgstr ""

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr ""

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr ""

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr ""

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr ""

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr ""

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr ""

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:92
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr ""

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
#: assets/build/js/acf-internal-post-type.js:173
#: assets/build/js/acf-internal-post-type.js:247
msgid "Posts"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:76
msgid "Last updated: %s"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:70
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:42
msgid "Invalid field group parameter(s)."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:48
msgid "Import"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:501
msgid "Sync changes"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr ""

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr ""

#: includes/admin/admin.php:173
msgid "View details"
msgstr ""

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr ""

#: includes/admin/admin.php:171
msgid "Information"
msgstr ""

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr ""

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr ""

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr ""

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr ""

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr ""

#: includes/fields/class-acf-field-user.php:374
msgid "Error loading field."
msgstr ""

#: assets/build/js/acf-input.js:2748 assets/build/js/acf-input.js:2817
#: assets/build/js/acf-input.js:2926 assets/build/js/acf-input.js:3000
msgid "Location not found: %s"
msgstr ""

#: includes/forms/form-user.php:337
msgid "<strong>Error</strong>: %s"
msgstr "<strong>FEJL</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Brugerrolle"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Kommentar"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Indlægsformat"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menu element"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Indlægs status"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menuer"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menu områder"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Indlægstaksonomi"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Indlægsside"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Forside"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Sidetype"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Viser backend"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Viser frontend"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Logget ind"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Nuværende bruger"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Sideskabelon"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registrer"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Tilføj / rediger"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Brugerformular"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Sideforælder"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Superadministrator"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Nuværende brugerrolle"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Standard skabelon"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Indlægsskabelon"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Indlægskategori"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Alle %s formater"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Vedhæftning"

#: includes/validation.php:319
msgid "%s value is required"
msgstr "%s værdi er påkrævet"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Vis dette felt hvis"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:392
msgid "Conditional Logic"
msgstr "Betinget logik"

#: includes/admin/views/acf-field-group/conditional-logic.php:161
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "og"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Lokal JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Klon felt"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Tjek også at alle premium add-ons (%s) er opdateret til den seneste version."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Denne version indeholder en opdatering af din database og kræver en "
"opgradering."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Tak fordi du opdaterede til %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Databaseopgradering påkrævet"

#: includes/admin/post-types/admin-field-group.php:129
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Indstillinger side"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:443
msgid "Gallery"
msgstr "Galleri"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:433
msgid "Flexible Content"
msgstr "Fleksibelt indhold"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:453
msgid "Repeater"
msgstr "Gentagelser"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Tilbage til alle værktøjer"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Vælg</b> elementer for at <b>skjule</b> dem i på redigeringssiden."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Skjul på skærm"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Send trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
#: assets/build/js/acf-internal-post-type.js:180
#: assets/build/js/acf-internal-post-type.js:254
msgid "Tags"
msgstr "Tags"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
#: assets/build/js/acf-internal-post-type.js:183
#: assets/build/js/acf-internal-post-type.js:257
msgid "Categories"
msgstr "Kategorier"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Sideegenskaber"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Forfatter"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Korttitel"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Ændringer"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Kommentarer"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Diskussion"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Uddrag"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Indholdseditor"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Vist i feltgruppe liste"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Feltgrupper med et lavere rækkefølge nr. vises først."

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Rækkefølge nr."

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Under felter"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Under labels"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Instruktions placering"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Label placering"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Side"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (efter indhold)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Høj (efter titel)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Placering"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Integreret (ingen metaboks)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standard (WP Metaboks)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Stil"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Type"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Nøgle"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Sortering"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Luk felt"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "class"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "bredde"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr ""

#: includes/fields/class-acf-field.php:311
msgid "Required"
msgstr "Påkrævet"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instruktioner til forfattere. Bliver vist når data indsendes"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instruktioner"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Felttype"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Enkelt ord, ingen mellemrum. Understregning og bindestreger er tilladt."

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Feltnavn"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Dette er navnet der vil blive vist på REDIGER siden"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Felt label"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Slet"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Slet felt"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Flyt"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Flyt felt til anden gruppe"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplikér felt"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Rediger felt"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Træk for at ændre rækkefølgen"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2374
#: assets/build/js/acf-field-group.js:2796
msgid "Show this field group if"
msgstr "Vis denne feltgruppe hvis"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Ingen tilgængelige opdateringer"

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Database opgradering udført. <a href=\"%s\">Se hvad der er ændret</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Indlæser opgraderings opgaver..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Opdatering fejlede."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Opdatering gennemført"

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Opdaterer data til version %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Det er yderst anbefalet at du tager en backup af din database inden du "
"fortsætter. Er du sikker på at du vil køre opdateringen nu?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Vælg venligst mindst et websted at opgradere."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Databaseopgradering udført. <a href=\"%s\">Tilbage til netværk kontrolpanel</"
"a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Webstedet er opdateret"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Webstedet kræver en databaseopgradering %1$s til %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Websted"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Opgrader websteder"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"De følgende websteder kræver en databaseopgradering. Vælg dem du ønsker at "
"opgradere og klik på %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:176
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Tilføj regelgruppe"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regler"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Kopieret"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Kopier til udklipsholder"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Vælg feltgrupper"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Ingen feltgrupper valgt"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Generér PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Eksporter feltgrupper"

#: includes/admin/tools/class-acf-admin-tool-import.php:174
msgid "Import file empty"
msgstr "Importeret fil er tom"

#: includes/admin/tools/class-acf-admin-tool-import.php:165
msgid "Incorrect file type"
msgstr "Forkert filtype"

#: includes/admin/tools/class-acf-admin-tool-import.php:160
msgid "Error uploading file. Please try again"
msgstr "Fejl ved upload af fil. Prøv venligst igen"

#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Vælg ACF JSON filen du gerne vil importere. Når du klikker import herunder, "
"vil ACF importere feltgrupperne."

#: includes/admin/tools/class-acf-admin-tool-import.php:27
msgid "Import Field Groups"
msgstr "Importer feltgrupper"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Synkroniser"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:885
msgid "Select %s"
msgstr "Vælg %s"

#: includes/admin/admin-internal-post-type-list.php:458
#: includes/admin/admin-internal-post-type-list.php:490
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplikér"

#: includes/admin/admin-internal-post-type-list.php:458
msgid "Duplicate this item"
msgstr "Dupliker dette element"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Understøtter"

#: includes/admin/admin.php:305
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Dokumentation"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Beskrivelse"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:757
msgid "Sync available"
msgstr "Synkronisering tilgængelig"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Feltgruppe synkroniseret."
msgstr[1] "%s feltgrupper synkroniseret."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Feltgruppe duplikeret."
msgstr[1] "%s feltgrupper duplikeret."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktive <span class=\"count\">(%s)</span>"
msgstr[1] "Aktive <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Gennemgå websteder og opdater"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Opgradér database"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Tilpasset felter"

#: includes/admin/post-types/admin-field-group.php:584
msgid "Move Field"
msgstr "Flyt felt"

#: includes/admin/post-types/admin-field-group.php:573
#: includes/admin/post-types/admin-field-group.php:577
msgid "Please select the destination for this field"
msgstr "Vælg venligst destinationen for dette felt"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:535
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Feltet %1$s kan nu findes i %2$s feltgruppen"

#: includes/admin/post-types/admin-field-group.php:532
msgid "Move Complete."
msgstr "Flytning udført."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Aktiv"

#: includes/admin/post-types/admin-field-group.php:246
msgid "Field Keys"
msgstr "Feltnøgler"

#: includes/admin/post-types/admin-field-group.php:150
msgid "Settings"
msgstr "Indstillinger"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Placering"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-input.js:983 assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1528
#: assets/build/js/acf-field-group.js:1844
msgid "copy"
msgstr "Kopier"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:624
#: assets/build/js/acf-field-group.js:779
msgid "(this field)"
msgstr "(dette felt)"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-input.js:918 assets/build/js/acf-input.js:943
#: assets/build/js/acf-input.js:1002 assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Valgt"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1633
#: assets/build/js/acf-field-group.js:1956
msgid "Move Custom Field"
msgstr "Flyt tilpasset Felt"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:650
#: assets/build/js/acf-field-group.js:805
msgid "No toggle fields available"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Feltgruppe titel er påkrævet"

#: includes/admin/post-types/admin-field-group.php:86
#: assets/build/js/acf-field-group.js:1622
#: assets/build/js/acf-field-group.js:1942
msgid "This field cannot be moved until its changes have been saved"
msgstr "Dette felt kan ikke flyttes før ændringerne er blevet gemt"

#: includes/admin/post-types/admin-field-group.php:85
#: assets/build/js/acf-field-group.js:1432
#: assets/build/js/acf-field-group.js:1739
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Strengen \"field_\" må ikke bruges i starten af et felts navn"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Feltgruppe kladde opdateret."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Feltgruppe planlagt til."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Feltgruppe indsendt."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Feltgruppe gemt."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Feltgruppe udgivet."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Feltgruppe slettet."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Feltgruppe opdateret."

#: includes/admin/admin-tools.php:112
#: includes/admin/views/global/navigation.php:248
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Værktøjer"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "er ikke lig med"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "er lig med"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formularer"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:175
#: assets/build/js/acf-internal-post-type.js:249
msgid "Page"
msgstr "Side"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:172
#: assets/build/js/acf-internal-post-type.js:246
msgid "Post"
msgstr "Indlæg"

#: includes/fields.php:335
msgid "Relational"
msgstr ""

#: includes/fields.php:334
msgid "Choice"
msgstr "Valg"

#: includes/fields.php:332
msgid "Basic"
msgstr "Grundlæggende"

#: includes/fields.php:283
msgid "Unknown"
msgstr "Ukendt"

#: includes/fields.php:283
msgid "Field type does not exist"
msgstr "Felttype eksisterer ikke"

#: includes/forms/form-front.php:219
msgid "Spam Detected"
msgstr "Spam opdaget"

#: includes/forms/form-front.php:102
msgid "Post updated"
msgstr "Indlæg opdateret"

#: includes/forms/form-front.php:101
msgid "Update"
msgstr "Opdater"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validér e-mail"

#: includes/fields.php:333 includes/forms/form-front.php:47
msgid "Content"
msgstr "Indhold"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:38
msgid "Title"
msgstr "Titel"

#: includes/assets.php:373 includes/forms/form-comment.php:144
#: assets/build/js/acf-input.js:7395 assets/build/js/acf-input.js:7984
msgid "Edit field group"
msgstr "Rediger feltgruppe"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:1125 assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "Det valgte er mindre end"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:1106 assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "Det valgte er større end"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "Værdien er mindre end"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:1045 assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "Værdien er højere end"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:888 assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "Værdi indeholder"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:862 assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:107
#: assets/build/js/acf-input.js:840 assets/build/js/acf-input.js:1023
#: assets/build/js/acf-input.js:903 assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "Værdien er ikke lige med"

#: includes/admin/post-types/admin-field-group.php:106
#: assets/build/js/acf-input.js:810 assets/build/js/acf-input.js:964
#: assets/build/js/acf-input.js:864 assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "Værdien er lige med"

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-input.js:788 assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "Har ingen værdi"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:758 assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "Har enhver værdi"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
#: assets/build/js/acf.js:1564 assets/build/js/acf.js:1658
msgid "Cancel"
msgstr "Annuller"

#: includes/assets.php:350 assets/build/js/acf.js:1738
#: assets/build/js/acf.js:1855
msgid "Are you sure?"
msgstr "Er du sikker?"

#: includes/assets.php:370 assets/build/js/acf-input.js:9464
#: assets/build/js/acf-input.js:10331
msgid "%d fields require attention"
msgstr "%d felter kræver opmærksomhed"

#: includes/assets.php:369 assets/build/js/acf-input.js:9462
#: assets/build/js/acf-input.js:10327
msgid "1 field requires attention"
msgstr "1 felt kræver opmærksomhed"

#: includes/assets.php:368 includes/validation.php:253
#: includes/validation.php:261 assets/build/js/acf-input.js:9457
#: assets/build/js/acf-input.js:10322
msgid "Validation failed"
msgstr "Validering fejlede"

#: includes/assets.php:367 assets/build/js/acf-input.js:9625
#: assets/build/js/acf-input.js:10510
msgid "Validation successful"
msgstr "Validering lykkedes"

#: includes/media.php:54 assets/build/js/acf-input.js:7223
#: assets/build/js/acf-input.js:7788
msgid "Restricted"
msgstr "Begrænset"

#: includes/media.php:53 assets/build/js/acf-input.js:7038
#: assets/build/js/acf-input.js:7552
msgid "Collapse Details"
msgstr "Skjul detaljer"

#: includes/media.php:52 assets/build/js/acf-input.js:7038
#: assets/build/js/acf-input.js:7549
msgid "Expand Details"
msgstr "Udvid detailer"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51 assets/build/js/acf-input.js:6905
#: assets/build/js/acf-input.js:7397
msgid "Uploaded to this post"
msgstr "Uploadet til dette indlæg"

#: includes/media.php:50 assets/build/js/acf-input.js:6944
#: assets/build/js/acf-input.js:7436
msgctxt "verb"
msgid "Update"
msgstr "Opdater"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Rediger"

#: includes/assets.php:364 assets/build/js/acf-input.js:9234
#: assets/build/js/acf-input.js:10093
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Dine ændringer vil gå tabt, hvis du går væk fra denne side"

#: includes/api/api-helpers.php:2950
msgid "File type must be %s."
msgstr "Filtypen skal være %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:174
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2947 assets/build/js/acf-field-group.js:772
#: assets/build/js/acf-field-group.js:2414
#: assets/build/js/acf-field-group.js:934
#: assets/build/js/acf-field-group.js:2843
msgid "or"
msgstr "eller"

#: includes/api/api-helpers.php:2923
msgid "File size must not exceed %s."
msgstr "Filstørrelsen må ikke overskride %s. "

#: includes/api/api-helpers.php:2919
msgid "File size must be at least %s."
msgstr "Filens størrelse skal være mindst %s."

#: includes/api/api-helpers.php:2906
msgid "Image height must not exceed %dpx."
msgstr "Billedets højde må ikke overskride %dpx."

#: includes/api/api-helpers.php:2902
msgid "Image height must be at least %dpx."
msgstr "Billedets højde skal være mindst %dpx."

#: includes/api/api-helpers.php:2890
msgid "Image width must not exceed %dpx."
msgstr "Billedets bredde må ikke overskride %dpx."

#: includes/api/api-helpers.php:2886
msgid "Image width must be at least %dpx."
msgstr "Billedets bredde skal være mindst %dpx."

#: includes/api/api-helpers.php:1400 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(ingen titel)"

#: includes/api/api-helpers.php:760
msgid "Full Size"
msgstr "Fuld størrelse"

#: includes/api/api-helpers.php:725
msgid "Large"
msgstr "Stor"

#: includes/api/api-helpers.php:724
msgid "Medium"
msgstr "Medium"

#: includes/api/api-helpers.php:723
msgid "Thumbnail"
msgstr "Thumbnail"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf-field-group.js:1077
#: assets/build/js/acf-field-group.js:1261
msgid "(no label)"
msgstr "(intet mærkat)"

#: includes/fields/class-acf-field-textarea.php:137
msgid "Sets the textarea height"
msgstr "Sætter tekstområdets højde"

#: includes/fields/class-acf-field-textarea.php:136
msgid "Rows"
msgstr "Rækker"

#: includes/fields/class-acf-field-textarea.php:23
msgid "Text Area"
msgstr "Tekstområde"

#: includes/fields/class-acf-field-checkbox.php:426
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:388
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:377
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:36
msgid "Add new choice"
msgstr "Tilføj nyt valg"

#: includes/fields/class-acf-field-checkbox.php:161
msgid "Toggle All"
msgstr "Vælg alle"

#: includes/fields/class-acf-field-page_link.php:454
msgid "Allow Archives URLs"
msgstr "Tillad Arkiv URLer"

#: includes/fields/class-acf-field-page_link.php:163
msgid "Archives"
msgstr "Arkiver"

#: includes/fields/class-acf-field-page_link.php:23
msgid "Page Link"
msgstr "Side link"

#: includes/fields/class-acf-field-taxonomy.php:873
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Tilføj"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:843
msgid "Name"
msgstr "Navn"

#: includes/fields/class-acf-field-taxonomy.php:828
msgid "%s added"
msgstr "%s tilføjet"

#: includes/fields/class-acf-field-taxonomy.php:792
msgid "%s already exists"
msgstr "%s findes allerede"

#: includes/fields/class-acf-field-taxonomy.php:780
msgid "User unable to add new %s"
msgstr "Brugeren kan ikke tilføje ny %s"

#: includes/fields/class-acf-field-taxonomy.php:680
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:679
msgid "Term Object"
msgstr "Term Objekt"

#: includes/fields/class-acf-field-taxonomy.php:664
msgid "Load value from posts terms"
msgstr "Indlæs værdi fra indlæggets termer"

#: includes/fields/class-acf-field-taxonomy.php:663
msgid "Load Terms"
msgstr "Indlæs termer"

#: includes/fields/class-acf-field-taxonomy.php:653
msgid "Connect selected terms to the post"
msgstr "Forbind valgte termer til indlæget"

#: includes/fields/class-acf-field-taxonomy.php:652
msgid "Save Terms"
msgstr "Gem termer"

#: includes/fields/class-acf-field-taxonomy.php:642
msgid "Allow new terms to be created whilst editing"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:641
msgid "Create Terms"
msgstr "Opret termer"

#: includes/fields/class-acf-field-taxonomy.php:700
msgid "Radio Buttons"
msgstr "Radioknapper"

#: includes/fields/class-acf-field-taxonomy.php:699
msgid "Single Value"
msgstr "Enkelt værdi"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Multi Select"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:23
#: includes/fields/class-acf-field-taxonomy.php:696
msgid "Checkbox"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multiple Values"
msgstr "Flere værdier"

#: includes/fields/class-acf-field-taxonomy.php:690
msgid "Select the appearance of this field"
msgstr "Vælg udseendet for dette felt"

#: includes/fields/class-acf-field-taxonomy.php:689
msgid "Appearance"
msgstr "Udseende"

#: includes/fields/class-acf-field-taxonomy.php:631
msgid "Select the taxonomy to be displayed"
msgstr "Vælg klassificeringen der vises"

#: includes/fields/class-acf-field-taxonomy.php:595
msgctxt "No Terms"
msgid "No %s"
msgstr "Ingen %s"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be equal to or lower than %d"
msgstr "Værdien skal være mindre end eller lig med %d"

#: includes/fields/class-acf-field-number.php:239
msgid "Value must be equal to or higher than %d"
msgstr "Værdien skal være lig med eller højere end %d"

#: includes/fields/class-acf-field-number.php:227
msgid "Value must be a number"
msgstr "Værdien skal være et tal"

#: includes/fields/class-acf-field-number.php:23
msgid "Number"
msgstr "Nummer"

#: includes/fields/class-acf-field-radio.php:257
msgid "Save 'other' values to the field's choices"
msgstr "Gem 'andre' værdier i feltet valgmuligheder"

#: includes/fields/class-acf-field-radio.php:246
msgid "Add 'other' choice to allow for custom values"
msgstr "Tilføj 'andet' muligheden for at tillade tilpasset værdier"

#: includes/admin/views/global/navigation.php:196
msgid "Other"
msgstr "Andre"

#: includes/fields/class-acf-field-radio.php:23
msgid "Radio Button"
msgstr "Radio-knap"

#: includes/fields/class-acf-field-accordion.php:105
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:94
msgid "Allow this accordion to open without closing others."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:93
msgid "Multi-Expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:83
msgid "Display this accordion as open on page load."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:82
msgid "Open"
msgstr "Åben"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Akkordion"

#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
msgid "Restrict which files can be uploaded"
msgstr "Begræns hvilke filer der kan uploades"

#: includes/fields/class-acf-field-file.php:210
msgid "File ID"
msgstr "Fil ID"

#: includes/fields/class-acf-field-file.php:209
msgid "File URL"
msgstr "Fil URL"

#: includes/fields/class-acf-field-file.php:208
msgid "File Array"
msgstr "Fil array"

#: includes/fields/class-acf-field-file.php:179
msgid "Add File"
msgstr "Tilføj fil"

#: includes/admin/tools/class-acf-admin-tool-import.php:153
#: includes/fields/class-acf-field-file.php:179
msgid "No file selected"
msgstr "Ingen fil valgt"

#: includes/fields/class-acf-field-file.php:143
msgid "File name"
msgstr "Filnavn"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2472 assets/build/js/acf-input.js:2625
msgid "Update File"
msgstr "Opdater fil"

#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2471 assets/build/js/acf-input.js:2624
msgid "Edit File"
msgstr "Rediger fil"

#: includes/admin/tools/class-acf-admin-tool-import.php:57
#: includes/fields/class-acf-field-file.php:57
#: assets/build/js/acf-input.js:2445 assets/build/js/acf-input.js:2597
msgid "Select File"
msgstr "Vælg fil"

#: includes/fields/class-acf-field-file.php:23
msgid "File"
msgstr "Fil"

#: includes/fields/class-acf-field-password.php:23
msgid "Password"
msgstr "Adgangskode"

#: includes/fields/class-acf-field-select.php:371
msgid "Specify the value returned"
msgstr ""

#: includes/fields/class-acf-field-select.php:439
msgid "Use AJAX to lazy load choices?"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:338
#: includes/fields/class-acf-field-select.php:360
msgid "Enter each default value on a new line"
msgstr "Indtast hver standardværdi på en ny linie"

#: includes/fields/class-acf-field-select.php:235 includes/media.php:48
#: assets/build/js/acf-input.js:6803 assets/build/js/acf-input.js:7282
msgctxt "verb"
msgid "Select"
msgstr "Vælg"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Indlæsning fejlede"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Søger&hellip;"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Indlæser flere resultater&hellip;"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Du kan kun vælge %d elementer"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Du kan kun vælge 1 element"

#: includes/fields/class-acf-field-select.php:106
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Fjern venligst %d karakterer"

#: includes/fields/class-acf-field-select.php:105
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Fjern venligst 1 karakter"

#: includes/fields/class-acf-field-select.php:104
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Tilføj venligst %d eller flere karakterer"

#: includes/fields/class-acf-field-select.php:103
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Tilføj venligst 1 eller flere karakterer"

#: includes/fields/class-acf-field-select.php:102
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Ingen match fundet"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d resultater fundet, brug piletasterne op og ned for at navigere."

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Et resultat er tilgængeligt, tryk enter for at vælge det."

#: includes/fields/class-acf-field-select.php:23
#: includes/fields/class-acf-field-taxonomy.php:701
msgctxt "noun"
msgid "Select"
msgstr "Vælg"

#: includes/fields/class-acf-field-user.php:73
msgid "User ID"
msgstr "Bruger ID"

#: includes/fields/class-acf-field-user.php:72
msgid "User Object"
msgstr "Bruger objekt"

#: includes/fields/class-acf-field-user.php:71
msgid "User Array"
msgstr "Bruger array"

#: includes/fields/class-acf-field-user.php:59
msgid "All user roles"
msgstr "Alle brugerroller"

#: includes/fields/class-acf-field-user.php:51
msgid "Filter by Role"
msgstr "Filtrer efter rolle"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Bruger"

#: includes/fields/class-acf-field-separator.php:23
msgid "Separator"
msgstr "Separator"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Select Color"
msgstr "Vælg farve"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:69
#: assets/build/js/acf-internal-post-type.js:72
#: assets/build/js/acf-internal-post-type.js:86
msgid "Default"
msgstr "Standard"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Clear"
msgstr "Ryd"

#: includes/fields/class-acf-field-color_picker.php:23
msgid "Color Picker"
msgstr "Farvevælger"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Vælg"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Udført"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nu"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Tidszone"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekund"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisekund"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekund"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minut"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Time"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Tid"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Vælg tidpunkt"

#: includes/fields/class-acf-field-date_time_picker.php:23
msgid "Date Time Picker"
msgstr "Datovælger"

#: includes/fields/class-acf-field-accordion.php:104
msgid "Endpoint"
msgstr "Endpoint"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:108
msgid "Left aligned"
msgstr "Venstrejusteret"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:107
msgid "Top aligned"
msgstr ""

#: includes/fields/class-acf-field-tab.php:103
msgid "Placement"
msgstr "Placering"

#: includes/fields/class-acf-field-tab.php:24
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Værdien skal være en valid URL"

#: includes/fields/class-acf-field-link.php:155
msgid "Link URL"
msgstr "Link URL"

#: includes/fields/class-acf-field-link.php:154
msgid "Link Array"
msgstr "Link array"

#: includes/fields/class-acf-field-link.php:126
msgid "Opens in a new window/tab"
msgstr "Åbner i et nyt vindue/faneblad"

#: includes/fields/class-acf-field-link.php:121
msgid "Select Link"
msgstr "Vælg link"

#: includes/fields/class-acf-field-link.php:23
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:23
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-range.php:209
msgid "Step Size"
msgstr ""

#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-range.php:187
msgid "Maximum Value"
msgstr "Maksimum værdi"

#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-range.php:176
msgid "Minimum Value"
msgstr "Minimum værdi"

#: includes/fields/class-acf-field-range.php:23
msgid "Range"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:166
#: includes/fields/class-acf-field-checkbox.php:355
#: includes/fields/class-acf-field-radio.php:213
#: includes/fields/class-acf-field-select.php:378
msgid "Both (Array)"
msgstr "Begge (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:354
#: includes/fields/class-acf-field-radio.php:212
#: includes/fields/class-acf-field-select.php:377
msgid "Label"
msgstr "Etiket"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:353
#: includes/fields/class-acf-field-radio.php:211
#: includes/fields/class-acf-field-select.php:376
msgid "Value"
msgstr "Værdi"

#: includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:285
msgid "Vertical"
msgstr "Vertikal"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:417
#: includes/fields/class-acf-field-radio.php:286
msgid "Horizontal"
msgstr "Horisontal"

#: includes/fields/class-acf-field-button-group.php:139
#: includes/fields/class-acf-field-checkbox.php:328
#: includes/fields/class-acf-field-radio.php:186
#: includes/fields/class-acf-field-select.php:349
msgid "red : Red"
msgstr "rød : Rød"

#: includes/fields/class-acf-field-button-group.php:139
#: includes/fields/class-acf-field-checkbox.php:328
#: includes/fields/class-acf-field-radio.php:186
#: includes/fields/class-acf-field-select.php:349
msgid "For more control, you may specify both a value and label like this:"
msgstr "For mere kontrol, kan du specificere både værdi og label, sådan:"

#: includes/fields/class-acf-field-button-group.php:139
#: includes/fields/class-acf-field-checkbox.php:328
#: includes/fields/class-acf-field-radio.php:186
#: includes/fields/class-acf-field-select.php:349
msgid "Enter each choice on a new line."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:327
#: includes/fields/class-acf-field-radio.php:185
#: includes/fields/class-acf-field-select.php:348
msgid "Choices"
msgstr "Valg"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Knappe gruppe"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-page_link.php:486
#: includes/fields/class-acf-field-post_object.php:408
#: includes/fields/class-acf-field-radio.php:231
#: includes/fields/class-acf-field-select.php:407
#: includes/fields/class-acf-field-taxonomy.php:710
#: includes/fields/class-acf-field-user.php:103
msgid "Allow Null"
msgstr "Tillad null"

#: includes/fields/class-acf-field-page_link.php:236
#: includes/fields/class-acf-field-post_object.php:230
#: includes/fields/class-acf-field-taxonomy.php:861
msgid "Parent"
msgstr "Forælder"

#: includes/fields/class-acf-field-wysiwyg.php:371
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:370
msgid "Delay Initialization"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Show Media Upload Buttons"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:343
msgid "Toolbar"
msgstr "Værktøjslinje"

#: includes/fields/class-acf-field-wysiwyg.php:335
msgid "Text Only"
msgstr "Kun tekst"

#: includes/fields/class-acf-field-wysiwyg.php:334
msgid "Visual Only"
msgstr "Kun visuelt"

#: includes/fields/class-acf-field-wysiwyg.php:333
msgid "Visual & Text"
msgstr "Visuelt & tekst"

#: includes/fields/class-acf-field-wysiwyg.php:328
msgid "Tabs"
msgstr "Tabs"

#: includes/fields/class-acf-field-wysiwyg.php:272
msgid "Click to initialize TinyMCE"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:266
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-wysiwyg.php:265
msgid "Visual"
msgstr "Visuel"

#: includes/fields/class-acf-field-text.php:183
#: includes/fields/class-acf-field-textarea.php:220
msgid "Value must not exceed %d characters"
msgstr "Værdi må ikke overskride %d karakterer"

#: includes/fields/class-acf-field-text.php:118
#: includes/fields/class-acf-field-textarea.php:116
msgid "Leave blank for no limit"
msgstr ""

#: includes/fields/class-acf-field-text.php:117
#: includes/fields/class-acf-field-textarea.php:115
msgid "Character Limit"
msgstr "Karakterbegrænsning"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:197
#: includes/fields/class-acf-field-password.php:97
#: includes/fields/class-acf-field-range.php:231
#: includes/fields/class-acf-field-text.php:158
msgid "Appears after the input"
msgstr "Vises efter feltet"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:96
#: includes/fields/class-acf-field-range.php:230
#: includes/fields/class-acf-field-text.php:157
msgid "Append"
msgstr "Tilføj før"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:187
#: includes/fields/class-acf-field-password.php:87
#: includes/fields/class-acf-field-range.php:221
#: includes/fields/class-acf-field-text.php:148
msgid "Appears before the input"
msgstr "Vises før feltet"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:186
#: includes/fields/class-acf-field-password.php:86
#: includes/fields/class-acf-field-range.php:220
#: includes/fields/class-acf-field-text.php:147
msgid "Prepend"
msgstr "Tilføj efter"

#: includes/fields/class-acf-field-email.php:126
#: includes/fields/class-acf-field-number.php:167
#: includes/fields/class-acf-field-password.php:77
#: includes/fields/class-acf-field-text.php:138
#: includes/fields/class-acf-field-textarea.php:148
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Vises i feltet"

#: includes/fields/class-acf-field-email.php:125
#: includes/fields/class-acf-field-number.php:166
#: includes/fields/class-acf-field-password.php:76
#: includes/fields/class-acf-field-text.php:137
#: includes/fields/class-acf-field-textarea.php:147
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-email.php:106
#: includes/fields/class-acf-field-number.php:117
#: includes/fields/class-acf-field-radio.php:196
#: includes/fields/class-acf-field-range.php:157
#: includes/fields/class-acf-field-text.php:98
#: includes/fields/class-acf-field-textarea.php:96
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:296
msgid "Appears when creating a new post"
msgstr "Vises når et nyt indlæg oprettes"

#: includes/fields/class-acf-field-text.php:23
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-relationship.php:735
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s kræver mindst %2$s valg"
msgstr[1] "%1$s kræver mindst %2$s valg"

#: includes/fields/class-acf-field-post_object.php:378
#: includes/fields/class-acf-field-relationship.php:596
msgid "Post ID"
msgstr "Indlægs ID"

#: includes/fields/class-acf-field-post_object.php:17
#: includes/fields/class-acf-field-post_object.php:377
#: includes/fields/class-acf-field-relationship.php:595
msgid "Post Object"
msgstr "Indlægs objekt"

#: includes/fields/class-acf-field-relationship.php:628
msgid "Maximum Posts"
msgstr "Maksimum antal indlæg"

#: includes/fields/class-acf-field-relationship.php:618
msgid "Minimum Posts"
msgstr "Minimum antal indlæg"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:653
msgid "Featured Image"
msgstr "Fremhævet billede"

#: includes/fields/class-acf-field-relationship.php:649
msgid "Selected elements will be displayed in each result"
msgstr "Valgte elementer vil blive vist i hvert resultat"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Elements"
msgstr "Elementer"

#: includes/fields/class-acf-field-relationship.php:582
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:630
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Klassificering"

#: includes/fields/class-acf-field-relationship.php:581
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Indholdstype"

#: includes/fields/class-acf-field-relationship.php:575
msgid "Filters"
msgstr "Filtre"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:365
#: includes/fields/class-acf-field-relationship.php:568
msgid "All taxonomies"
msgstr "Alle klassificeringer"

#: includes/fields/class-acf-field-page_link.php:439
#: includes/fields/class-acf-field-post_object.php:357
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Taxonomy"
msgstr "Filtrer efter klassificeringer"

#: includes/fields/class-acf-field-page_link.php:417
#: includes/fields/class-acf-field-post_object.php:335
#: includes/fields/class-acf-field-relationship.php:538
msgid "All post types"
msgstr "Alle indholdstyper"

#: includes/fields/class-acf-field-page_link.php:409
#: includes/fields/class-acf-field-post_object.php:327
#: includes/fields/class-acf-field-relationship.php:530
msgid "Filter by Post Type"
msgstr "Filtrer efter indholdstype"

#: includes/fields/class-acf-field-relationship.php:430
msgid "Search..."
msgstr "Søg..."

#: includes/fields/class-acf-field-relationship.php:361
msgid "Select taxonomy"
msgstr "Vælg klassificering"

#: includes/fields/class-acf-field-relationship.php:353
msgid "Select post type"
msgstr "Vælg indholdstype"

#: includes/fields/class-acf-field-relationship.php:58
#: assets/build/js/acf-input.js:3928 assets/build/js/acf-input.js:4214
msgid "No matches found"
msgstr "Ingen match fundet"

#: includes/fields/class-acf-field-relationship.php:57
#: assets/build/js/acf-input.js:3911 assets/build/js/acf-input.js:4193
msgid "Loading"
msgstr "Indlæser"

#: includes/fields/class-acf-field-relationship.php:56
#: assets/build/js/acf-input.js:3816 assets/build/js/acf-input.js:4084
msgid "Maximum values reached ( {max} values )"
msgstr "Maksimalt antal værdier nået ( {max} værdier )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relation"

#: includes/fields/class-acf-field-file.php:280
#: includes/fields/class-acf-field-image.php:310
msgid "Comma separated list. Leave blank for all types"
msgstr "Kommasepareret liste. Efterlad blank hvis alle typer tillades"

#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:309
msgid "Allowed File Types"
msgstr "Tilladte filtyper"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:273
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:147
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-file.php:271
#: includes/fields/class-acf-field-image.php:264
#: includes/fields/class-acf-field-image.php:300
msgid "File size"
msgstr "Filstørrelse"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
msgid "Restrict which images can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:237
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:203
msgid "Uploaded to post"
msgstr "Uploadet til indlæg"

#: includes/fields/class-acf-field-file.php:224
#: includes/fields/class-acf-field-image.php:202
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Alle"

#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:197
msgid "Limit the media library choice"
msgstr ""

#: includes/fields/class-acf-field-file.php:218
#: includes/fields/class-acf-field-image.php:196
msgid "Library"
msgstr "Bibliotek"

#: includes/fields/class-acf-field-image.php:329
msgid "Preview Size"
msgstr "Størrelse på forhåndsvisning"

#: includes/fields/class-acf-field-image.php:188
msgid "Image ID"
msgstr "Billede ID"

#: includes/fields/class-acf-field-image.php:187
msgid "Image URL"
msgstr "Billede URL"

#: includes/fields/class-acf-field-image.php:186
msgid "Image Array"
msgstr "Billede array"

#: includes/fields/class-acf-field-button-group.php:159
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-file.php:203
#: includes/fields/class-acf-field-link.php:149
#: includes/fields/class-acf-field-radio.php:206
msgid "Specify the returned value on front end"
msgstr "Specificerer værdien der returneres til frontenden"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-file.php:202
#: includes/fields/class-acf-field-link.php:148
#: includes/fields/class-acf-field-radio.php:205
#: includes/fields/class-acf-field-taxonomy.php:674
msgid "Return Value"
msgstr "Returneret værdi"

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Tilføj billede"

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Intet billede valgt"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:155
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:126 assets/build/js/acf.js:1563
#: assets/build/js/acf.js:1657
msgid "Remove"
msgstr "Fjern"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:153
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:126
msgid "Edit"
msgstr "Rediger"

#: includes/fields/class-acf-field-image.php:65 includes/media.php:55
#: assets/build/js/acf-input.js:6850 assets/build/js/acf-input.js:7336
msgid "All images"
msgstr "Alle billeder"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3179 assets/build/js/acf-input.js:3399
msgid "Update Image"
msgstr "Opdater billede"

#: includes/fields/class-acf-field-image.php:63
#: assets/build/js/acf-input.js:3178 assets/build/js/acf-input.js:3398
msgid "Edit Image"
msgstr "Rediger billede"

#: includes/fields/class-acf-field-image.php:62
#: assets/build/js/acf-input.js:3154 assets/build/js/acf-input.js:3373
msgid "Select Image"
msgstr "Vælg billede"

#: includes/fields/class-acf-field-image.php:23
msgid "Image"
msgstr "Billede"

#: includes/fields/class-acf-field-message.php:112
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Tillad at HTML kode bliver vist som tekst i stedet for at blive renderet"

#: includes/fields/class-acf-field-message.php:111
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:164
msgid "No Formatting"
msgstr "Ingen formatering"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:163
msgid "Automatically add &lt;br&gt;"
msgstr "Tilføj automatisk &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-textarea.php:162
msgid "Automatically add paragraphs"
msgstr "Tilføj automatisk afsnit"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:158
msgid "Controls how new lines are rendered"
msgstr "Kontroller hvordan linjeskift vises"

#: includes/fields/class-acf-field-message.php:96
#: includes/fields/class-acf-field-textarea.php:157
msgid "New Lines"
msgstr "Linjeskift"

#: includes/fields/class-acf-field-date_picker.php:224
#: includes/fields/class-acf-field-date_time_picker.php:211
msgid "Week Starts On"
msgstr "Ugen starter"

#: includes/fields/class-acf-field-date_picker.php:193
msgid "The format used when saving a value"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:192
msgid "Save Format"
msgstr "Gem format"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Uge"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Forrige"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Næste"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Udført"

#: includes/fields/class-acf-field-date_picker.php:23
msgid "Date Picker"
msgstr "Datovælger"

#: includes/fields/class-acf-field-image.php:241
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:255
msgid "Width"
msgstr "Bredde"

#: includes/fields/class-acf-field-oembed.php:252
#: includes/fields/class-acf-field-oembed.php:264
msgid "Embed Size"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:212
msgid "Enter URL"
msgstr "Indtast URL"

#: includes/fields/class-acf-field-oembed.php:23
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:174
msgid "Text shown when inactive"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:173
msgid "Off Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:158
msgid "Text shown when active"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:157
msgid "On Text"
msgstr ""

#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-true_false.php:189
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:337
#: includes/fields/class-acf-field-color_picker.php:148
#: includes/fields/class-acf-field-email.php:105
#: includes/fields/class-acf-field-number.php:116
#: includes/fields/class-acf-field-radio.php:195
#: includes/fields/class-acf-field-range.php:156
#: includes/fields/class-acf-field-select.php:359
#: includes/fields/class-acf-field-text.php:97
#: includes/fields/class-acf-field-textarea.php:95
#: includes/fields/class-acf-field-true_false.php:137
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:295
msgid "Default Value"
msgstr "Standardværdi"

#: includes/fields/class-acf-field-true_false.php:128
msgid "Displays text alongside the checkbox"
msgstr ""

#: includes/fields/class-acf-field-message.php:24
#: includes/fields/class-acf-field-message.php:86
#: includes/fields/class-acf-field-true_false.php:127
msgid "Message"
msgstr "Besked"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:81
#: includes/fields/class-acf-field-true_false.php:177
#: assets/build/js/acf.js:1740 assets/build/js/acf.js:1857
msgid "No"
msgstr "Nej"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:78
#: includes/fields/class-acf-field-true_false.php:161
#: assets/build/js/acf.js:1739 assets/build/js/acf.js:1856
msgid "Yes"
msgstr "Ja"

#: includes/fields/class-acf-field-true_false.php:23
msgid "True / False"
msgstr "Sand / Falsk"

#: includes/fields/class-acf-field-group.php:421
msgid "Row"
msgstr "Række"

#: includes/fields/class-acf-field-group.php:420
msgid "Table"
msgstr "Tabel"

#: includes/admin/post-types/admin-field-group.php:128
#: includes/fields/class-acf-field-group.php:419
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:414
msgid "Specify the style used to render the selected fields"
msgstr ""

#: includes/fields.php:337 includes/fields/class-acf-field-button-group.php:205
#: includes/fields/class-acf-field-checkbox.php:410
#: includes/fields/class-acf-field-group.php:413
#: includes/fields/class-acf-field-radio.php:279
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:397
msgid "Sub Fields"
msgstr "Underfelter"

#: includes/fields/class-acf-field-group.php:23
msgid "Group"
msgstr "Gruppe"

#: includes/fields/class-acf-field-google-map.php:226
msgid "Customize the map height"
msgstr "Tilpas kortets højde"

#: includes/fields/class-acf-field-google-map.php:225
#: includes/fields/class-acf-field-image.php:252
#: includes/fields/class-acf-field-image.php:288
#: includes/fields/class-acf-field-oembed.php:267
msgid "Height"
msgstr "Højde"

#: includes/fields/class-acf-field-google-map.php:214
msgid "Set the initial zoom level"
msgstr "Sæt standard zoom niveau"

#: includes/fields/class-acf-field-google-map.php:213
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:187
#: includes/fields/class-acf-field-google-map.php:200
msgid "Center the initial map"
msgstr "Kortets centrum fra start"

#: includes/fields/class-acf-field-google-map.php:186
#: includes/fields/class-acf-field-google-map.php:199
msgid "Center"
msgstr "Centrum"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Search for address..."
msgstr "Søg efter adresse..."

#: includes/fields/class-acf-field-google-map.php:154
msgid "Find current location"
msgstr "Find nuværende lokation"

#: includes/fields/class-acf-field-google-map.php:153
msgid "Clear location"
msgstr "Ryd lokation"

#: includes/fields/class-acf-field-google-map.php:152
#: includes/fields/class-acf-field-relationship.php:580
msgid "Search"
msgstr "Søg"

#: includes/fields/class-acf-field-google-map.php:59
#: assets/build/js/acf-input.js:2838 assets/build/js/acf-input.js:3026
msgid "Sorry, this browser does not support geolocation"
msgstr "Beklager, denne browser understøtter ikke geolokation"

#: includes/fields/class-acf-field-google-map.php:23
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:204
#: includes/fields/class-acf-field-date_time_picker.php:192
#: includes/fields/class-acf-field-time_picker.php:124
msgid "The format returned via template functions"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:172
#: includes/fields/class-acf-field-date_picker.php:203
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-image.php:180
#: includes/fields/class-acf-field-post_object.php:372
#: includes/fields/class-acf-field-relationship.php:590
#: includes/fields/class-acf-field-select.php:370
#: includes/fields/class-acf-field-time_picker.php:123
#: includes/fields/class-acf-field-user.php:66
msgid "Return Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:182
#: includes/fields/class-acf-field-date_picker.php:213
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:115
#: includes/fields/class-acf-field-time_picker.php:131
msgid "Custom:"
msgstr "Tilpasset:"

#: includes/fields/class-acf-field-date_picker.php:174
#: includes/fields/class-acf-field-date_time_picker.php:174
#: includes/fields/class-acf-field-time_picker.php:108
msgid "The format displayed when editing a post"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:173
#: includes/fields/class-acf-field-date_time_picker.php:173
#: includes/fields/class-acf-field-time_picker.php:107
msgid "Display Format"
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:23
msgid "Time Picker"
msgstr ""

#. translators: counts for inactive field groups
#: acf.php:496
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inaktivt <span class=\"count\">(%s)</span>"
msgstr[1] "Inaktive <span class=\"count\">(%s)</span>"

#: acf.php:457
msgid "No Fields found in Trash"
msgstr "Ingen felter fundet i papirkurven."

#: acf.php:456
msgid "No Fields found"
msgstr "Ingen felter fundet"

#: acf.php:455
msgid "Search Fields"
msgstr "Søge felter"

#: acf.php:454
msgid "View Field"
msgstr "Vis felt"

#: acf.php:453 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nyt felt"

#: acf.php:452
msgid "Edit Field"
msgstr "Rediger felt"

#: acf.php:451
msgid "Add New Field"
msgstr "Tilføj nyt felt"

#: acf.php:449
msgid "Field"
msgstr "Felt"

#: acf.php:448 includes/admin/post-types/admin-field-group.php:149
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Felter"

#: acf.php:423
msgid "No Field Groups found in Trash"
msgstr "Ingen gruppefelter fundet i papirkurven"

#: acf.php:422
msgid "No Field Groups found"
msgstr "Ingen gruppefelter fundet"

#: acf.php:421
msgid "Search Field Groups"
msgstr "Søg feltgrupper"

#: acf.php:420
msgid "View Field Group"
msgstr "Vis feltgruppe"

#: acf.php:419
msgid "New Field Group"
msgstr "Ny feltgruppe"

#: acf.php:418
msgid "Edit Field Group"
msgstr "Rediger feltgruppe"

#: acf.php:417
msgid "Add New Field Group"
msgstr "Tilføj ny feltgruppe"

#: acf.php:416 acf.php:450
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Tilføj ny"

#: acf.php:415
msgid "Field Group"
msgstr "Feltgruppe"

#: acf.php:414 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Feltgrupper"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Tilpas WordPress med effektfulde, professionelle og intuitive felter."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr ""
