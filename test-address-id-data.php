<?php
/**
 * Test script to verify address ID data is being stored correctly
 * This can be run as a WordPress page or standalone script
 */

// If running in WordPress
if (function_exists('get_users')) {
    echo "<h1>Address ID Data Test</h1>";
    
    // Test 1: Check WordPress user metadata
    echo "<h2>Test 1: WordPress User Metadata</h2>";
    
    $users = get_users([
        'meta_key' => '_wcmca_additional_addresses',
        'number' => 5
    ]);
    
    if (empty($users)) {
        echo "<p>❌ No users found with WCMCA addresses</p>";
    } else {
        foreach ($users as $user) {
            echo "<h3>User: {$user->display_name} (ID: {$user->ID})</h3>";
            
            $addresses = get_user_meta($user->ID, '_wcmca_additional_addresses', true);
            
            if (!is_array($addresses)) {
                echo "<p>❌ No address data found</p>";
                continue;
            }
            
            echo "<p>✅ Found " . count($addresses) . " addresses</p>";
            
            foreach ($addresses as $index => $address) {
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
                echo "<h4>Address #{$index}</h4>";
                
                if (isset($address['address_id'])) {
                    echo "<p><strong>✅ address_id:</strong> " . $address['address_id'] . " (" . gettype($address['address_id']) . ")</p>";
                } else {
                    echo "<p><strong>❌ address_id:</strong> NOT FOUND</p>";
                }
                
                if (isset($address['address_internal_name'])) {
                    echo "<p><strong>Name:</strong> " . $address['address_internal_name'] . "</p>";
                }
                
                if (isset($address['type'])) {
                    echo "<p><strong>Type:</strong> " . $address['type'] . "</p>";
                }
                
                echo "<p><strong>All keys:</strong> " . implode(', ', array_keys($address)) . "</p>";
                echo "</div>";
            }
        }
    }
    
    // Test 2: Check SAP database table
    echo "<h2>Test 2: SAP Database Table</h2>";
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'sap_shipto_addresses';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
        echo "<p>❌ SAP ShipTo table does not exist: {$table_name}</p>";
    } else {
        $records = $wpdb->get_results("SELECT customer_id, wcmca_addresses_data FROM {$table_name} LIMIT 5");
        
        if (empty($records)) {
            echo "<p>❌ No records found in SAP table</p>";
        } else {
            echo "<p>✅ Found " . count($records) . " SAP records</p>";
            
            foreach ($records as $record) {
                echo "<h3>SAP Customer: {$record->customer_id}</h3>";
                
                $addresses = maybe_unserialize($record->wcmca_addresses_data);
                
                if (!is_array($addresses)) {
                    echo "<p>❌ Invalid address data</p>";
                    continue;
                }
                
                foreach ($addresses as $index => $address) {
                    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #f9f9f9;'>";
                    echo "<h4>SAP Address #{$index}</h4>";
                    
                    if (isset($address['address_id'])) {
                        echo "<p><strong>✅ address_id:</strong> " . $address['address_id'] . " (" . gettype($address['address_id']) . ")</p>";
                    } else {
                        echo "<p><strong>❌ address_id:</strong> NOT FOUND</p>";
                    }
                    
                    if (isset($address['address_internal_name'])) {
                        echo "<p><strong>Name:</strong> " . $address['address_internal_name'] . "</p>";
                    }
                    
                    echo "<p><strong>All keys:</strong> " . implode(', ', array_keys($address)) . "</p>";
                    echo "</div>";
                }
            }
        }
    }
    
    // Test 3: Test the wcmca_get_value_if_set function
    echo "<h2>Test 3: Function Test</h2>";
    
    $test_address = [
        'address_id' => '12345',
        'address_internal_name' => 'Test Address',
        'type' => 'shipping'
    ];
    
    echo "<p><strong>Test address array:</strong></p>";
    echo "<pre>" . print_r($test_address, true) . "</pre>";
    
    if (function_exists('wcmca_get_value_if_set')) {
        $result = wcmca_get_value_if_set($test_address, 'address_id', 'DEFAULT');
        echo "<p><strong>wcmca_get_value_if_set result:</strong> '{$result}'</p>";
    } else {
        echo "<p>❌ wcmca_get_value_if_set function not found</p>";
        
        // Manual test
        $manual_result = isset($test_address['address_id']) ? $test_address['address_id'] : 'DEFAULT';
        echo "<p><strong>Manual test result:</strong> '{$manual_result}'</p>";
    }
    
} else {
    echo "<h1>WordPress Not Loaded</h1>";
    echo "<p>This script needs to be run within WordPress context.</p>";
    echo "<p>Copy this file to your WordPress root directory and access it via browser.</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3, h4 { color: #333; }
.success { color: green; }
.error { color: red; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
</style>
