{"version": 3, "file": "acf-pro-input.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;;;;8FAAA;AAMA;AAOA;AAQA;AAgBA;;;;8FAAA;ACrCA;;;;8FAAA;ACAA;;;;+FAAA;AAMA;EAEC;EAUA;EAqEA;EASA;EAiCA;EAcA;EACD;;;;;;;;GAAA;EAgBC;EAWA;EAYA;EAkBA;AH7JD;AGnCC;EACC;EACA;AHqCF;AGnCE;EACC;AHqCH;AGhCC;EACC;EACA;EACA;EACA;EAiBA;EAmBA;EAoBA;AHnBF;AGnCE;EACC;AHqCH;AGlCE;;EAEC;EACA;AHoCH;AGjCE;EACC;EACA;AHmCH;AG/BE;EACC;EACA;EACA;EACA;EAGA;AH+BH;AG9BG;EACC;EAEA;AH+BJ;AG9BI;EAAsB;AHiC1B;AG1BE;EACC;EACA;EACA;EACA;AH4BH;AG1BG;EACC;AH4BJ;AGzBG;EACC;AH2BJ;AGvBE;EACC;AHyBH;AGrBE;EACC;EACA;AHuBH;AGjBC;EACC;EACA;EACA;EACA;AHmBF;AGdC;EAEC;EAMA;AHUF;AGfE;EACC;AHiBH;AGZE;EAGC;AHYH;AGXG;EACC;AHaJ;AGVI;EACC;AHYL;AGXK;EACC;AHaN;AGTK;EACC;AHWN;AGHC;EACC;AHKF;AGFC;EACC;AHIF;AGFE;EACC;AHIH;AGYC;EAEC;AHXF;AGgBC;EACC;AHdF;AGiBC;EAEC;AHhBF;AGuBE;EACC;AHrBH;AGwBE;EACC;AHtBH;AG6BE;EACC;AH3BH;AG8BE;EACC;AH5BH;AG8BG;EACC;EACA;AH5BJ;AGmCC;EACC;AHjCF;AGwCE;EACC;AHtCH;AG2CE;EACC;EACA;AHzCH;AG4CE;EACC;EACA;AH1CH;AG4CG;EACC;AH1CJ;;AGgDA;;;;+FAAA;AAMA;EACC;AH9CD;AGiDC;EACC;AH/CF;AGmDC;EACC;AHjDF;AGoDE;EACC;EACA;EAEA;EACA;AHnDH;AGwDC;EACC;EACA;EACG;EACA;AHtDL;AGwDK;EACF;AHtDH;AG0DE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHxDH;AG4DE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH1DH;AG4DG;EACC;EACA;EACA;AH1DJ;AG+DE;EACC;EACA;EACA;AH7DH;AG+DG;EACC;EACA;EACA;AH7DJ;AG+DI;EAAkC;AH5DtC;AG+DG;EACC;EACA;AH7DJ;AGkEE;EACC,qBFzSe;ADyOlB;AGiEG;EACC,qBF3Sc;AD4OlB;AG2EK;EAAkC;AHxEvC;AG+EG;EACC;AH7EJ;AGgFG;;EAEC;AH9EJ;AGmFE;EACC;EACA;AHjFH;AGmFG;EACC;AHjFJ;AGoFG;EACC;AHlFJ;AGwFC;EACC;EACA;EACA;EACA;AHtFF;AG0FC;EACC;AHxFF;;AG6FA;EACC;EACA;EACA;AH1FD;AG4FC;EACC;EACA;EACA;EACA;AH1FF;AG6FC;EACC;EACA;EACA;AH3FF;AG8FC;EACC;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;AH7FF;AGgGC;EACC;EACA;EACA;EACA;EACA;AH9FF;AGgGE;EACC;EACA;AH9FH;AGiGE;EACC;EACA;AH/FH;;AGsGA;;;;+FAAA;AAMA;EACC;EACA;EACA;EAEA;EAWA;EAaA;EAoJA;EAuBA;EAyBA;EAiEA;EAmDA;EAWA;AHxbD;AG8FC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AH5FF;AGgGC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH9FF;AGmGC;EACC;EACA;EACA;EACA;EAiEA;EAUA;EAuBA;EAUA;EAUA;AHlNF;AG8FE;EACC;EACA;EACA;EACA;EACA;AH5FH;AG8FG;EACC;EACG;EACA;AH5FP;AGgGE;EACC;EACA;EACA;EACA;EACA;EACA;AH9FH;AGgGG;EACC;AH9FJ;AGiGG;EACC;EACA;EACA;EACA;EACA;AH/FJ;AGiGI;EACC;AH/FL;AGoGE;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHlGN;AGqGE;EACC;EACA;EACA;EACA;AHnGH;AG0GG;EACC;AHxGJ;AGiHG;EACC;EACA;AH/GJ;AGqHG;EACC;EACA;AHnHJ;AGqHI;EACC;AHnHL;AG6HG;EACC;AH3HJ;AGoIG;EACC;AHlIJ;AGyIE;EACC;AHvIH;AG8IC;EAEC;EAMA;AHlJF;AG6IE;EACC;AH3IH;AGgJE;EACC;AH9IH;AGqJC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHnJF;AGqJE;EACC;AHnJH;AGsJE;EACC;EACA;AHpJH;AG2JC;EACC;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;AH3JF;AG6JE;EACC;EACA;EACA;EACA;EACA;AH3JH;AGkKC;EAEC;EACA;EACA;EACA;EACA;EACA;AHjKF;AEhgBC;EACC;EACA;EACA;AFkgBF;AG8JE;EACC;EACA;AH5JH;AG+JE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;AH9JH;AG+JG;EACC;EACA;AH7JJ;AGiKE;EACC;EACA;EACA;EACA;EACA;AH/JH;AGiKG;EACC;AH/JJ;AGmKE;EACC;AHjKH;AGmKG;EACC;AHjKJ;AGoKG;EACC;AHlKJ;AGqKG;EACC;AHnKJ;AG4KC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH1KF;AG6KE;;EAEC;EACA;EACA;EACA;EACA;EACA;AH3KH;AG6KG;;EACC;AH1KJ;AG6KG;;EACC;AH1KJ;AG8KE;;EAEC;AH5KH;AG+KE;EACC;EACA;AH7KH;AGgLE;EACC;AH9KH;AGgLG;EACC;AH9KJ;AGsLC;EAA8C;AHnL/C;AGoLC;EAA8C;AHjL/C;AGkLC;EAA8C;AH/K/C;AGgLC;EAA8C;AH7K/C;AG8KC;EAA8C;AH3K/C;AG4KC;EAA8C;AHzK/C;AG0KC;EAA8C;AHvK/C;AGwKC;EAA8C;AHrK/C;AGyKC;EACC;EACA;AHvKF;AG0KC;EACC;EACA;EACA;EACA;EACA;AHxKF;;AG+KA;AACA;EACC;AH5KD;AG8KC;EACC;AH5KF;AG+KC;EACC;AH7KF;AGgLC;EACC;EACA;EACA;AH9KF;;AGqLC;EACC;EACA;EACA;EACA,6CFlvBa;ADgkBf;AGqLC;EACC;AHnLF;AGsLC;EACC;EACA,cFlyBS;AD8mBX;AGsLE;EACC,cF1xBQ;ADsmBX;AGwLC;EAEC;EACA;AHvLF;AG2LC;EACC;AHzLF;;AG8LA;EACC;AH3LD;;AI7qBA;;;;8EAAA;AASC;EACC;AJ4qBF;;AIxqBA;EACC;AJ2qBD;;AIvqBA;EAEC;EAGA;EACA;EACA;EACA;EACA,gIACC;AJsqBF;AI5pBC;EACC;EACA;AJ8pBF;AI5pBE;EACC;EACA;EACA;AJ8pBH;AI1pBC;EACC;AJ4pBF;AIzpBC;EACC;EACA;AJ2pBF;;AInpBC;;EACC;AJupBF;AInpBC;;EACC;EACA;AJspBF;AIppBE;;EACC;EACA;EACA;EACA;EAGA;AJqpBH;AIlpBE;;EACC;AJqpBH;AIhpBC;;EACC;AJmpBF;AIhpBC;;EACC;EACA;EACA;EACA;EACA;AJmpBF;AIjpBE;;EACC;AJopBH;AI/oBC;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJkpBF;AI/oBC;;EACC;AJkpBF;AI/oBC;;EACC;AJkpBF;AI/oBC;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;AJipBF;AI9oBC;;EACC;EACA;EACA;AJipBF;AI7oBC;;EACC;AJgpBF;AI3oBG;;EACC;EACA;AJ8oBJ;AIxoBC;;EACC;AJ2oBF;AIxoBE;;EACC;AJ2oBH;AIzoBG;;EACC;AJ4oBJ;AItoBC;;;;;;EAGC;EACA;EACA;EACA;AJ2oBF;AIzoBE;;;;;;EACC;EACA;AJgpBH;AI7oBE;;;;;;EACC;EACA;AJopBH;AIjpBE;;;;;;EACC;AJwpBH;AInpBC;;EACC;AJspBF;;AI/oBC;EACC;EACA;EACA;AJkpBF;AIhpBE;EACC;AJkpBH;AI9oBE;EACC;AJgpBH;;AIzoBA;EACC;AJ4oBD,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/pro/acf-pro-input.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_variables.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_mixins.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/pro/_fields.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/pro/_blocks.scss"], "sourcesContent": ["@charset \"UTF-8\";\n/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n/* colors */\n/* acf-field */\n/* responsive */\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Repeater\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-repeater {\n  /* table */\n  /* row handle (add/remove) */\n  /* add in spacer to th (force correct width) */\n  /* row */\n  /* sortable */\n  /* layouts */\n  /*\n  \t&.-row > table > tbody > tr:before,\n  \t&.-block > table > tbody > tr:before {\n  \t\tcontent: \"\";\n  \t\tdisplay: table-row;\n  \t\theight: 2px;\n  \t\tbackground: #f00;\n  \t}\n  */\n  /* empty */\n  /* collapsed */\n  /* collapsed (block layout) */\n  /* collapsed (table layout) */\n}\n.acf-repeater > table {\n  margin: 0 0 8px;\n  background: #F9F9F9;\n}\n.acf-repeater > table > tbody tr.acf-divider:not(:first-child) > td {\n  border-top: 10px solid #EAECF0;\n}\n.acf-repeater .acf-row-handle {\n  width: 16px;\n  text-align: center !important;\n  vertical-align: middle !important;\n  position: relative;\n  /* icons */\n  /* .order */\n  /* remove */\n}\n.acf-repeater .acf-row-handle .acf-order-input-wrap {\n  width: 45px;\n}\n.acf-repeater .acf-row-handle .acf-order-input::-webkit-outer-spin-button,\n.acf-repeater .acf-row-handle .acf-order-input::-webkit-inner-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n.acf-repeater .acf-row-handle .acf-order-input {\n  -moz-appearance: textfield;\n  text-align: center;\n}\n.acf-repeater .acf-row-handle .acf-icon {\n  display: none;\n  position: absolute;\n  top: 0;\n  margin: -8px 0 0 -2px;\n  /* minus icon */\n}\n.acf-repeater .acf-row-handle .acf-icon.-minus {\n  top: 50%;\n  /* ie fix */\n}\nbody.browser-msie .acf-repeater .acf-row-handle .acf-icon.-minus {\n  top: 25px;\n}\n.acf-repeater .acf-row-handle.order {\n  background: #f4f4f4;\n  cursor: move;\n  color: #aaa;\n  text-shadow: #fff 0 1px 0;\n}\n.acf-repeater .acf-row-handle.order:hover {\n  color: #666;\n}\n.acf-repeater .acf-row-handle.order + td {\n  border-left-color: #DFDFDF;\n}\n.acf-repeater .acf-row-handle.pagination {\n  cursor: auto;\n}\n.acf-repeater .acf-row-handle.remove {\n  background: #F9F9F9;\n  border-left-color: #DFDFDF;\n}\n.acf-repeater th.acf-row-handle:before {\n  content: \"\";\n  width: 16px;\n  display: block;\n  height: 1px;\n}\n.acf-repeater .acf-row {\n  /* hide clone */\n  /* hover */\n}\n.acf-repeater .acf-row.acf-clone {\n  display: none !important;\n}\n.acf-repeater .acf-row:hover, .acf-repeater .acf-row.-hover {\n  /* icons */\n}\n.acf-repeater .acf-row:hover > .acf-row-handle .acf-icon, .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon {\n  display: block;\n}\n.acf-repeater .acf-row:hover > .acf-row-handle .acf-icon.show-on-shift, .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon.show-on-shift {\n  display: none;\n}\nbody.acf-keydown-shift .acf-repeater .acf-row:hover > .acf-row-handle .acf-icon.show-on-shift, body.acf-keydown-shift .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon.show-on-shift {\n  display: block;\n}\nbody.acf-keydown-shift .acf-repeater .acf-row:hover > .acf-row-handle .acf-icon.hide-on-shift, body.acf-keydown-shift .acf-repeater .acf-row.-hover > .acf-row-handle .acf-icon.hide-on-shift {\n  display: none;\n}\n.acf-repeater > table > tbody > tr.ui-sortable-helper {\n  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);\n}\n.acf-repeater > table > tbody > tr.ui-sortable-placeholder {\n  visibility: visible !important;\n}\n.acf-repeater > table > tbody > tr.ui-sortable-placeholder td {\n  background: #F9F9F9;\n}\n.acf-repeater.-row > table > tbody > tr > td, .acf-repeater.-block > table > tbody > tr > td {\n  border-top-color: #E1E1E1;\n}\n.acf-repeater.-empty > table > thead > tr > th {\n  border-bottom: 0 none;\n}\n.acf-repeater.-empty.-row > table, .acf-repeater.-empty.-block > table {\n  display: none;\n}\n.acf-repeater .acf-row.-collapsed > .acf-field {\n  display: none !important;\n}\n.acf-repeater .acf-row.-collapsed > td.acf-field.-collapsed-target {\n  display: table-cell !important;\n}\n.acf-repeater .acf-row.-collapsed > .acf-fields > * {\n  display: none !important;\n}\n.acf-repeater .acf-row.-collapsed > .acf-fields > .acf-field.-collapsed-target {\n  display: block !important;\n}\n.acf-repeater .acf-row.-collapsed > .acf-fields > .acf-field.-collapsed-target[data-width] {\n  float: none !important;\n  width: auto !important;\n}\n.acf-repeater.-table .acf-row.-collapsed .acf-field.-collapsed-target {\n  border-left-color: #dfdfdf;\n}\n.acf-repeater.-max .acf-icon[data-event=add-row] {\n  display: none !important;\n}\n.acf-repeater > .acf-actions .acf-button {\n  float: right;\n  pointer-events: auto !important;\n}\n.acf-repeater > .acf-actions .acf-tablenav {\n  float: right;\n  margin-right: 20px;\n}\n.acf-repeater > .acf-actions .acf-tablenav .current-page {\n  width: auto !important;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Flexible Content\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-flexible-content {\n  position: relative;\n}\n.acf-flexible-content > .clones {\n  display: none;\n}\n.acf-flexible-content > .values {\n  margin: 0 0 8px;\n}\n.acf-flexible-content > .values > .ui-sortable-placeholder {\n  visibility: visible !important;\n  border: 1px dashed #b4b9be;\n  box-shadow: none;\n  background: transparent;\n}\n.acf-flexible-content .layout {\n  position: relative;\n  margin: 20px 0 0;\n  background: #fff;\n  border: 1px solid #ccd0d4;\n}\n.acf-flexible-content .layout:first-child {\n  margin-top: 0;\n}\n.acf-flexible-content .layout .acf-fc-layout-handle {\n  display: block;\n  position: relative;\n  padding: 8px 10px;\n  cursor: move;\n  border-bottom: #ccd0d4 solid 1px;\n  color: #444;\n  font-size: 14px;\n  line-height: 1.4em;\n}\n.acf-flexible-content .layout .acf-fc-layout-order {\n  display: block;\n  width: 20px;\n  height: 20px;\n  border-radius: 10px;\n  display: inline-block;\n  text-align: center;\n  line-height: 20px;\n  margin: 0 2px 0 0;\n  background: #F1F1F1;\n  font-size: 12px;\n  color: #444;\n}\nhtml[dir=rtl] .acf-flexible-content .layout .acf-fc-layout-order {\n  float: right;\n  margin-right: 0;\n  margin-left: 5px;\n}\n.acf-flexible-content .layout .acf-fc-layout-controls {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n}\n.acf-flexible-content .layout .acf-fc-layout-controls .acf-icon {\n  display: block;\n  float: left;\n  margin: 0 0 0 5px;\n}\n.acf-flexible-content .layout .acf-fc-layout-controls .acf-icon.-plus, .acf-flexible-content .layout .acf-fc-layout-controls .acf-icon.-minus, .acf-flexible-content .layout .acf-fc-layout-controls .acf-icon.-duplicate {\n  visibility: hidden;\n}\nhtml[dir=rtl] .acf-flexible-content .layout .acf-fc-layout-controls {\n  right: auto;\n  left: 9px;\n}\n.acf-flexible-content .layout.is-selected {\n  border-color: #7e8993;\n}\n.acf-flexible-content .layout.is-selected .acf-fc-layout-handle {\n  border-color: #7e8993;\n}\n.acf-flexible-content .layout:hover .acf-fc-layout-controls .acf-icon.-plus, .acf-flexible-content .layout:hover .acf-fc-layout-controls .acf-icon.-minus, .acf-flexible-content .layout:hover .acf-fc-layout-controls .acf-icon.-duplicate, .acf-flexible-content .layout.-hover .acf-fc-layout-controls .acf-icon.-plus, .acf-flexible-content .layout.-hover .acf-fc-layout-controls .acf-icon.-minus, .acf-flexible-content .layout.-hover .acf-fc-layout-controls .acf-icon.-duplicate {\n  visibility: visible;\n}\n.acf-flexible-content .layout.-collapsed > .acf-fc-layout-handle {\n  border-bottom-width: 0;\n}\n.acf-flexible-content .layout.-collapsed > .acf-fields,\n.acf-flexible-content .layout.-collapsed > .acf-table {\n  display: none;\n}\n.acf-flexible-content .layout > .acf-table {\n  border: 0 none;\n  box-shadow: none;\n}\n.acf-flexible-content .layout > .acf-table > tbody > tr {\n  background: #fff;\n}\n.acf-flexible-content .layout > .acf-table > thead > tr > th {\n  background: #F9F9F9;\n}\n.acf-flexible-content .no-value-message {\n  padding: 19px;\n  border: #ccc dashed 2px;\n  text-align: center;\n  display: none;\n}\n.acf-flexible-content.-empty > .no-value-message {\n  display: block;\n}\n\n.acf-fc-popup {\n  padding: 5px 0;\n  z-index: 900001;\n  min-width: 135px;\n}\n.acf-fc-popup ul, .acf-fc-popup li {\n  list-style: none;\n  display: block;\n  margin: 0;\n  padding: 0;\n}\n.acf-fc-popup li {\n  position: relative;\n  float: none;\n  white-space: nowrap;\n}\n.acf-fc-popup .badge {\n  display: inline-block;\n  border-radius: 8px;\n  font-size: 9px;\n  line-height: 15px;\n  padding: 0 5px;\n  background: #d54e21;\n  text-align: center;\n  color: #fff;\n  vertical-align: top;\n  margin: 0 0 0 5px;\n}\n.acf-fc-popup a {\n  color: #eee;\n  padding: 5px 10px;\n  display: block;\n  text-decoration: none;\n  position: relative;\n}\n.acf-fc-popup a:hover {\n  background: #0073aa;\n  color: #fff;\n}\n.acf-fc-popup a.disabled {\n  color: #888;\n  background: transparent;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Galery\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-gallery {\n  border: #ccd0d4 solid 1px;\n  height: 400px;\n  position: relative;\n  /* main */\n  /* attachments */\n  /* attachment */\n  /* toolbar */\n  /* sidebar */\n  /* side info */\n  /* side data */\n  /* column widths */\n  /* resizable */\n}\n.acf-gallery .acf-gallery-main {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background: #fff;\n  z-index: 2;\n}\n.acf-gallery .acf-gallery-attachments {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 48px;\n  left: 0;\n  padding: 5px;\n  overflow: auto;\n  overflow-x: hidden;\n}\n.acf-gallery .acf-gallery-attachment {\n  width: 25%;\n  float: left;\n  cursor: pointer;\n  position: relative;\n  /* hover */\n  /* sortable */\n  /* active */\n  /* icon */\n  /* rtl */\n}\n.acf-gallery .acf-gallery-attachment .margin {\n  margin: 5px;\n  border: #d5d9dd solid 1px;\n  position: relative;\n  overflow: hidden;\n  background: #eee;\n}\n.acf-gallery .acf-gallery-attachment .margin:before {\n  content: \"\";\n  display: block;\n  padding-top: 100%;\n}\n.acf-gallery .acf-gallery-attachment .thumbnail {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  transform: translate(50%, 50%);\n}\nhtml[dir=rtl] .acf-gallery .acf-gallery-attachment .thumbnail {\n  transform: translate(-50%, 50%);\n}\n.acf-gallery .acf-gallery-attachment .thumbnail img {\n  display: block;\n  height: auto;\n  max-height: 100%;\n  width: auto;\n  transform: translate(-50%, -50%);\n}\nhtml[dir=rtl] .acf-gallery .acf-gallery-attachment .thumbnail img {\n  transform: translate(50%, -50%);\n}\n.acf-gallery .acf-gallery-attachment .filename {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 5%;\n  background: #F4F4F4;\n  background: rgba(255, 255, 255, 0.8);\n  border-top: #DFDFDF solid 1px;\n  font-weight: bold;\n  text-align: center;\n  word-wrap: break-word;\n  max-height: 90%;\n  overflow: hidden;\n}\n.acf-gallery .acf-gallery-attachment .actions {\n  position: absolute;\n  top: 0;\n  right: 0;\n  display: none;\n}\n.acf-gallery .acf-gallery-attachment:hover .actions {\n  display: block;\n}\n.acf-gallery .acf-gallery-attachment.ui-sortable-helper .margin {\n  border: none;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n}\n.acf-gallery .acf-gallery-attachment.ui-sortable-placeholder .margin {\n  background: #F1F1F1;\n  border: none;\n}\n.acf-gallery .acf-gallery-attachment.ui-sortable-placeholder .margin * {\n  display: none !important;\n}\n.acf-gallery .acf-gallery-attachment.active .margin {\n  box-shadow: 0 0 0 1px #FFFFFF, 0 0 0 5px #0073aa;\n}\n.acf-gallery .acf-gallery-attachment.-icon .thumbnail img {\n  transform: translate(-50%, -70%);\n}\nhtml[dir=rtl] .acf-gallery .acf-gallery-attachment {\n  float: right;\n}\n.acf-gallery.sidebar-open {\n  /* hide attachment actions when sidebar is open */\n  /* allow sidebar to move over main for small widths (widget edit box) */\n}\n.acf-gallery.sidebar-open .acf-gallery-attachment .actions {\n  display: none;\n}\n.acf-gallery.sidebar-open .acf-gallery-side {\n  z-index: 2;\n}\n.acf-gallery .acf-gallery-toolbar {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: 10px;\n  border-top: #d5d9dd solid 1px;\n  background: #fff;\n  min-height: 28px;\n}\n.acf-gallery .acf-gallery-toolbar .acf-hl li {\n  line-height: 24px;\n}\n.acf-gallery .acf-gallery-toolbar .bulk-actions-select {\n  width: auto;\n  margin: 0 1px 0 0;\n}\n.acf-gallery .acf-gallery-side {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  width: 0;\n  background: #F9F9F9;\n  border-left: #ccd0d4 solid 1px;\n  z-index: 1;\n  overflow: hidden;\n}\n.acf-gallery .acf-gallery-side .acf-gallery-side-inner {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  width: 349px;\n}\n.acf-gallery .acf-gallery-side-info {\n  position: relative;\n  width: 100%;\n  padding: 10px;\n  margin: -10px 0 15px -10px;\n  background: #F1F1F1;\n  border-bottom: #DFDFDF solid 1px;\n}\n.acf-gallery .acf-gallery-side-info:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\nhtml[dir=rtl] .acf-gallery .acf-gallery-side-info {\n  margin-left: 0;\n  margin-right: -10px;\n}\n.acf-gallery .acf-gallery-side-info img {\n  float: left;\n  width: auto;\n  max-width: 65px;\n  max-height: 65px;\n  margin: 0 10px 1px 0;\n  background: #FFFFFF;\n  padding: 3px;\n  border: #ccd0d4 solid 1px;\n  border-radius: 1px;\n  /* rtl */\n}\nhtml[dir=rtl] .acf-gallery .acf-gallery-side-info img {\n  float: right;\n  margin: 0 0 0 10px;\n}\n.acf-gallery .acf-gallery-side-info p {\n  font-size: 13px;\n  line-height: 15px;\n  margin: 3px 0;\n  word-break: break-all;\n  color: #666;\n}\n.acf-gallery .acf-gallery-side-info p strong {\n  color: #000;\n}\n.acf-gallery .acf-gallery-side-info a {\n  text-decoration: none;\n}\n.acf-gallery .acf-gallery-side-info a.acf-gallery-edit {\n  color: #21759b;\n}\n.acf-gallery .acf-gallery-side-info a.acf-gallery-remove {\n  color: #bc0b0b;\n}\n.acf-gallery .acf-gallery-side-info a:hover {\n  text-decoration: underline;\n}\n.acf-gallery .acf-gallery-side-data {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 48px;\n  left: 0;\n  overflow: auto;\n  overflow-x: inherit;\n  padding: 10px;\n}\n.acf-gallery .acf-gallery-side-data .acf-label,\n.acf-gallery .acf-gallery-side-data th.label {\n  color: #666666;\n  font-size: 12px;\n  line-height: 25px;\n  padding: 0 4px 8px 0 !important;\n  width: auto !important;\n  vertical-align: top;\n}\nhtml[dir=rtl] .acf-gallery .acf-gallery-side-data .acf-label,\nhtml[dir=rtl] .acf-gallery .acf-gallery-side-data th.label {\n  padding: 0 0 8px 4px !important;\n}\n.acf-gallery .acf-gallery-side-data .acf-label label,\n.acf-gallery .acf-gallery-side-data th.label label {\n  font-weight: normal;\n}\n.acf-gallery .acf-gallery-side-data .acf-input,\n.acf-gallery .acf-gallery-side-data td.field {\n  padding: 0 0 8px !important;\n}\n.acf-gallery .acf-gallery-side-data textarea {\n  min-height: 0;\n  height: 60px;\n}\n.acf-gallery .acf-gallery-side-data p.help {\n  font-size: 12px;\n}\n.acf-gallery .acf-gallery-side-data p.help:hover {\n  font-weight: normal;\n}\n.acf-gallery[data-columns=\"1\"] .acf-gallery-attachment {\n  width: 100%;\n}\n.acf-gallery[data-columns=\"2\"] .acf-gallery-attachment {\n  width: 50%;\n}\n.acf-gallery[data-columns=\"3\"] .acf-gallery-attachment {\n  width: 33.333%;\n}\n.acf-gallery[data-columns=\"4\"] .acf-gallery-attachment {\n  width: 25%;\n}\n.acf-gallery[data-columns=\"5\"] .acf-gallery-attachment {\n  width: 20%;\n}\n.acf-gallery[data-columns=\"6\"] .acf-gallery-attachment {\n  width: 16.666%;\n}\n.acf-gallery[data-columns=\"7\"] .acf-gallery-attachment {\n  width: 14.285%;\n}\n.acf-gallery[data-columns=\"8\"] .acf-gallery-attachment {\n  width: 12.5%;\n}\n.acf-gallery .ui-resizable-handle {\n  display: block;\n  position: absolute;\n}\n.acf-gallery .ui-resizable-s {\n  bottom: -5px;\n  cursor: ns-resize;\n  height: 7px;\n  left: 0;\n  width: 100%;\n}\n\n/* media modal selected */\n.acf-media-modal .attachment.acf-selected {\n  box-shadow: 0 0 0 3px #fff inset, 0 0 0 7px #0073aa inset !important;\n}\n.acf-media-modal .attachment.acf-selected .check {\n  display: none !important;\n}\n.acf-media-modal .attachment.acf-selected .thumbnail {\n  opacity: 0.25 !important;\n}\n.acf-media-modal .attachment.acf-selected .attachment-preview:before {\n  background: rgba(0, 0, 0, 0.15);\n  z-index: 1;\n  position: relative;\n}\n\n.acf-admin-single-options-page .select2-dropdown {\n  border-color: #6BB5D8 !important;\n  margin-top: -5px;\n  overflow: hidden;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-admin-single-options-page .select2-dropdown.select2-dropdown--above {\n  margin-top: 0;\n}\n.acf-admin-single-options-page .select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #F9FAFB !important;\n  color: #667085;\n}\n.acf-admin-single-options-page .select2-container--default .select2-results__option[aria-selected=true]:hover {\n  color: #399CCB;\n}\n.acf-admin-single-options-page .select2-container--default .select2-results__option--highlighted[aria-selected] {\n  color: #fff !important;\n  background-color: #0783BE !important;\n}\n.acf-admin-single-options-page .select2-dropdown .select2-results__option {\n  margin-bottom: 0;\n}\n\n.acf-create-options-page-popup ~ .select2-container {\n  z-index: 999999999;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tACF Blocks\n*\n*----------------------------------------------------------------------------*/\n.acf-block-component .components-placeholder {\n  margin: 0;\n}\n\n.block-editor .acf-field.acf-error {\n  background-color: rgba(255, 0, 0, 0.05);\n}\n\n.acf-block-component .acf-block-fields {\n  background: #fff;\n  text-align: left;\n  font-size: 13px;\n  line-height: 1.4em;\n  color: #444;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n}\n.acf-block-component .acf-block-fields.acf-empty-block-fields {\n  border: 1px solid #1e1e1e;\n  padding: 12px;\n}\n.components-panel .acf-block-component .acf-block-fields.acf-empty-block-fields {\n  border: none;\n  border-top: 1px solid #ddd;\n  border-bottom: 1px solid #ddd;\n}\nhtml[dir=rtl] .acf-block-component .acf-block-fields {\n  text-align: right;\n}\n.acf-block-component .acf-block-fields p {\n  font-size: 13px;\n  line-height: 1.5;\n}\n\n.acf-block-body .acf-block-fields:has(> .acf-error-message),\n.acf-block-fields:has(> .acf-error-message) .acf-block-fields:has(> .acf-error-message) {\n  border: none !important;\n}\n.acf-block-body .acf-error-message,\n.acf-block-fields:has(> .acf-error-message) .acf-error-message {\n  margin-top: 0;\n  border: none;\n}\n.acf-block-body .acf-error-message .acf-notice-dismiss,\n.acf-block-fields:has(> .acf-error-message) .acf-error-message .acf-notice-dismiss {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  outline: unset;\n}\n.acf-block-body .acf-error-message .acf-icon.-cancel::before,\n.acf-block-fields:has(> .acf-error-message) .acf-error-message .acf-icon.-cancel::before {\n  margin: 0 !important;\n}\n.acf-block-body.acf-block-has-validation-error,\n.acf-block-fields:has(> .acf-error-message).acf-block-has-validation-error {\n  border: 2px solid #d94f4f;\n}\n.acf-block-body .acf-error .acf-input .acf-notice,\n.acf-block-fields:has(> .acf-error-message) .acf-error .acf-input .acf-notice {\n  background: none !important;\n  border: none !important;\n  display: flex !important;\n  align-items: center !important;\n  padding-left: 0;\n}\n.acf-block-body .acf-error .acf-input .acf-notice p,\n.acf-block-fields:has(> .acf-error-message) .acf-error .acf-input .acf-notice p {\n  margin: 0.5em 0 !important;\n}\n.acf-block-body .acf-error .acf-input .acf-notice::before,\n.acf-block-fields:has(> .acf-error-message) .acf-error .acf-input .acf-notice::before {\n  content: \"\";\n  position: relative;\n  top: 0;\n  left: 0;\n  font-size: 20px;\n  background-image: url(../../../images/icons/icon-info-red.svg);\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: 69%;\n  height: 26px !important;\n  width: 26px !important;\n  box-sizing: border-box;\n}\n.acf-block-body .acf-error .acf-label label,\n.acf-block-fields:has(> .acf-error-message) .acf-error .acf-label label {\n  color: #d94f4f;\n}\n.acf-block-body .acf-error .acf-input input,\n.acf-block-fields:has(> .acf-error-message) .acf-error .acf-input input {\n  border-color: #d94f4f;\n}\n.acf-block-body.acf-block-has-validation-error::before,\n.acf-block-fields:has(> .acf-error-message).acf-block-has-validation-error::before {\n  content: \"\";\n  position: absolute;\n  top: -2px;\n  left: -32px;\n  font-size: 20px;\n  background-color: #d94f4f;\n  background-image: url(../../../images/icons/icon-info-white.svg);\n  background-repeat: no-repeat;\n  background-position-x: center;\n  background-position-y: 52%;\n  background-size: 55%;\n  height: 40px;\n  width: 32px;\n  box-sizing: border-box;\n}\n.acf-block-body .acf-block-validation-error,\n.acf-block-fields:has(> .acf-error-message) .acf-block-validation-error {\n  color: #d94f4f;\n  display: flex;\n  align-items: center;\n}\n.acf-block-body .acf-block-fields,\n.acf-block-fields:has(> .acf-error-message) .acf-block-fields {\n  border: #adb2ad solid 1px;\n}\n.acf-block-body .acf-block-fields .acf-tab-wrap .acf-tab-group,\n.acf-block-fields:has(> .acf-error-message) .acf-block-fields .acf-tab-wrap .acf-tab-group {\n  margin-left: 0;\n  padding: 16px 20px 0;\n}\n.acf-block-body .acf-fields > .acf-field,\n.acf-block-fields:has(> .acf-error-message) .acf-fields > .acf-field {\n  padding: 16px 20px;\n}\n.acf-block-body .acf-fields > .acf-field.acf-accordion,\n.acf-block-fields:has(> .acf-error-message) .acf-fields > .acf-field.acf-accordion {\n  border-color: #adb2ad;\n}\n.acf-block-body .acf-fields > .acf-field.acf-accordion .acf-accordion-title,\n.acf-block-fields:has(> .acf-error-message) .acf-fields > .acf-field.acf-accordion .acf-accordion-title {\n  padding: 16px 20px;\n}\n.acf-block-body .acf-button,\n.acf-block-body .acf-link a.button,\n.acf-block-body .acf-add-checkbox,\n.acf-block-fields:has(> .acf-error-message) .acf-button,\n.acf-block-fields:has(> .acf-error-message) .acf-link a.button,\n.acf-block-fields:has(> .acf-error-message) .acf-add-checkbox {\n  color: #2271b1 !important;\n  border-color: #2271b1 !important;\n  background: #f6f7f7 !important;\n  vertical-align: top;\n}\n.acf-block-body .acf-button.button-primary:hover,\n.acf-block-body .acf-link a.button.button-primary:hover,\n.acf-block-body .acf-add-checkbox.button-primary:hover,\n.acf-block-fields:has(> .acf-error-message) .acf-button.button-primary:hover,\n.acf-block-fields:has(> .acf-error-message) .acf-link a.button.button-primary:hover,\n.acf-block-fields:has(> .acf-error-message) .acf-add-checkbox.button-primary:hover {\n  color: white !important;\n  background: #2271b1 !important;\n}\n.acf-block-body .acf-button:focus,\n.acf-block-body .acf-link a.button:focus,\n.acf-block-body .acf-add-checkbox:focus,\n.acf-block-fields:has(> .acf-error-message) .acf-button:focus,\n.acf-block-fields:has(> .acf-error-message) .acf-link a.button:focus,\n.acf-block-fields:has(> .acf-error-message) .acf-add-checkbox:focus {\n  outline: none !important;\n  background: #f6f7f7 !important;\n}\n.acf-block-body .acf-button:hover,\n.acf-block-body .acf-link a.button:hover,\n.acf-block-body .acf-add-checkbox:hover,\n.acf-block-fields:has(> .acf-error-message) .acf-button:hover,\n.acf-block-fields:has(> .acf-error-message) .acf-link a.button:hover,\n.acf-block-fields:has(> .acf-error-message) .acf-add-checkbox:hover {\n  color: #0a4b78 !important;\n}\n.acf-block-body .acf-block-preview,\n.acf-block-fields:has(> .acf-error-message) .acf-block-preview {\n  min-height: 10px;\n}\n\n.acf-block-panel .acf-block-fields {\n  border-top: #ddd solid 1px;\n  border-bottom: #ddd solid 1px;\n  min-height: 1px;\n}\n.acf-block-panel .acf-block-fields:empty {\n  border-top: none;\n}\n.acf-block-panel .acf-block-fields .acf-tab-wrap {\n  background: transparent;\n}\n\n.components-panel__body .acf-block-panel {\n  margin: 16px -16px -16px;\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* colors */\n$acf_blue: #2a9bd9;\n$acf_notice: #2a9bd9;\n$acf_error: #d94f4f;\n$acf_success: #49ad52;\n$acf_warning: #fd8d3b;\n\n/* acf-field */\n$field_padding: 15px 12px;\n$field_padding_x: 12px;\n$field_padding_y: 15px;\n$fp: 15px 12px;\n$fy: 15px;\n$fx: 12px;\n\n/* responsive */\n$md: 880px;\n$sm: 640px;\n\n// Admin.\n$wp-card-border: #ccd0d4;\t\t\t// Card border.\n$wp-card-border-1: #d5d9dd;\t\t  // Card inner border 1: Structural (darker).\n$wp-card-border-2: #eeeeee;\t\t  // Card inner border 2: Fields (lighter).\n$wp-input-border: #7e8993;\t\t   // Input border.\n\n// Admin 3.8\n$wp38-card-border: #E5E5E5;\t\t  // Card border.\n$wp38-card-border-1: #dfdfdf;\t\t// Card inner border 1: Structural (darker).\n$wp38-card-border-2: #eeeeee;\t\t// Card inner border 2: Fields (lighter).\n$wp38-input-border: #dddddd;\t\t // Input border.\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Grays\n$gray-50:  #F9FAFB;\n$gray-100: #F2F4F7;\n$gray-200: #EAECF0;\n$gray-300: #D0D5DD;\n$gray-400: #98A2B3;\n$gray-500: #667085;\n$gray-600: #475467;\n$gray-700: #344054;\n$gray-800: #1D2939;\n$gray-900: #101828;\n\n// Blues\n$blue-50:  #EBF5FA;\n$blue-100: #D8EBF5;\n$blue-200: #A5D2E7;\n$blue-300: #6BB5D8;\n$blue-400: #399CCB;\n$blue-500: #0783BE;\n$blue-600: #066998;\n$blue-700: #044E71;\n$blue-800: #033F5B;\n$blue-900: #032F45;\n\n// Utility\n$color-info:\t#2D69DA;\n$color-success:\t#52AA59;\n$color-warning:\t#F79009;\n$color-danger:\t#D13737;\n\n$color-primary: $blue-500;\n$color-primary-hover: $blue-600;\n$color-secondary: $gray-500;\n$color-secondary-hover: $gray-400;\n\n// Gradients\n$gradient-pro: radial-gradient(141.77% 141.08% at 100.26% 99.25%, #0ECAD4 0%, #7A45E5 100%);\n\n// Border radius\n$radius-sm:\t4px;\n$radius-md: 6px;\n$radius-lg: 8px;\n$radius-xl: 12px;\n\n// Elevations / Box shadows\n$elevation-01: 0px 1px 2px rgba($gray-900, 0.10);\n\n// Input & button focus outline\n$outline: 3px solid $blue-50;\n\n// Link colours\n$link-color: $blue-500;\n\n// Responsive\n$max-width: 1440px;", "/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n@mixin clearfix() {\n\t&:after {\n\t\tdisplay: block;\n\t\tclear: both;\n\t\tcontent: \"\";\n\t}\n}\n\n@mixin border-box() {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n}\n\n@mixin centered() {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n@mixin animate( $properties: 'all' ) {\n\t-webkit-transition: $properties 0.3s ease;  // Safari 3.2+, Chrome\n    -moz-transition: $properties 0.3s ease;  \t// Firefox 4-15\n    -o-transition: $properties 0.3s ease;  \t\t// Opera 10.5–12.00\n    transition: $properties 0.3s ease;  \t\t// Firefox 16+, Opera 12.50+\n}\n\n@mixin rtl() {\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t\t@content;\n\t}\n}\n\n@mixin wp-admin( $version: '3-8' ) {\n\t.acf-admin-#{$version} & {\n\t\t@content;\n\t}\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Repeater\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-repeater {\n\t\n\t/* table */\n\t> table {\n\t\tmargin: 0 0 8px;\n\t\tbackground: #F9F9F9;\n\n\t\t> tbody tr.acf-divider:not(:first-child) > td {\n\t\t\tborder-top: 10px solid $gray-200;\n\t\t}\n\t}\n\t\n\t/* row handle (add/remove) */\n\t.acf-row-handle {\n\t\twidth: 16px;\n\t\ttext-align: center !important;\n\t\tvertical-align: middle !important;\n\t\tposition: relative;\n\n\t\t.acf-order-input-wrap {\n\t\t\twidth: 45px;\n\t\t}\n\n\t\t.acf-order-input::-webkit-outer-spin-button,\n\t\t.acf-order-input::-webkit-inner-spin-button {\n\t\t\t-webkit-appearance: none;\n\t\t\tmargin: 0;\n\t\t}\n\n\t\t.acf-order-input {\n\t\t\t-moz-appearance: textfield;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t/* icons */\n\t\t.acf-icon {\n\t\t\tdisplay: none;\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tmargin: -8px 0 0 -2px;\n\t\t\t\n\t\t\t\n\t\t\t/* minus icon */\n\t\t\t&.-minus {\n\t\t\t\ttop: 50%;\n\t\t\t\t\n\t\t\t\t/* ie fix */\n\t\t\t\tbody.browser-msie & { top: 25px; }\n\t\t\t\t\n\t\t\t}\n\t\t}\n\t\t\n\t\t\n\t\t/* .order */\n\t\t&.order {\n\t\t\tbackground: #f4f4f4;\n\t\t\tcursor: move;\n\t\t\tcolor: #aaa;\n\t\t\ttext-shadow: #fff 0 1px 0;\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tcolor: #666;\n\t\t\t}\n\t\t\t\n\t\t\t+ td {\n\t\t\t\tborder-left-color: #DFDFDF;\n\t\t\t}\n\t\t}\n\n\t\t&.pagination {\n\t\t\tcursor: auto;\n\t\t}\n\t\t\n\t\t/* remove */\n\t\t&.remove {\n\t\t\tbackground: #F9F9F9;\n\t\t\tborder-left-color: #DFDFDF;\n\t\t}\n\t}\n\t\n\t\n\t/* add in spacer to th (force correct width) */\n\tth.acf-row-handle:before {\n\t\tcontent: \"\";\n\t\twidth: 16px;\n\t\tdisplay: block;\n\t\theight: 1px;\n\t}\n\t\n\t\n\t/* row */\n\t.acf-row {\n\t\t\n\t\t/* hide clone */\n\t\t&.acf-clone {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\t\n\t\t\n\t\t/* hover */\n\t\t&:hover,\n\t\t&.-hover {\n\t\t\t\n\t\t\t/* icons */\n\t\t\t> .acf-row-handle .acf-icon {\n\t\t\t\tdisplay: block;\n\n\t\t\t\t// Show \"duplicate\" icon above \"add\" when holding \"shift\" key.\n\t\t\t\t&.show-on-shift {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t\tbody.acf-keydown-shift & {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t&.hide-on-shift {\n\t\t\t\t\tbody.acf-keydown-shift & {\n\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* sortable */\n\t> table > tbody > tr.ui-sortable-helper {\n\t\tbox-shadow: 0 1px 5px rgba(0,0,0,0.2);\n\t}\n\t\n\t> table > tbody > tr.ui-sortable-placeholder {\n\t\tvisibility: visible !important;\n\t\t\n\t\ttd {\n\t\t\tbackground: #F9F9F9;\n\t\t}\n\t}\n\t\n\t\n\t/* layouts */\n/*\n\t&.-row > table > tbody > tr:before,\n\t&.-block > table > tbody > tr:before {\n\t\tcontent: \"\";\n\t\tdisplay: table-row;\n\t\theight: 2px;\n\t\tbackground: #f00;\n\t}\n*/\n\t\n\t&.-row > table > tbody > tr > td,\n\t&.-block > table > tbody > tr > td {\n\t\tborder-top-color: #E1E1E1;\n\t}\n\t\n\t\n\t/* empty */\n\t&.-empty > table > thead > tr > th {\n\t\tborder-bottom: 0 none;\n\t}\n\t\n\t&.-empty.-row > table,\n\t&.-empty.-block > table {\n\t\tdisplay: none;\n\t}\n\t\n\t\n\t/* collapsed */\n\t.acf-row.-collapsed {\n\t\t\n\t\t> .acf-field {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\t\n\t\t> td.acf-field.-collapsed-target {\n\t\t\tdisplay: table-cell !important;\n\t\t}\n\t}\n\t\n\t/* collapsed (block layout) */\n\t.acf-row.-collapsed > .acf-fields {\n\t\t\n\t\t> * {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\t\n\t\t> .acf-field.-collapsed-target {\n\t\t\tdisplay: block !important;\n\t\t\t\n\t\t\t&[data-width] {\n\t\t\t\tfloat: none !important;\n\t\t\t\twidth: auto !important;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t\n\t/* collapsed (table layout) */\n\t&.-table .acf-row.-collapsed .acf-field.-collapsed-target {\n\t\tborder-left-color: #dfdfdf;\n\t}\n\t\n\t// Reached maximum rows.\n\t&.-max {\n\t\t\n\t\t// Hide icons to add rows.\n\t\t.acf-icon[data-event=\"add-row\"] {\n\t\t\tdisplay: none !important;\n\t\t}\n\t}\n\n\t> .acf-actions {\n\t\t.acf-button {\n\t\t\tfloat: right;\n\t\t\tpointer-events: auto !important;\n\t\t}\n\n\t\t.acf-tablenav {\n\t\t\tfloat: right;\n\t\t\tmargin-right: 20px;\n\n\t\t\t.current-page {\n\t\t\t\twidth: auto !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Flexible Content\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-flexible-content {\n\tposition: relative;\n\t\n\t// clones\n\t> .clones {\n\t\tdisplay: none;\n\t}\n\t\n\t// values\n\t> .values {\n\t\tmargin: 0 0 8px;\n\t\t\n\t\t// sortable\n\t\t> .ui-sortable-placeholder {\n\t\t\tvisibility: visible !important;\n\t\t\tborder: 1px dashed #b4b9be;\n\t\t\n\t\t\tbox-shadow: none;\n\t\t\tbackground: transparent;\n\t\t}\n\t}\n\t\n\t// layout\n\t.layout {\n\t\tposition: relative;\n\t\tmargin: 20px 0 0;\n\t    background: #fff;\n\t    border: 1px solid $wp-card-border;\n\t\t\n\t    &:first-child {\n\t\t\tmargin-top: 0;\n\t\t}\n\t\t\t\n\t\t// handle\n\t\t.acf-fc-layout-handle {\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\t\t\tpadding: 8px 10px;\n\t\t\tcursor: move;\n\t\t\tborder-bottom: $wp-card-border solid 1px;\n\t\t\tcolor: #444;\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 1.4em;\n\t\t}\n\t\t\n\t\t// order\n\t\t.acf-fc-layout-order {\n\t\t\tdisplay: block;\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tborder-radius: 10px;\n\t\t\tdisplay: inline-block;\n\t\t\ttext-align: center;\n\t\t\tline-height: 20px;\n\t\t\tmargin: 0 2px 0 0;\n\t\t\tbackground: #F1F1F1;\n\t\t\tfont-size: 12px;\n\t\t\tcolor: #444;\n\t\t\t\n\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\tfloat: right;\n\t\t\t\tmargin-right: 0;\n\t\t\t\tmargin-left: 5px;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// controlls\n\t\t.acf-fc-layout-controls {\n\t\t\tposition: absolute;\n\t\t\ttop: 8px;\n\t\t\tright: 8px;\n\t\t\t\n\t\t\t.acf-icon {\n\t\t\t\tdisplay: block;\n\t\t\t\tfloat: left;\n\t\t\t\tmargin: 0 0 0 5px;\n\t\t\t\t\n\t\t\t\t&.-plus, &.-minus, &.-duplicate { visibility: hidden; }\n\t\t\t}\n\t\t\t\n\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\tright: auto;\n\t\t\t\tleft: 9px;\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t&.is-selected {\n\t\t\tborder-color: $wp-input-border;\n\t\t\t.acf-fc-layout-handle  {\n\t\t\t\tborder-color: $wp-input-border;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// open\n\t\t&:hover, \n\t\t&.-hover {\n\t\t\t\n\t\t\t// controls\n\t\t\t.acf-fc-layout-controls {\n\t\t\t\t\n\t\t\t\t.acf-icon {\n\t\t\t\t\t&.-plus, &.-minus, &.-duplicate { visibility: visible; }\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t// collapsed\n\t\t&.-collapsed {\n\t\t\t> .acf-fc-layout-handle {\n\t\t\t\tborder-bottom-width: 0;\n\t\t\t}\n\t\t\t\n\t\t\t> .acf-fields,\n\t\t\t> .acf-table {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// table\n\t\t> .acf-table {\n\t\t\tborder: 0 none;\n\t\t\tbox-shadow: none;\n\t\t\t\n\t\t\t> tbody > tr {\n\t\t\t\tbackground: #fff;\n\t\t\t}\n\t\t\t\n\t\t\t> thead > tr > th {\n\t\t\t\tbackground: #F9F9F9;\n\t\t\t}\n\t\t}\n\t}\n\n\t// no value\n\t.no-value-message {\n\t\tpadding: 19px;\n\t\tborder: #ccc dashed 2px;\n\t\ttext-align: center;\n\t\tdisplay: none;\n\t}\n\n\t// empty\n\t&.-empty > .no-value-message  {\n\t\tdisplay: block;\n\t}\n}\n\n// popup\n.acf-fc-popup {\n\tpadding: 5px 0;\n\tz-index: 900001; // +1 higher than .acf-tooltip\n\tmin-width: 135px;\n\t\n\tul, li {\n\t\tlist-style: none;\n\t\tdisplay: block;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t}\n\t\n\tli {\n\t\tposition: relative;\n\t\tfloat: none;\n\t\twhite-space: nowrap;\n\t}\n\t\n\t.badge {\n\t\tdisplay: inline-block;\n\t\tborder-radius: 8px;\n\t\tfont-size: 9px;\n\t\tline-height: 15px;\n\t\tpadding: 0 5px;\n\t\t\n\t\tbackground: #d54e21;\n\t\ttext-align: center;\n\t\tcolor: #fff;\n\t\tvertical-align: top;\n\t\tmargin: 0 0 0 5px;\n\t}\n\t\n\ta {\n\t\tcolor: #eee;\n\t\tpadding: 5px 10px;\n\t\tdisplay: block;\n\t\ttext-decoration: none;\n\t\tposition: relative;\n\t\t\n\t\t&:hover {\n\t\t\tbackground: #0073aa;\n\t\t\tcolor: #fff;\n\t\t}\n\t\t\n\t\t&.disabled {\n\t\t\tcolor: #888;\n\t\t\tbackground: transparent;\n\t\t}\n\t}\n}\n\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Galery\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-gallery {\n\tborder: $wp-card-border solid 1px;\n\theight: 400px;\n\tposition: relative;\n\t\n\t/* main */\n\t.acf-gallery-main {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tbackground: #fff;\n\t\tz-index: 2;\n\t}\n\t\n\t/* attachments */\n\t.acf-gallery-attachments {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 48px;\n\t\tleft: 0;\n\t\tpadding: 5px;\n\t\toverflow: auto;\n\t\toverflow-x: hidden;\n\t}\n\t\n\t\n\t/* attachment */\n\t.acf-gallery-attachment {\n\t\twidth: 25%;\n\t\tfloat: left;\n\t\tcursor: pointer;\n\t\tposition: relative;\n\t\t\n\t\t.margin {\n\t\t\tmargin: 5px;\n\t\t\tborder: $wp-card-border-1 solid 1px;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t\tbackground: #eee;\n\t\t\t\n\t\t\t&:before {\n\t\t\t\tcontent: \"\";\n\t\t\t    display: block;\n\t\t\t    padding-top: 100%;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.thumbnail {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\ttransform: translate(50%, 50%);\n\t\t\t\n\t\t\thtml[dir=\"rtl\"] & { \n\t\t\t\ttransform: translate(-50%, 50%);\n\t\t\t}\n\t\t\t\n\t\t\timg {\n\t\t\t\tdisplay: block;\n\t\t\t\theight: auto;\n\t\t\t\tmax-height: 100%;\n\t\t\t\twidth: auto;\n\t\t\t\ttransform: translate(-50%, -50%);\n\t\t\t\t\n\t\t\t\thtml[dir=\"rtl\"] & { \n\t\t\t\t\ttransform: translate(50%, -50%);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.filename {\n\t\t    position: absolute;\n\t\t    bottom: 0;\n\t\t    left: 0;\n\t\t    right: 0;\n\t\t    padding: 5%;\n\t\t    background: #F4F4F4;\n\t\t    background: rgba(255, 255, 255, 0.8);\n\t\t    border-top: #DFDFDF solid 1px;\n\t\t    font-weight: bold;\n\t\t    text-align: center;\n\t\t    word-wrap: break-word;\n\t\t    max-height: 90%;\n\t\t    overflow: hidden;\n\t\t}\n\t\t\n\t\t.actions {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tdisplay: none;\n\t\t}\n\t\t\n\t\t\n\t\t/* hover */\n\t\t&:hover {\n\t\t\t\n\t\t\t.actions {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t\t\n\t\t/* sortable */\n\t\t&.ui-sortable-helper {\n\t\t\t\n\t\t\t.margin {\n\t\t\t\tborder: none;\n\t\t\t\tbox-shadow: 0 1px 3px rgba(0,0,0,0.3);\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.ui-sortable-placeholder {\n\t\t\t\n\t\t\t.margin {\n\t\t\t\tbackground: #F1F1F1;\n\t\t\t\tborder: none;\n\t\t\t\t\n\t\t\t\t* {\n\t\t\t\t\tdisplay: none !important;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t\t\n\t\t/* active */\n\t\t&.active {\n\t\t\t\n\t\t\t.margin {\n\t\t\t\tbox-shadow: 0 0 0 1px #FFFFFF, 0 0 0 5px #0073aa;\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t\t\n\t\t/* icon */\n\t\t&.-icon {\n\t\t\t\n\t\t\t.thumbnail img {\n\t\t\t\ttransform: translate(-50%, -70%);\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t\t\n\t\t/* rtl */\n\t\thtml[dir=\"rtl\"] & {\n\t\t\tfloat: right;\n\t\t}\n\t\t\n\t}\n\t\n\t\n\t\n\t&.sidebar-open {\n\t\t\n\t\t/* hide attachment actions when sidebar is open */\n\t\t.acf-gallery-attachment .actions {\n\t\t\tdisplay: none;\n\t\t}\n\t\t\n\t\t\n\t\t/* allow sidebar to move over main for small widths (widget edit box) */\n\t\t.acf-gallery-side {\n\t\t\tz-index: 2;\n\t\t}\n\t\t\n\t}\n\t\n\t\n\t/* toolbar */\n\t.acf-gallery-toolbar {\n\t\tposition: absolute;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tpadding: 10px;\n\t\tborder-top: $wp-card-border-1 solid 1px;\n\t\tbackground: #fff;\n\t\tmin-height: 28px;\n\t\t\n\t\t.acf-hl li {\n\t\t\tline-height: 24px;\n\t\t}\n\t\t\n\t\t.bulk-actions-select {\n\t\t\twidth: auto;\n\t\t\tmargin: 0 1px 0 0;\n\t\t}\n\t\t\n\t}\n\t\n\t\n\t/* sidebar */\n\t.acf-gallery-side {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\twidth: 0;\n\t\t\n\t\tbackground: #F9F9F9;\n\t\tborder-left: $wp-card-border solid 1px;\n\t\t\n\t\tz-index: 1;\n\t\toverflow: hidden;\n\t\t\n\t\t.acf-gallery-side-inner {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tbottom: 0;\n\t\t\twidth: 349px;\n\t\t}\n\t\t\n\t}\n\t\n\t\n\t/* side info */\n\t.acf-gallery-side-info {\n\t\t@include clearfix();\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\tpadding: 10px;\n\t\tmargin: -10px 0 15px -10px;\n\t\tbackground: #F1F1F1;\n\t\tborder-bottom: #DFDFDF solid 1px;\n\t\t\n\t\thtml[dir=\"rtl\"] & {\n\t\t\tmargin-left: 0;\n\t\t\tmargin-right: -10px;\n\t\t}\n\t\n\t\timg {\n\t\t\tfloat: left;\n\t\t\twidth: auto;\n\t\t\tmax-width: 65px;\n\t\t\tmax-height: 65px;\n\t\t\tmargin: 0 10px 1px 0;\n\t\t\tbackground: #FFFFFF;\n\t\t\tpadding: 3px;\n\t\t\tborder: $wp-card-border solid 1px;\n\t\t\tborder-radius: 1px;\n\t\t\t\n\t\t\t/* rtl */\n\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\tfloat: right;\n\t\t\t\tmargin: 0 0 0 10px;\n\t\t\t}\n\t\t}\n\t\t\t\t\n\t\tp {\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 15px;\n\t\t\tmargin: 3px 0;\n\t\t\tword-break: break-all;\n\t\t\tcolor: #666;\n\t\t\t\n\t\t\tstrong {\n\t\t\t\tcolor: #000;\n\t\t\t}\n\t\t}\n\t\t\n\t\ta {\n\t\t\ttext-decoration: none;\n\t\t\t\n\t\t\t&.acf-gallery-edit {\n\t\t\t\tcolor: #21759b;\n\t\t\t}\n\t\t\t\n\t\t\t&.acf-gallery-remove {\n\t\t\t\tcolor: #bc0b0b;\n\t\t\t}\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\ttext-decoration: underline;\n\t\t\t}\n\t\t\n\t\t}\n\t\t\n\t}\n\t\n\t\n\t/* side data */\n\t.acf-gallery-side-data {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 48px;\n\t\tleft: 0;\n\t\toverflow: auto;\n\t\toverflow-x: inherit;\n\t\tpadding: 10px;\n\t\n\t\t\n\t\t.acf-label,\n\t\tth.label {\n\t\t\tcolor: #666666;\n\t\t\tfont-size: 12px;\n\t\t\tline-height: 25px;\n\t\t\tpadding: 0 4px 8px 0 !important;\n\t\t\twidth: auto !important;\n\t\t\tvertical-align: top;\n\t\t\t\n\t\t\thtml[dir=\"rtl\"] & { \n\t\t\t\tpadding: 0 0 8px 4px !important;\n\t\t\t}\n\t\t\t\n\t\t\tlabel {\n\t\t\t\tfont-weight: normal;\n\t\t\t}\n\t\t}\n\t\t\t\t\n\t\t.acf-input,\n\t\ttd.field {\n\t\t\tpadding: 0 0 8px !important;\n\t\t}\n\t\t\n\t\ttextarea {\n\t\t\tmin-height: 0;\n\t\t\theight: 60px;\n\t\t}\n\t\t\n\t\tp.help {\n\t\t\tfont-size: 12px;\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tfont-weight: normal;\n\t\t\t}\n\t\t}\n\t\n\t}\n\t\n\t\n\t/* column widths */\n\t&[data-columns=\"1\"] .acf-gallery-attachment { width: 100%; }\n\t&[data-columns=\"2\"] .acf-gallery-attachment { width: 50%; }\n\t&[data-columns=\"3\"] .acf-gallery-attachment { width: 33.333%; }\n\t&[data-columns=\"4\"] .acf-gallery-attachment { width: 25%; }\n\t&[data-columns=\"5\"] .acf-gallery-attachment { width: 20%; }\n\t&[data-columns=\"6\"] .acf-gallery-attachment { width: 16.666%; }\n\t&[data-columns=\"7\"] .acf-gallery-attachment { width: 14.285%; }\n\t&[data-columns=\"8\"] .acf-gallery-attachment { width: 12.5%; }\n\t\n\t\n\t/* resizable */\n\t.ui-resizable-handle {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t}\n\t\n\t.ui-resizable-s {\n\t\tbottom: -5px;\n\t\tcursor: ns-resize;\n\t\theight: 7px;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t}\n\n}\n\n\n\n/* media modal selected */\n.acf-media-modal .attachment.acf-selected {\n\tbox-shadow: 0 0 0 3px #fff inset, 0 0 0 7px #0073aa inset !important;\n\t\n\t.check {\n\t\tdisplay: none !important;\n\t}\n\t\n\t.thumbnail {\n\t\topacity: 0.25 !important;\n\t}\n\t\t\n\t.attachment-preview:before {\n\t\tbackground: rgba(0,0,0,0.15);\n\t\tz-index: 1;\n\t\tposition: relative;\n\t}\n\n}\n\n\n.acf-admin-single-options-page {\n\t.select2-dropdown {\n\t\tborder-color: $blue-300 !important;\n\t\tmargin-top: -5px;\n\t\toverflow: hidden;\n\t\tbox-shadow: $elevation-01;\n\t}\n\n\t.select2-dropdown.select2-dropdown--above {\n\t\tmargin-top: 0;\n\t}\n\n\t.select2-container--default .select2-results__option[aria-selected=\"true\"] {\n\t\tbackground-color: $gray-50 !important;\n\t\tcolor: $gray-500;\n\n\t\t&:hover {\n\t\t\tcolor: $blue-400;\n\t\t}\n\t}\n\n\t.select2-container--default\n\t\t.select2-results__option--highlighted[aria-selected] {\n\t\tcolor: #fff !important;\n\t\tbackground-color: $blue-500 !important;\n\t}\n\n\t// remove bottom margin on options\n\t.select2-dropdown .select2-results__option {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n// z-index helper for the popup modal.\n.acf-create-options-page-popup ~ .select2-container {\n\tz-index: 999999999;\n}\n", "/*-----------------------------------------------------------------------------\n*\n*\tACF Blocks\n*\n*----------------------------------------------------------------------------*/\n\n// All block components.\n.acf-block-component {\n\n\t.components-placeholder {\n\t\tmargin: 0;\n\t}\n}\n\n.block-editor .acf-field.acf-error {\n\tbackground-color: rgba(255, 0, 0, 0.05);\n}\n\n// Block fields\n.acf-block-component .acf-block-fields {\n\t// Ensure white background behind fields.\n\tbackground: #fff;\n\n\t// Generic body styles\n\ttext-align: left;\n\tfont-size: 13px;\n\tline-height: 1.4em;\n\tcolor: #444;\n\tfont-family:\n\t\t-apple-system,\n\t\tBlinkMacSystemFont,\n\t\t\"Segoe UI\",\n\t\tRoboto,\n\t\tOxygen-<PERSON>,\n\t\tUbuntu,\n\t\tCantarell,\n\t\t\"Helvetica Neue\",\n\t\tsans-serif;\n\n\t&.acf-empty-block-fields {\n\t\tborder: 1px solid #1e1e1e;\n\t\tpadding: 12px;\n\n\t\t.components-panel & {\n\t\t\tborder: none;\n\t\t\tborder-top: 1px solid #ddd;\n\t\t\tborder-bottom: 1px solid #ddd;\n\t\t}\n\t}\n\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t}\n\n\tp {\n\t\tfont-size: 13px;\n\t\tline-height: 1.5;\n\t}\n}\n\n// Block body.\n.acf-block-body,\n.acf-block-fields:has(> .acf-error-message) {\n\n\t.acf-block-fields:has(> .acf-error-message) {\n\t\tborder: none !important;\n\t}\n\n\n\t.acf-error-message {\n\t\tmargin-top: 0;\n\t\tborder: none;\n\n\t\t.acf-notice-dismiss {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\toverflow: hidden;\n\n\t\t\t// Prevent Core outline styles from impacting the close button's focus state. Without unsetting the outline, we get a black glow around the button.\n\t\t\toutline: unset;\n\t\t}\n\n\t\t.acf-icon.-cancel::before {\n\t\t\tmargin: 0 !important;\n\t\t}\n\n\t}\n\n\t&.acf-block-has-validation-error {\n\t\tborder: 2px solid #d94f4f;\n\t}\n\n\t.acf-error .acf-input .acf-notice {\n\t\tbackground: none !important;\n\t\tborder: none !important;\n\t\tdisplay: flex !important;\n\t\talign-items: center !important;\n\t\tpadding-left: 0;\n\n\t\tp {\n\t\t\tmargin: 0.5em 0 !important;\n\t\t}\n\t}\n\n\n\t.acf-error .acf-input .acf-notice::before {\n\t\tcontent: \"\";\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tfont-size: 20px;\n\t\tbackground-image: url(../../../images/icons/icon-info-red.svg);\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position: center;\n\t\tbackground-size: 69%;\n\t\theight: 26px !important;\n\t\twidth: 26px !important;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.acf-error .acf-label label {\n\t\tcolor: #d94f4f;\n\t}\n\n\t.acf-error .acf-input input {\n\t\tborder-color: #d94f4f;\n\t}\n\n\t&.acf-block-has-validation-error::before {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: -2px;\n\t\tleft: -32px;\n\t\tfont-size: 20px;\n\t\tbackground-color: #d94f4f;\n\t\tbackground-image: url(../../../images/icons/icon-info-white.svg);\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position-x: center;\n\t\t// Offset the icon down slighly to match the notice text basline that is being impacted by the outer stroke\n\t\tbackground-position-y: 52%;\n\t\tbackground-size: 55%;\n\t\theight: 40px;\n\t\twidth: 32px;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.acf-block-validation-error {\n\t\tcolor: #d94f4f;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t// Fields wrapper.\n\t.acf-block-fields {\n\t\tborder: #adb2ad solid 1px;\n\n\t\t// Tab\n\t\t.acf-tab-wrap {\n\n\t\t\t.acf-tab-group {\n\t\t\t\tmargin-left: 0;\n\t\t\t\tpadding: 16px 20px 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Block fields (div).\n\t.acf-fields > .acf-field {\n\t\tpadding: 16px 20px;\n\n\t\t// Accordions.\n\t\t&.acf-accordion {\n\t\t\tborder-color: #adb2ad;\n\n\t\t\t.acf-accordion-title {\n\t\t\t\tpadding: 16px 20px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Ensure ACF buttons aren't changed by theme colors in the block editor.\n\t.acf-button,\n\t.acf-link a.button,\n\t.acf-add-checkbox {\n\t\tcolor: #2271b1 !important;\n\t\tborder-color: #2271b1 !important;\n\t\tbackground: #f6f7f7 !important;\n\t\tvertical-align: top;\n\n\t\t&.button-primary:hover {\n\t\t\tcolor: white !important;\n\t\t\tbackground: #2271b1 !important;\n\t\t}\n\n\t\t&:focus {\n\t\t\toutline: none !important;\n\t\t\tbackground: #f6f7f7 !important;\n\t\t}\n\n\t\t&:hover {\n\t\t\tcolor: #0a4b78 !important;\n\t\t}\n\t}\n\n\t// Preview.\n\t.acf-block-preview {\n\t\tmin-height: 10px;\n\t}\n}\n\n// Block panel.\n.acf-block-panel {\n\t// Fields wrapper.\n\t.acf-block-fields {\n\t\tborder-top: #ddd solid 1px;\n\t\tborder-bottom: #ddd solid 1px;\n\t\tmin-height: 1px;\n\n\t\t&:empty {\n\t\t\tborder-top: none;\n\t\t}\n\n\t\t// Tab\n\t\t.acf-tab-wrap {\n\t\t\tbackground: transparent;\n\t\t}\n\t}\n}\n\n// Add compatibility for WP 5.3 and older.\n// - Sidebar area is wrapped in a PanelBody element.\n.components-panel__body .acf-block-panel {\n\tmargin: 16px -16px -16px;\n}\n"], "names": [], "sourceRoot": ""}