{"version": 3, "file": "acf-pro-ui-options-page.js", "mappings": ";;;;;;;;;AAAA,CAAE,UAAWA,CAAC,EAAEC,SAAS,EAAG;EAE3B,MAAMC,wBAAwB,GAAG,SAAAA,CAAWC,SAAS,EAAG;IACvD,IAAK,WAAW,KAAK,OAAOA,SAAS,CAACC,OAAO,EAAG;MAC/C,OAAOD,SAAS;IACjB;;IAEA;IACA,IAAKA,SAAS,CAACE,QAAQ,IAAI,MAAM,KAAKF,SAAS,CAACG,IAAI,EAAG;MACtD;IACD;IAEA,IAAK,iBAAiB,KAAKH,SAAS,CAACG,IAAI,EAAG;MAC3CH,SAAS,CAACG,IAAI,GAAGC,GAAG,CAACC,EAAE,CAAE,eAAgB,CAAC;IAC3C;IAEA,OAAOR,CAAC,CAAE,qCAAsC,CAAC,CAC/CS,IAAI,CAAE,SAAS,EAAEN,SAAS,CAACC,OAAQ,CAAC,CACpCM,IAAI,CAAEH,GAAG,CAACI,SAAS,CAAER,SAAS,CAACG,IAAK,CAAE,CAAC;EAC1C,CAAC;EAED,MAAMM,mBAAmB,GAAG,SAAAA,CAAWT,SAAS,EAAG;IAClD,IAAK,WAAW,KAAK,OAAOA,SAAS,CAACC,OAAO,EAAG;MAC/C,OAAOD,SAAS;IACjB;IAEA,MAAMU,UAAU,GAAGb,CAAC,CAAE,qCAAsC,CAAC;IAC7Da,UAAU,CAACH,IAAI,CAAEH,GAAG,CAACI,SAAS,CAAER,SAAS,CAACC,OAAO,CAACU,SAAU,CAAE,CAAC;IAE/D,IACCX,SAAS,CAACY,EAAE,KAAK,SAAS,IAC1BZ,SAAS,CAACY,EAAE,KAAK,YAAY,EAC5B;MACDF,UAAU,CAACG,MAAM,CAChB,yCAAyC,GACzCT,GAAG,CAACC,EAAE,CAAE,SAAU,CAAC,GACnB,SACD,CAAC;IACF;IACAK,UAAU,CAACJ,IAAI,CAAE,SAAS,EAAEN,SAAS,CAACC,OAAQ,CAAC;IAC/C,OAAOS,UAAU;EAClB,CAAC;EAED,MAAMI,oBAAoB,GAAG,IAAIV,GAAG,CAACW,KAAK,CAAC;IAC1CH,EAAE,EAAE,sBAAsB;IAC1BI,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;MACP,sCAAsC,EAAG;IAC1C,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAA,EAAW;MACtB,IAAK,iBAAiB,KAAKd,GAAG,CAACe,GAAG,CAAE,QAAS,CAAC,EAAG;QAChD;MACD;MACAf,GAAG,CAACgB,UAAU,CAAEvB,CAAC,CAAE,qCAAsC,CAAC,EAAE;QAC3DwB,KAAK,EAAE,KAAK;QACZC,iBAAiB,EAAEvB,wBAAwB;QAC3CwB,cAAc,EAAExB,wBAAwB;QACxCyB,gBAAgB,EAAE;MACnB,CAAE,CAAC;MAEHpB,GAAG,CAACgB,UAAU,CAAEvB,CAAC,CAAE,oCAAqC,CAAC,EAAE;QAC1DwB,KAAK,EAAE,KAAK;QACZC,iBAAiB,EAAEb,mBAAmB;QACtCc,cAAc,EAAEd;MACjB,CAAE,CAAC;MAEHL,GAAG,CAACgB,UAAU,CAAEvB,CAAC,CAAE,sCAAuC,CAAC,EAAE;QAC5DwB,KAAK,EAAE,KAAK;QACZC,iBAAiB,EAAEb,mBAAmB;QACtCc,cAAc,EAAEd;MACjB,CAAE,CAAC;MAEH,IAAI,CAACgB,sBAAsB,CAAC,CAAC;IAC9B,CAAC;IAEDA,sBAAsB,EAAE,SAAAA,CAAUC,CAAC,EAAEC,GAAG,EAAG;MAC1C,MAAMC,UAAU,GAAG/B,CAAC,CAAE,qCAAsC,CAAC,CAACgC,GAAG,CAAC,CAAC;MAEnE,IAAK,MAAM,KAAKD,UAAU,EAAG;QAC5B/B,CAAC,CAAE,+BAAgC,CAAC,CAACiC,IAAI,CAAC,CAAC;QAC3CjC,CAAC,CAAE,gCAAiC,CAAC,CAACkC,IAAI,CAAC,CAAC;MAC7C,CAAC,MAAM;QACNlC,CAAC,CAAE,gCAAiC,CAAC,CAACiC,IAAI,CAAC,CAAC;QAC5CjC,CAAC,CAAE,+BAAgC,CAAC,CAACkC,IAAI,CAAC,CAAC;MAC5C;IACD;EACD,CAAC,CAAC;EAEF,MAAMC,uBAAuB,GAAG,IAAI5B,GAAG,CAACW,KAAK,CAAE;IAC9CH,EAAE,EAAE,yBAAyB;IAC7BK,MAAM,EAAE;MACP,6BAA6B,EAAE;IAChC,CAAC;IACDgB,iBAAiB,EAAE,SAAAA,CAAUP,CAAC,EAAG;MAChC,MAAMQ,eAAe,GAAGrC,CAAC,CAAE6B,CAAC,CAACS,MAAO,CAAC;MAErC,IAAK,sBAAsB,KAAKD,eAAe,CAACL,GAAG,CAAC,CAAC,EAAG;QACvD;MACD;MAEA,IAAIO,KAAK,GAAG,KAAK;MAEjB,MAAMC,OAAO,GAAG,SAAAA,CAAA,EAAW;QAC1B,MAAMC,eAAe,GAAGzC,CAAC,CAAE,4BAA6B,CAAC,CAACgC,GAAG,CAAC,CAAC;QAC/D,MAAMU,QAAQ,GAAG;UAChBC,MAAM,EAAE,yBAAyB;UACjCC,uBAAuB,EAAE,IAAI,CAACrC,GAAG,CAACE,IAAI,CAACoC,uBAAuB,GAAG,IAAI,CAACtC,GAAG,CAACE,IAAI,CAACoC,uBAAuB,GAAG;QAC1G,CAAC;QAED,IAAKJ,eAAe,CAACK,MAAM,EAAG;UAC7BJ,QAAQ,CAACK,iBAAiB,GAAGN,eAAe;QAC7C;QAEAzC,CAAC,CAACgD,IAAI,CAAE;UACPC,GAAG,EAAE1C,GAAG,CAACe,GAAG,CAAE,SAAU,CAAC;UACzBb,IAAI,EAAEF,GAAG,CAAC2C,cAAc,CAAER,QAAS,CAAC;UACpCS,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEC;QACV,CAAE,CAAC;MACJ,CAAC;MAED,MAAMA,YAAY,GAAG,SAAAA,CAAUC,QAAQ,EAAG;QACzChB,KAAK,GAAGhC,GAAG,CAACiD,QAAQ,CAAE;UACrBC,KAAK,EAAEF,QAAQ,CAAC9C,IAAI,CAACgD,KAAK;UAC1BC,OAAO,EAAEH,QAAQ,CAAC9C,IAAI,CAACiD,OAAO;UAC9BC,KAAK,EAAE;QACR,CAAE,CAAC;QAEHpB,KAAK,CAACT,GAAG,CAAC8B,QAAQ,CAAE,+BAAgC,CAAC;;QAErD;QACA,MAAMC,UAAU,GAAGtB,KAAK,CAACT,GAAG,CAACgC,IAAI,CAAE,iCAAkC,CAAC;QACtE,MAAMC,YAAY,GAAGF,UAAU,CAAC7B,GAAG,CAAC,CAAC;QACrC6B,UAAU,CAACG,KAAK,CAAC,CAAC,CAAChC,GAAG,CAAE,EAAG,CAAC,CAACA,GAAG,CAAE+B,YAAa,CAAC;QAEhDxD,GAAG,CAACgB,UAAU,CAAEvB,CAAC,CAAE,kCAAmC,CAAC,EAAE;UACxDwB,KAAK,EAAE,KAAK;UACZC,iBAAiB,EAAEvB,wBAAwB;UAC3CwB,cAAc,EAAExB,wBAAwB;UACxCyB,gBAAgB,EAAE;QACnB,CAAE,CAAC;QAEHY,KAAK,CAAC0B,EAAE,CAAE,QAAQ,EAAE,MAAM,EAAEC,YAAa,CAAC;MAC3C,CAAC;MAED,MAAMA,YAAY,GAAG,SAAAA,CAAUrC,CAAC,EAAG;QAClCA,CAAC,CAACsC,cAAc,CAAC,CAAC;QAElB5D,GAAG,CAAC2D,YAAY,CAAE;UACjBE,IAAI,EAAEpE,CAAC,CAAE,+BAAgC,CAAC;UAC1CqD,OAAO,EAAEgB,UAAU;UACnBC,OAAO,EAAEC;QACV,CAAE,CAAC;MACJ,CAAC;MAED,MAAMF,UAAU,GAAG,SAAAA,CAAA,EAAW;QAC7B,MAAMG,UAAU,GAAGxE,CAAC,CAAE,+BAAgC,CAAC,CAACyE,cAAc,CAAC,CAAC;QACxE,MAAM/B,QAAQ,GAAG;UAChBC,MAAM,EAAE;QACT,CAAC;QAED6B,UAAU,CAACE,OAAO,CAAEC,OAAO,IAAI;UAC9BjC,QAAQ,CAAEiC,OAAO,CAACC,IAAI,CAAE,GAAGD,OAAO,CAACE,KAAK;QACzC,CAAE,CAAC;QAEH7E,CAAC,CAACgD,IAAI,CAAE;UACPC,GAAG,EAAE1C,GAAG,CAACe,GAAG,CAAE,SAAU,CAAC;UACzBb,IAAI,EAAEF,GAAG,CAAC2C,cAAc,CAAER,QAAS,CAAC;UACpCS,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEyB;QACV,CAAE,CAAC;MACJ,CAAC;MAED,MAAMP,MAAM,GAAG,SAAAA,CAAU1C,CAAC,EAAG;QAC5B,MAAMkD,KAAK,GAAG/E,CAAC,CAAE,+BAAgC,CAAC;QAClD,MAAMgF,aAAa,GAAGD,KAAK,CAACjB,IAAI,CAAE,+BAAgC,CAAC;;QAEnE;QACAiB,KAAK,CAACjB,IAAI,CAAE,aAAc,CAAC,CAACmB,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;;QAE5C;QACAF,aAAa,CAACG,IAAI,CAAE,YAAW;UAC9B,MAAMC,MAAM,GAAGpF,CAAC,CAAE,IAAK,CAAC,CAACqF,OAAO,CAAE,YAAa,CAAC,CAACvB,IAAI,CAAE,kBAAmB,CAAC;UAC3E9D,CAAC,CAAE,IAAK,CAAC,CAACsF,IAAI,CAAE,OAAO,EAAE,8BAA+B,CAAC,CAACC,QAAQ,CAAEH,MAAO,CAAC;QAC7E,CAAE,CAAC;MACJ,CAAC;MAED,MAAMN,sBAAsB,GAAG,SAAAA,CAAUvB,QAAQ,EAAG;QACnD,IAAKA,QAAQ,CAACF,OAAO,IAAIE,QAAQ,CAAC9C,IAAI,CAAC+E,SAAS,EAAG;UAClDnD,eAAe,CAACoD,OAAO,CACtB,iBAAiB,GAAGlC,QAAQ,CAAC9C,IAAI,CAAC+E,SAAS,GAAG,IAAI,GAAGjC,QAAQ,CAAC9C,IAAI,CAACiF,UAAU,GAAG,WACjF,CAAC;UACDrD,eAAe,CAACL,GAAG,CAAEuB,QAAQ,CAAC9C,IAAI,CAAC+E,SAAU,CAAC;UAC9CjD,KAAK,CAACoD,KAAK,CAAC,CAAC;QACd,CAAC,MAAM,IAAK,CAAEpC,QAAQ,CAACF,OAAO,IAAIE,QAAQ,CAAC9C,IAAI,CAACmF,KAAK,EAAG;UACvDC,KAAK,CAACtC,QAAQ,CAAC9C,IAAI,CAACmF,KAAK,CAAC;QAC3B;MACD,CAAC;MAEDpD,OAAO,CAAC,CAAC;IACV;EACD,CAAE,CAAC;AAEJ,CAAC,EAAIsD,MAAO,CAAC;;;;;;UC7Mb;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-ui-options-page.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/acf-pro-ui-options-page.js"], "sourcesContent": ["( function ( $, undefined ) {\n\n\tconst parentPageSelectTemplate = function ( selection ) {\n\t\tif ( 'undefined' === typeof selection.element ) {\n\t\t\treturn selection;\n\t\t}\n\n\t\t// Hides the optgroup for the \"No Parent\" option.\n\t\tif ( selection.children && 'None' === selection.text ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( 'acfOptionsPages' === selection.text ) {\n\t\t\tselection.text = acf.__( 'Options Pages' );\n\t\t}\n\n\t\treturn $( '<span class=\"acf-selection\"></span>' )\n\t\t\t.data( 'element', selection.element )\n\t\t\t.html( acf.strEscape( selection.text ) );\n\t};\n\n\tconst defaultPillTemplate = function ( selection ) {\n\t\tif ( 'undefined' === typeof selection.element ) {\n\t\t\treturn selection;\n\t\t}\n\n\t\tconst $selection = $( '<span class=\"acf-selection\"></span>' );\n\t\t$selection.html( acf.strEscape( selection.element.innerHTML ) );\n\n\t\tif (\n\t\t\tselection.id === 'options' ||\n\t\t\tselection.id === 'edit_posts'\n\t\t) {\n\t\t\t$selection.append(\n\t\t\t\t'<span class=\"acf-select2-default-pill\">' +\n\t\t\t\tacf.__( 'Default' ) +\n\t\t\t\t'</span>'\n\t\t\t);\n\t\t}\n\t\t$selection.data( 'element', selection.element );\n\t\treturn $selection;\n\t};\n\n\tconst UIOptionsPageManager = new acf.Model({\n\t\tid: 'UIOptionsPageManager',\n\t\twait: 'ready',\n\t\tevents: {\n\t\t\t'change .acf-options-page-parent_slug' : 'toggleMenuPositionDesc',\n\t\t},\n\t\tinitialize: function() {\n\t\t\tif ( 'ui_options_page' !== acf.get( 'screen' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tacf.newSelect2( $( 'select.acf-options-page-parent_slug' ), {\n\t\t\t\tfield: false,\n\t\t\t\ttemplateSelection: parentPageSelectTemplate,\n\t\t\t\ttemplateResult: parentPageSelectTemplate,\n\t\t\t\tdropdownCssClass: 'field-type-select-results'\n\t\t\t} );\n\n\t\t\tacf.newSelect2( $( 'select.acf-options-page-capability' ), {\n\t\t\t\tfield: false,\n\t\t\t\ttemplateSelection: defaultPillTemplate,\n\t\t\t\ttemplateResult: defaultPillTemplate,\n\t\t\t} );\n\n\t\t\tacf.newSelect2( $( 'select.acf-options-page-data_storage' ), {\n\t\t\t\tfield: false,\n\t\t\t\ttemplateSelection: defaultPillTemplate,\n\t\t\t\ttemplateResult: defaultPillTemplate,\n\t\t\t} );\n\n\t\t\tthis.toggleMenuPositionDesc();\n\t\t},\n\n\t\ttoggleMenuPositionDesc: function( e, $el ) {\n\t\t\tconst parentPage = $( 'select.acf-options-page-parent_slug' ).val();\n\n\t\t\tif ( 'none' === parentPage ) {\n\t\t\t\t$( '.acf-menu-position-desc-child' ).hide();\n\t\t\t\t$( '.acf-menu-position-desc-parent' ).show();\n\t\t\t} else {\n\t\t\t\t$( '.acf-menu-position-desc-parent' ).hide();\n\t\t\t\t$( '.acf-menu-position-desc-child' ).show();\n\t\t\t}\n\t\t},\n\t});\n\n\tconst optionsPageModalManager = new acf.Model( {\n\t\tid: 'optionsPageModalManager',\n\t\tevents: {\n\t\t\t'change .location-rule-value': 'createOptionsPage',\n\t\t},\n\t\tcreateOptionsPage: function( e ) {\n\t\t\tconst $locationSelect = $( e.target );\n\n\t\t\tif ( 'add_new_options_page' !== $locationSelect.val() ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet popup = false;\n\n\t\t\tconst getForm = function() {\n\t\t\t\tconst fieldGroupTitle = $( '.acf-headerbar-title-field' ).val();\n\t\t\t\tconst ajaxData = {\n\t\t\t\t\taction: 'acf/create_options_page',\n\t\t\t\t\tacf_parent_page_choices: this.acf.data.optionPageParentOptions ? this.acf.data.optionPageParentOptions : []\n\t\t\t\t};\n\n\t\t\t\tif ( fieldGroupTitle.length ) {\n\t\t\t\t\tajaxData.field_group_title = fieldGroupTitle;\n\t\t\t\t}\n\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'json',\n\t\t\t\t\tsuccess: populateForm,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tconst populateForm = function( response ) {\n\t\t\t\tpopup = acf.newPopup( {\n\t\t\t\t\ttitle: response.data.title,\n\t\t\t\t\tcontent: response.data.content,\n\t\t\t\t\twidth: '600px',\n\t\t\t\t} );\n\n\t\t\t\tpopup.$el.addClass( 'acf-create-options-page-popup' );\n\n\t\t\t\t// Hack to focus with the cursor at the end of the input.\n\t\t\t\tconst $pageTitle = popup.$el.find( '#acf_ui_options_page-page_title' );\n\t\t\t\tconst pageTitleVal = $pageTitle.val();\n\t\t\t\t$pageTitle.focus().val( '' ).val( pageTitleVal );\n\n\t\t\t\tacf.newSelect2( $( '#acf_ui_options_page-parent_slug' ), {\n\t\t\t\t\tfield: false,\n\t\t\t\t\ttemplateSelection: parentPageSelectTemplate,\n\t\t\t\t\ttemplateResult: parentPageSelectTemplate,\n\t\t\t\t\tdropdownCssClass: 'field-type-select-results'\n\t\t\t\t} );\n\n\t\t\t\tpopup.on( 'submit', 'form', validateForm );\n\t\t\t};\n\n\t\t\tconst validateForm = function( e ) {\n\t\t\t\te.preventDefault();\n\n\t\t\t\tacf.validateForm( {\n\t\t\t\t\tform: $( '#acf-create-options-page-form' ),\n\t\t\t\t\tsuccess: submitForm,\n\t\t\t\t\tfailure: onFail\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\tconst submitForm = function() {\n\t\t\t\tconst formValues = $( '#acf-create-options-page-form' ).serializeArray();\n\t\t\t\tconst ajaxData = {\n\t\t\t\t\taction: 'acf/create_options_page'\n\t\t\t\t};\n\n\t\t\t\tformValues.forEach( setting => {\n\t\t\t\t\tajaxData[ setting.name ] = setting.value;\n\t\t\t\t} );\n\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'json',\n\t\t\t\t\tsuccess: populateLocationSelect,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tconst onFail = function( e ) {\n\t\t\t\tconst $form = $( '#acf-create-options-page-form' );\n\t\t\t\tconst $fieldNotices = $form.find( '.acf-field .acf-error-message' );\n\n\t\t\t\t// Hide the general validation failed notice.\n\t\t\t\t$form.find( '.acf-notice' ).first().remove();\n\n\t\t\t\t// Update class for inline notices and move into field label.\n\t\t\t\t$fieldNotices.each( function() {\n\t\t\t\t\tconst $label = $( this ).closest( '.acf-field' ).find( '.acf-label:first' );\n\t\t\t\t\t$( this ).attr( 'class', 'acf-options-page-modal-error' ).appendTo( $label );\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tconst populateLocationSelect = function( response ) {\n\t\t\t\tif ( response.success && response.data.menu_slug ) {\n\t\t\t\t\t$locationSelect.prepend(\n\t\t\t\t\t\t'<option value=\"' + response.data.menu_slug + '\">' + response.data.page_title + '</option>'\n\t\t\t\t\t);\n\t\t\t\t\t$locationSelect.val( response.data.menu_slug );\n\t\t\t\t\tpopup.close();\n\t\t\t\t} else if ( ! response.success && response.data.error ) {\n\t\t\t\t\talert(response.data.error);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tgetForm();\n\t\t},\n\t} );\n\n} )( jQuery );", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_acf-ui-options-page';\n"], "names": ["$", "undefined", "parentPageSelectTemplate", "selection", "element", "children", "text", "acf", "__", "data", "html", "strEscape", "defaultPillTemplate", "$selection", "innerHTML", "id", "append", "UIOptionsPageManager", "Model", "wait", "events", "initialize", "get", "newSelect2", "field", "templateSelection", "templateResult", "dropdownCssClass", "toggleMenuPositionDesc", "e", "$el", "parentPage", "val", "hide", "show", "optionsPageModalManager", "createOptionsPage", "$locationSelect", "target", "popup", "getForm", "fieldGroupTitle", "ajaxData", "action", "acf_parent_page_choices", "optionPageParentOptions", "length", "field_group_title", "ajax", "url", "prepareForAjax", "type", "dataType", "success", "populateForm", "response", "newPopup", "title", "content", "width", "addClass", "$pageTitle", "find", "pageTitleVal", "focus", "on", "validateForm", "preventDefault", "form", "submitForm", "failure", "onFail", "formValues", "serializeArray", "for<PERSON>ach", "setting", "name", "value", "populateLocationSelect", "$form", "$fieldNotices", "first", "remove", "each", "$label", "closest", "attr", "appendTo", "menu_slug", "prepend", "page_title", "close", "error", "alert", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}