<?php
/**
 * Debug script to check address data structure
 * Add this to your WordPress site temporarily to debug the address data
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing outside WordPress, you can comment this out
    // exit;
}

function debug_address_data() {
    global $wpdb;
    
    echo "<h2>Debug: Address Data Structure</h2>";
    
    // Get a sample user with addresses
    $users_with_addresses = $wpdb->get_results("
        SELECT user_id, meta_value 
        FROM {$wpdb->usermeta} 
        WHERE meta_key = '_wcmca_additional_addresses' 
        AND meta_value != '' 
        LIMIT 3
    ");
    
    if (empty($users_with_addresses)) {
        echo "<p>❌ No users found with WCMCA addresses</p>";
        return;
    }
    
    foreach ($users_with_addresses as $user_data) {
        echo "<h3>User ID: {$user_data->user_id}</h3>";
        
        $addresses = maybe_unserialize($user_data->meta_value);
        
        if (!is_array($addresses)) {
            echo "<p>❌ Address data is not an array</p>";
            continue;
        }
        
        echo "<p>✅ Found " . count($addresses) . " addresses</p>";
        
        foreach ($addresses as $index => $address) {
            echo "<h4>Address #{$index}</h4>";
            echo "<pre>";
            print_r($address);
            echo "</pre>";
            
            // Check specifically for address_id
            if (isset($address['address_id'])) {
                echo "<p>✅ <strong>address_id found:</strong> " . $address['address_id'] . "</p>";
            } else {
                echo "<p>❌ <strong>address_id NOT found</strong></p>";
                echo "<p>Available keys: " . implode(', ', array_keys($address)) . "</p>";
            }
            
            echo "<hr>";
        }
    }
    
    // Also check SAP database table
    echo "<h3>SAP Database Table Check</h3>";
    $sap_table = $wpdb->prefix . 'sap_shipto_addresses';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '{$sap_table}'") != $sap_table) {
        echo "<p>❌ SAP ShipTo table does not exist</p>";
        return;
    }
    
    $sap_records = $wpdb->get_results("SELECT customer_id, wcmca_addresses_data FROM {$sap_table} LIMIT 3");
    
    if (empty($sap_records)) {
        echo "<p>❌ No SAP address records found</p>";
        return;
    }
    
    foreach ($sap_records as $sap_record) {
        echo "<h4>SAP Customer: {$sap_record->customer_id}</h4>";
        
        $sap_addresses = maybe_unserialize($sap_record->wcmca_addresses_data);
        
        if (!is_array($sap_addresses)) {
            echo "<p>❌ SAP address data is not an array</p>";
            continue;
        }
        
        echo "<p>✅ Found " . count($sap_addresses) . " SAP addresses</p>";
        
        foreach ($sap_addresses as $index => $address) {
            echo "<h5>SAP Address #{$index}</h5>";
            
            if (isset($address['address_id'])) {
                echo "<p>✅ <strong>SAP address_id found:</strong> " . $address['address_id'] . "</p>";
            } else {
                echo "<p>❌ <strong>SAP address_id NOT found</strong></p>";
            }
            
            echo "<p>Keys: " . implode(', ', array_keys($address)) . "</p>";
            echo "<hr>";
        }
    }
}

// If running in WordPress admin or frontend
if (defined('ABSPATH')) {
    // Add this as a shortcode for testing: [debug_addresses]
    add_shortcode('debug_addresses', 'debug_address_data');
    
    // Or add to admin menu for testing
    add_action('admin_menu', function() {
        add_submenu_page(
            'tools.php',
            'Debug Addresses',
            'Debug Addresses',
            'manage_options',
            'debug-addresses',
            'debug_address_data'
        );
    });
}

// For direct access (testing)
if (!defined('ABSPATH')) {
    echo "<!DOCTYPE html><html><head><title>Address Debug</title></head><body>";
    echo "<p>This script needs to be run within WordPress context.</p>";
    echo "<p>Add it to your WordPress site and access via:</p>";
    echo "<ul>";
    echo "<li>Admin menu: Tools > Debug Addresses</li>";
    echo "<li>Shortcode: [debug_addresses] on any page</li>";
    echo "</ul>";
    echo "</body></html>";
}
?>
