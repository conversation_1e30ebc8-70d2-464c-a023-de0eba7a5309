<?php
return ['domain'=>NULL,'plural-forms'=>NULL,'language'=>'cs_CZ','project-id-version'=>'Advanced Custom Fields','pot-creation-date'=>'2024-10-02T12:17:25+00:00','po-revision-date'=>'2024-10-02T12:08:46+00:00','x-generator'=>'gettext','messages'=>['Select Multiple'=>'Vybrat více','WP Engine logo'=>'Logo WP Engine','Lower case letters, underscores and dashes only, Max 32 characters.'=>'Pouze malá písmena, podtržítka a pomlčky, max. 32 znaků.','More Tools from WP Engine'=>'Dalš<PERSON> nástroje od WP Engine','Built for those that build with WordPress, by the team at %s'=>'Vytvořeno pro ty, kte<PERSON>í vytvářejí ve WordPressu, týmem %s','View Pricing & Upgrade'=>'Zobrazit ceny a upgrade','%s fields'=>'Pole pro %s','No terms'=>'Žádné pojmy','No post types'=>'Žádné typy obsahu','No posts'=>'Žádné příspěvky','No taxonomies'=>'Žádné taxonomie','No field groups'=>'Žádné skupiny polí','No fields'=>'Žádná pole','No description'=>'Bez popisu','Any post status'=>'Jakýkoli stav příspěvku','This taxonomy key is already in use by another taxonomy registered outside of ACF and cannot be used.'=>'Tento klíč taxonomie je již používán jinou taxonomií registrovanou mimo ACF a nelze jej použít.','This taxonomy key is already in use by another taxonomy in ACF and cannot be used.'=>'Tento klíč taxonomie je již používán jinou taxonomií v ACF a nelze jej použít.','The taxonomy key must only contain lower case alphanumeric characters, underscores or dashes.'=>'Klíč taxonomie musí obsahovat pouze malé alfanumerické znaky, podtržítka nebo pomlčky.','No Taxonomies found in Trash'=>'V koši nebyly nalezeny žádné taxonomie','No Taxonomies found'=>'Nebyly nalezeny žádné taxonomie','Search Taxonomies'=>'Hledat taxonomie','View Taxonomy'=>'Zobrazit taxonomii','New Taxonomy'=>'Nová taxonomie','Edit Taxonomy'=>'Upravit taxonomii','Add New Taxonomy'=>'Přidat novou taxonomii','No Post Types found in Trash'=>'V koši nejsou žádné typy obsahu','No Post Types found'=>'Nebyly nalezeny žádné typy obsahu','Search Post Types'=>'Hledat typy obsahu','View Post Type'=>'Zobrazit typ obsahu','New Post Type'=>'Nový typ obsahu','Edit Post Type'=>'Upravit typ obsahu','Add New Post Type'=>'Přidat nový typ obsahu','This post type key is already in use by another post type registered outside of ACF and cannot be used.'=>'Tento klíč typu obsahu je již používán jiným typem obsahu registrovaným mimo ACF a nelze jej použít.','This post type key is already in use by another post type in ACF and cannot be used.'=>'Tento klíč typu obsahu je již používán jiným typem obsahu v ACF a nelze jej použít.','This field must not be a WordPress <a href="%s" target="_blank">reserved term</a>.'=>'Toto pole nesmí být <a href="%s" target="_blank">vyhrazený termín</a> WordPressu.','The post type key must only contain lower case alphanumeric characters, underscores or dashes.'=>'Klíč typu obsahu musí obsahovat pouze malé alfanumerické znaky, podtržítka nebo pomlčky.','The post type key must be under 20 characters.'=>'Klíč typu obsahu musí mít méně než 20 znaků.','We do not recommend using this field in ACF Blocks.'=>'Nedoporučujeme používat toto pole v blocích ACF.','Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing for a rich text-editing experience that also allows for multimedia content.'=>'Zobrazí WYSIWYG editor WordPressu používaný k úpravám příspěvků a stránek, který umožňuje bohatou editaci textu a také multimediální obsah.','WYSIWYG Editor'=>'Editor WYSIWYG','Allows the selection of one or more users which can be used to create relationships between data objects.'=>'Umožňuje výběr jednoho nebo více uživatelů, které lze použít k vytvoření vztahů mezi datovými objekty.','A text input specifically designed for storing web addresses.'=>'Textové pole určené speciálně pro ukládání webových adres.','URL'=>'URL adresa','A toggle that allows you to pick a value of 1 or 0 (on or off, true or false, etc). Can be presented as a stylized switch or checkbox.'=>'Přepínač, který umožňuje vybrat hodnotu 1 nebo 0 (zapnuto nebo vypnuto, pravda nebo nepravda atd.). Může být prezentován jako stylizovaný přepínač nebo zaškrtávací políčko.','An interactive UI for picking a time. The time format can be customized using the field settings.'=>'Interaktivní uživatelské rozhraní pro výběr času. Formát času lze přizpůsobit pomocí nastavení pole.','A basic textarea input for storing paragraphs of text.'=>'Základní textové pole pro ukládání odstavců textu.','A basic text input, useful for storing single string values.'=>'Základní textové pole užitečné pro ukládání jednoslovných textových hodnot.','Allows the selection of one or more taxonomy terms based on the criteria and options specified in the fields settings.'=>'Umožňuje výběr jednoho nebo více pojmů taxonomie na základě kritérií a možností uvedených v nastavení polí.','Allows you to group fields into tabbed sections in the edit screen. Useful for keeping fields organized and structured.'=>'Umožňuje seskupit pole do sekcí s kartami na obrazovce úprav. Užitečné pro udržení přehlednosti a struktury polí.','A dropdown list with a selection of choices that you specify.'=>'Rozbalovací seznam s výběrem možností, které zadáte.','A dual-column interface to select one or more posts, pages, or custom post type items to create a relationship with the item that you\'re currently editing. Includes options to search and filter.'=>'Rozhraní se dvěma sloupci, které umožňuje vybrat jeden nebo více příspěvků, stránek nebo uživatelských typů obsahu a vytvořit vztah s položkou, kterou právě upravujete. Obsahuje možnosti vyhledávání a filtrování.','An input for selecting a numerical value within a specified range using a range slider element.'=>'Vstupní pole pro výběr číselné hodnoty v zadaném rozsahu pomocí posuvného prvku rozsahu.','A group of radio button inputs that allows the user to make a single selection from values that you specify.'=>'Skupina s přepínači, která umožňuje uživateli výběr jedné z hodnot, které zadáte.','An interactive and customizable UI for picking one or many posts, pages or post type items with the option to search. '=>'Interaktivní a přizpůsobitelné uživatelské rozhraní pro výběr jednoho či více příspěvků, stránek nebo typů obsahu s možností vyhledávání. ','An input for providing a password using a masked field.'=>'Vstup pro zadání hesla pomocí maskovaného pole.','Filter by Post Status'=>'Filtrovat podle stavu příspěvku','An interactive dropdown to select one or more posts, pages, custom post type items or archive URLs, with the option to search.'=>'Interaktivní rozbalovací seznam pro výběr jednoho nebo více příspěvků, stránek, uživatelských typů obsahu nebo adres URL archivu s možností vyhledávání.','An interactive component for embedding videos, images, tweets, audio and other content by making use of the native WordPress oEmbed functionality.'=>'Interaktivní komponenta pro vkládání videí, obrázků, tweetů, zvuku a dalšího obsahu s využitím nativní funkce WordPress oEmbed.','An input limited to numerical values.'=>'Vstup omezený na číselné hodnoty.','Used to display a message to editors alongside other fields. Useful for providing additional context or instructions around your fields.'=>'Slouží k zobrazení zprávy pro editory vedle jiných polí. Užitečné pro poskytnutí dalšího kontextu nebo pokynů k polím.','Allows you to specify a link and its properties such as title and target using the WordPress native link picker.'=>'Umožňuje zadat odkaz a jeho vlastnosti jako jsou text odkazu a jeho cíl pomocí nativního nástroje pro výběr odkazů ve WordPressu.','Uses the native WordPress media picker to upload, or choose images.'=>'K nahrávání nebo výběru obrázků používá nativní nástroj pro výběr médií ve WordPressu.','Provides a way to structure fields into groups to better organize the data and the edit screen.'=>'Umožňuje strukturovat pole do skupin a lépe tak uspořádat data a obrazovku úprav.','An interactive UI for selecting a location using Google Maps. Requires a Google Maps API key and additional configuration to display correctly.'=>'Interaktivní uživatelské rozhraní pro výběr místa pomocí Map Google. Pro správné zobrazení vyžaduje klíč Google Maps API a další konfiguraci.','Uses the native WordPress media picker to upload, or choose files.'=>'K nahrávání nebo výběru souborů používá nativní nástroj pro výběr médií ve WordPressu.','A text input specifically designed for storing email addresses.'=>'Textové pole určené speciálně pro ukládání e-mailových adres.','An interactive UI for picking a date and time. The date return format can be customized using the field settings.'=>'Interaktivní uživatelské rozhraní pro výběr data a času. Formát vráceného data lze přizpůsobit pomocí nastavení pole.','An interactive UI for picking a date. The date return format can be customized using the field settings.'=>'Interaktivní uživatelské rozhraní pro výběr data. Formát vráceného data lze přizpůsobit pomocí nastavení pole.','An interactive UI for selecting a color, or specifying a Hex value.'=>'Interaktivní uživatelské rozhraní pro výběr barvy nebo zadání hodnoty Hex.','A group of checkbox inputs that allow the user to select one, or multiple values that you specify.'=>'Skupina zaškrtávacích políček, která umožňují uživateli vybrat jednu nebo více zadaných hodnot.','A group of buttons with values that you specify, users can choose one option from the values provided.'=>'Skupina tlačítek s předdefinovanými hodnotami. Uživatelé mohou vybrat jednu možnost z uvedených hodnot.','Allows you to group and organize custom fields into collapsable panels that are shown while editing content. Useful for keeping large datasets tidy.'=>'Umožňuje seskupit a uspořádat vlastní pole do skládacích panelů, které se zobrazují při úpravách obsahu. Užitečné pro udržování pořádku ve velkých souborech dat.','This provides a solution for repeating content such as slides, team members, and call-to-action tiles, by acting as a parent to a set of subfields which can be repeated again and again.'=>'Nabízí řešení pro opakování obsahu, jako jsou snímky, členové týmu a dlaždice s výzvou k akci, tím, že funguje jako nadřazené pole pro sadu podpolí, která lze opakovat znovu a znovu.','This provides an interactive interface for managing a collection of attachments. Most settings are similar to the Image field type. Additional settings allow you to specify where new attachments are added in the gallery and the minimum/maximum number of attachments allowed.'=>'Poskytuje interaktivní rozhraní pro správu sbírky příloh. Většina nastavení je podobná typu pole Obrázek. Další nastavení umožňují určit, kam se budou v galerii přidávat nové přílohy, a minimální/maximální povolený počet příloh.','This provides a simple, structured, layout-based editor. The Flexible Content field allows you to define, create and manage content with total control by using layouts and subfields to design the available blocks.'=>'Poskytuje jednoduchý, strukturovaný editor založený na rozvržení. Pole Flexibilní obsah umožňuje definovat, vytvářet a spravovat obsah s naprostou kontrolou pomocí rozvržení a podpolí pro návrh dostupných bloků.','This allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields.'=>'Umožňuje vybrat a zobrazit existující pole. Neduplikuje žádná pole v databázi, ale načítá a zobrazuje vybraná pole za běhu. Pole Klonování se může buď nahradit vybranými poli, nebo zobrazit vybraná pole jako skupinu podpolí.','nounClone'=>'Klonování','PRO'=>'PRO','Advanced'=>'Pokročilé','JSON (newer)'=>'JSON (novější)','Original'=>'Původní','Invalid post ID.'=>'Neplatné ID příspěvku.','Invalid post type selected for review.'=>'Ke kontrole byl vybrán neplatný typ obsahu.','More'=>'Více','Tutorial'=>'Tutoriál','Select Field'=>'Vybrat pole','Try a different search term or browse %s'=>'Zkuste použít jiný vyhledávací výraz nebo projít %s','Popular fields'=>'Oblíbená pole','No search results for \'%s\''=>'Žádné výsledky hledání pro „%s“','Search fields...'=>'Hledat pole...','Select Field Type'=>'Výběr typu pole','Popular'=>'Oblíbená','Add Taxonomy'=>'Přidat taxonomii','Create custom taxonomies to classify post type content'=>'Vytvořte vlastní taxonomie pro klasifikaci obsahu','Add Your First Taxonomy'=>'Přidejte první taxonomii','Hierarchical taxonomies can have descendants (like categories).'=>'Hierarchické taxonomie mohou mít potomky (stejně jako rubriky).','Makes a taxonomy visible on the frontend and in the admin dashboard.'=>'Zviditelní taxonomii ve frontendu a na nástěnce správce.','One or many post types that can be classified with this taxonomy.'=>'Jeden nebo více typů obsahu, které lze klasifikovat pomocí této taxonomie.','genre'=>'žánr','Genre'=>'Žánr','Genres'=>'Žánry','Optional custom controller to use instead of `WP_REST_Terms_Controller `.'=>'Volitelný kontrolér, který se použije místo `WP_REST_Terms_Controller`.','Expose this post type in the REST API.'=>'Zveřejněte tento typ obsahu v rozhraní REST API.','Customize the query variable name'=>'Přizpůsobení názvu proměnné dotazu','Terms can be accessed using the non-pretty permalink, e.g., {query_var}={term_slug}.'=>'K pojmům lze přistupovat pomocí nepěkného trvalého odkazu, např. {query_var}={term_slug}.','Parent-child terms in URLs for hierarchical taxonomies.'=>'Nadřazené a podřazené pojmy v adresách URL pro hierarchické taxonomie.','Customize the slug used in the URL'=>'Přizpůsobení názvu použitém v adrese URL','Permalinks for this taxonomy are disabled.'=>'Trvalé odkazy pro tuto taxonomii jsou zakázány.','Rewrite the URL using the taxonomy key as the slug. Your permalink structure will be'=>'Přepište adresu URL pomocí klíče taxonomie jako názvu v URL. Struktura trvalého odkazu bude následující','Taxonomy Key'=>'Klíč taxonomie','Select the type of permalink to use for this taxonomy.'=>'Vyberte typ trvalého odkazu, který chcete pro tuto taxonomii použít.','Display a column for the taxonomy on post type listing screens.'=>'Zobrazení sloupce pro taxonomii na obrazovkách s výpisem typů obsahu.','Show Admin Column'=>'Zobrazit sloupec správce','Show the taxonomy in the quick/bulk edit panel.'=>'Zobrazení taxonomie v panelu rychlých/hromadných úprav.','Quick Edit'=>'Rychlé úpravy','List the taxonomy in the Tag Cloud Widget controls.'=>'Uveďte taxonomii v ovládacích prvcích bloku Shluk štítků.','Tag Cloud'=>'Shluk štítků','Meta Box Sanitization Callback'=>'Callback pro sanitaci metaboxu','A PHP function name to be called to handle the content of a meta box on your taxonomy.'=>'Název funkce PHP, která se volá pro zpracování obsahu metaboxu v taxonomii.','Register Meta Box Callback'=>'Registrovat callback metaboxu','No Meta Box'=>'Žádný metabox','Custom Meta Box'=>'Vlastní metabox','Controls the meta box on the content editor screen. By default, the Categories meta box is shown for hierarchical taxonomies, and the Tags meta box is shown for non-hierarchical taxonomies.'=>'Ovládá pole meta na obrazovce editoru obsahu. Ve výchozím nastavení se u hierarchických taxonomií zobrazuje metabox Rubriky a u nehierarchických taxonomií metabox Štítky.','Meta Box'=>'Metabox','Categories Meta Box'=>'Metabox pro rubriky','Tags Meta Box'=>'Metabox pro štítky','A link to a tag'=>'Odkaz na štítek','Describes a navigation link block variation used in the block editor.'=>'Popisuje variantu bloku navigačního odkazu použitou v editoru bloků.','A link to a %s'=>'Odkaz na %s','Tag Link'=>'Odkaz štítku','Assigns a title for navigation link block variation used in the block editor.'=>'Přiřadí název pro variantu bloku navigačního odkazu použitou v editoru bloků.','← Go to tags'=>'← Přejít na štítky','Assigns the text used to link back to the main index after updating a term.'=>'Přiřadí text, který se po aktualizaci pojmu použije k odkazu zpět na hlavní rejstřík.','Back To Items'=>'Zpět na položky','← Go to %s'=>'← Přejít na %s','Tags list'=>'Seznam štítků','Assigns text to the table hidden heading.'=>'Přiřadí text skrytému záhlaví tabulky.','Tags list navigation'=>'Navigace v seznamu štítků','Assigns text to the table pagination hidden heading.'=>'Přiřadí text skrytému záhlaví stránkování tabulky.','Filter by category'=>'Filtrovat podle rubriky','Assigns text to the filter button in the posts lists table.'=>'Přiřadí text tlačítku filtru v tabulce seznamů příspěvků.','Filter By Item'=>'Filtrovat podle položky','Filter by %s'=>'Filtrovat podle %s','The description is not prominent by default; however, some themes may show it.'=>'Popis není ve výchozím nastavení viditelný, některé šablony jej však mohou zobrazovat.','Describes the Description field on the Edit Tags screen.'=>'Popisuje pole Popis na obrazovce Upravit štítky.','Description Field Description'=>'Popis pole Popis','Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band'=>'Přiřazením nadřazeného pojmu vytvoříte hierarchii. Například pojem Jazz bude nadřazený výrazům Bebop a Big Band.','Describes the Parent field on the Edit Tags screen.'=>'Popisuje pole Nadřazený na obrazovce Upravit štítky.','Parent Field Description'=>'Popis pole Nadřazený','The "slug" is the URL-friendly version of the name. It is usually all lower case and contains only letters, numbers, and hyphens.'=>'Název v URL je verze vhodná pro adresy URL. Obvykle se píše malými písmeny a obsahuje pouze písmena bez diakritiky, číslice a pomlčky.','Describes the Slug field on the Edit Tags screen.'=>'Popisuje pole Název v URL na obrazovce Upravit štítky.','Slug Field Description'=>'Popis pole Název v URL','The name is how it appears on your site'=>'Název se bude v této podobě zobrazovat na webu','Describes the Name field on the Edit Tags screen.'=>'Popisuje pole Název na obrazovce Upravit štítky.','Name Field Description'=>'Popis pole Název','No tags'=>'Žádné štítky','Assigns the text displayed in the posts and media list tables when no tags or categories are available.'=>'Přiřazuje text zobrazený v tabulkách seznamů příspěvků a médií, pokud nejsou k dispozici žádné štítky nebo rubriky.','No Terms'=>'Žádné pojmy','No %s'=>'Žádné %s','No tags found'=>'Nebyly nalezeny žádné štítky','Assigns the text displayed when clicking the \'choose from most used\' text in the taxonomy meta box when no tags are available, and assigns the text used in the terms list table when there are no items for a taxonomy.'=>'Přiřazuje text zobrazený po kliknutí na text „zvolit z nejpoužívanějších“ v metaboxu taxonomie, pokud nejsou k dispozici žádné štítky, a přiřazuje text použitý v tabulce seznamu pojmů, pokud pro taxonomii neexistují žádné položky.','Not Found'=>'Nenalezeno','Assigns text to the Title field of the Most Used tab.'=>'Přiřadí text do pole Název na kartě Nejpoužívanější.','Most Used'=>'Nejčastější','Choose from the most used tags'=>'Vyberte si z nejpoužívanějších štítků','Assigns the \'choose from most used\' text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies.'=>'Přiřazuje text pro „zvolit z nejpoužívanějších“, který se používá v metaboxu při vypnutém JavaScriptu. Používá se pouze u nehierarchických taxonomií.','Choose From Most Used'=>'Zvolit z nejpoužívanějších','Choose from the most used %s'=>'Vyberte si z nejpoužívanějších %s','Add or remove tags'=>'Přidat nebo odebrat štítky','Assigns the add or remove items text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies'=>'Přiřazuje text přidávání nebo odebírání položek použitý v metaboxu při vypnutém JavaScriptu. Používá se pouze u nehierarchických taxonomií.','Add Or Remove Items'=>'Přidat nebo odstranit položky','Add or remove %s'=>'Přidat nebo odstranit %s','Separate tags with commas'=>'Více štítků oddělte čárkami','Assigns the separate item with commas text used in the taxonomy meta box. Only used on non-hierarchical taxonomies.'=>'Přiřadí text pro oddělení položek čárkami používaný v metaboxu. Používá se pouze u nehierarchických taxonomií.','Separate Items With Commas'=>'Oddělujte položky čárkami','Separate %s with commas'=>'Oddělte %s čárkami','Popular Tags'=>'Oblíbené štítky','Assigns popular items text. Only used for non-hierarchical taxonomies.'=>'Přiřadí text pro oblíbené položky. Používá se pouze pro nehierarchické taxonomie.','Popular Items'=>'Oblíbené položky','Popular %s'=>'Oblíbené %s','Search Tags'=>'Hledat štítky','Assigns search items text.'=>'Přiřadí text pro hledání položek.','Parent Category:'=>'Nadřazená rubrika:','Assigns parent item text, but with a colon (:) added to the end.'=>'Přiřadí text nadřazené položky, ale s dvojtečkou (:) na konci.','Parent Item With Colon'=>'Nadřazená položka s dvojtečkou','Parent Category'=>'Nadřazená rubrika','Assigns parent item text. Only used on hierarchical taxonomies.'=>'Přiřadí text nadřazené položky. Používá se pouze u hierarchických taxonomií.','Parent Item'=>'Nadřazená položka','Parent %s'=>'Nadřazený %s','New Tag Name'=>'Název nového štítku','Assigns the new item name text.'=>'Přiřadí text pro název nové položky.','New Item Name'=>'Název nové položky','New %s Name'=>'Název nového %s','Add New Tag'=>'Vytvořit nový štítek','Assigns the add new item text.'=>'Přiřadí text pro vytvoření nové položky.','Update Tag'=>'Aktualizovat štítek','Assigns the update item text.'=>'Přiřadí text pro aktualizaci položky.','Update Item'=>'Aktualizovat položku','Update %s'=>'Aktualizovat %s','View Tag'=>'Zobrazit štítek','In the admin bar to view term during editing.'=>'Na panelu správce pro zobrazení pojmu během úprav.','Edit Tag'=>'Upravit štítek','At the top of the editor screen when editing a term.'=>'V horní části obrazovky editoru při úpravě položky.','All Tags'=>'Všechny štítky','Assigns the all items text.'=>'Přiřadí text pro všechny položky.','Assigns the menu name text.'=>'Přiřadí text názvu menu.','Menu Label'=>'Označení menu','Active taxonomies are enabled and registered with WordPress.'=>'Aktivní taxonomie jsou povoleny a zaregistrovány ve WordPressu.','A descriptive summary of the taxonomy.'=>'Popisné shrnutí taxonomie.','A descriptive summary of the term.'=>'Popisné shrnutí pojmu.','Term Description'=>'Popis pojmu','Single word, no spaces. Underscores and dashes allowed.'=>'Jedno slovo, bez mezer. Podtržítka a pomlčky jsou povoleny.','Term Slug'=>'Název pojmu v URL','The name of the default term.'=>'Název výchozího pojmu.','Term Name'=>'Název pojmu','Create a term for the taxonomy that cannot be deleted. It will not be selected for posts by default.'=>'Vytvoření pojmu pro taxonomii, který nelze odstranit. Ve výchozím nastavení nebude vybrán pro příspěvky.','Default Term'=>'Výchozí pojem','Whether terms in this taxonomy should be sorted in the order they are provided to `wp_set_object_terms()`.'=>'Zda mají být pojmy v této taxonomii seřazeny v pořadí, v jakém byly zadány do `wp_set_object_terms()`.','Sort Terms'=>'Řadit pojmy','Add Post Type'=>'Přidat typ obsahu','Expand the functionality of WordPress beyond standard posts and pages with custom post types.'=>'Rozšiřte funkčnost WordPressu nad rámec standardních příspěvků a stránek pomocí vlastních typů obsahu.','Add Your First Post Type'=>'Přidejte první typ obsahu','I know what I\'m doing, show me all the options.'=>'Vím, co dělám, ukažte mi všechny možnosti.','Advanced Configuration'=>'Pokročilá konfigurace','Hierarchical post types can have descendants (like pages).'=>'Hierarchické typy obsahu mohou mít potomky (stejně jako stránky).','Hierarchical'=>'Hierarchické','Visible on the frontend and in the admin dashboard.'=>'Je viditelný ve frontendu a na nástěnce správce.','Public'=>'Veřejné','movie'=>'film','Lower case letters, underscores and dashes only, Max 20 characters.'=>'Pouze malá písmena, podtržítka a pomlčky, max. 20 znaků.','Movie'=>'Film','Singular Label'=>'Štítek v jednotném čísle','Movies'=>'Filmy','Plural Label'=>'Štítek pro množné číslo','Optional custom controller to use instead of `WP_REST_Posts_Controller`.'=>'Volitelný kontrolér, který se použije místo `WP_REST_Posts_Controller`.','Controller Class'=>'Třída kontroléru','The namespace part of the REST API URL.'=>'Část adresy URL rozhraní REST API obsahující jmenný prostor.','Namespace Route'=>'Cesta jmenného prostoru','The base URL for the post type REST API URLs.'=>'Základní URL pro adresy daného typu obsahu v rozhraní REST API.','Base URL'=>'Základní URL','Exposes this post type in the REST API. Required to use the block editor.'=>'Zveřejní tento typ obsahu v rozhraní REST API. Vyžadováno pro použití editoru bloků.','Show In REST API'=>'Zobrazit v rozhraní REST API','Customize the query variable name.'=>'Přizpůsobte název proměnné dotazu.','Query Variable'=>'Proměnná dotazu','No Query Variable Support'=>'Chybějící podpora proměnné dotazu','Custom Query Variable'=>'Uživatelská proměnná dotazu','Items can be accessed using the non-pretty permalink, eg. {post_type}={post_slug}.'=>'K položkám lze přistupovat pomocí nepěkného trvalého odkazu, např. {post_type}={post_slug}.','Query Variable Support'=>'Podpora proměnné dotazu','URLs for an item and items can be accessed with a query string.'=>'K adresám URL položky a položek lze přistupovat pomocí řetězce dotazů.','Publicly Queryable'=>'Veřejně dotazovatelné','Custom slug for the Archive URL.'=>'Vlastní název v adrese URL archivu.','Archive Slug'=>'Název archivu v URL','Has an item archive that can be customized with an archive template file in your theme.'=>'Má archiv položek, který lze přizpůsobit pomocí souboru šablony archivu v šabloně webu.','Archive'=>'Archiv','Pagination support for the items URLs such as the archives.'=>'Podpora stránkování pro adresy URL položek jako jsou archivy.','Pagination'=>'Stránkování','RSS feed URL for the post type items.'=>'Adresa URL zdroje RSS pro položky daného typu obsahu.','Feed URL'=>'Adresa URL zdroje','Alters the permalink structure to add the `WP_Rewrite::$front` prefix to URLs.'=>'Změní strukturu trvalých odkazů tak, že do adres URL přidá předponu `WP_Rewrite::$front`.','Front URL Prefix'=>'Předpona URL','Customize the slug used in the URL.'=>'Přizpůsobte název, který se používá v adrese URL.','URL Slug'=>'Název v URL','Permalinks for this post type are disabled.'=>'Trvalé odkazy pro tento typ obsahu jsou zakázány.','Rewrite the URL using a custom slug defined in the input below. Your permalink structure will be'=>'Přepište adresu URL pomocí názvu definovaném v poli níže. Struktura trvalého odkazu bude následující','No Permalink (prevent URL rewriting)'=>'Žádný trvalý odkaz (zabránění přepisování URL)','Custom Permalink'=>'Uživatelský trvalý odkaz','Post Type Key'=>'Klíč typu obsahu','Rewrite the URL using the post type key as the slug. Your permalink structure will be'=>'Přepište adresu URL pomocí klíče typu obsahu jako názvu v URL. Struktura trvalého odkazu bude následující','Permalink Rewrite'=>'Přepsání trvalého odkazu','Delete items by a user when that user is deleted.'=>'Smazat položky uživatele při jeho smazání.','Delete With User'=>'Smazat s uživatelem','Allow the post type to be exported from \'Tools\' > \'Export\'.'=>'Povolte exportování typu obsahu z Nástroje > Export.','Can Export'=>'Může exportovat','Optionally provide a plural to be used in capabilities.'=>'Volitelně uveďte množné číslo, které se použije pro oprávnění.','Plural Capability Name'=>'Název oprávnění v množném čísle','Choose another post type to base the capabilities for this post type.'=>'Zvolte jiný typ obsahu, z něhož budou odvozeny oprávnění pro tento typ obsahu.','Singular Capability Name'=>'Název oprávnění v jednotném čísle','By default the capabilities of the post type will inherit the \'Post\' capability names, eg. edit_post, delete_posts. Enable to use post type specific capabilities, eg. edit_{singular}, delete_{plural}.'=>'Ve výchozím nastavení zdědí názvy oprávnění z typu „Příspěvek“, např. edit_post, delete_posts. Povolte pro použití oprávnění specifických pro daný typ obsahu, např. edit_{singular}, delete_{plural}.','Rename Capabilities'=>'Přejmenovat oprávnění','Exclude From Search'=>'Vyloučit z vyhledávání','Allow items to be added to menus in the \'Appearance\' > \'Menus\' screen. Must be turned on in \'Screen options\'.'=>'Povolení přidávání položek do menu na obrazovce Vzhled > Menu. Musí být zapnuto v Nastavení zobrazených informací.','Appearance Menus Support'=>'Podpora menu Vzhled','Appears as an item in the \'New\' menu in the admin bar.'=>'Zobrazí se jako položka v menu „Akce“ na panelu správce.','Show In Admin Bar'=>'Zobrazit na panelu správce','A PHP function name to be called when setting up the meta boxes for the edit screen.'=>'Název funkce PHP, která se volá při nastavování metaboxů pro obrazovku úprav.','Custom Meta Box Callback'=>'Vlastní callback metaboxu','Menu Icon'=>'Ikona menu','The position in the sidebar menu in the admin dashboard.'=>'Pozice v menu postranního panelu na nástěnce správce.','Menu Position'=>'Pozice menu','By default the post type will get a new top level item in the admin menu. If an existing top level item is supplied here, the post type will be added as a submenu item under it.'=>'Ve výchozím nastavení získá typ obsahu novou položku nejvyšší úrovně v menu správce. Pokud je zde uvedena existující položka nejvyšší úrovně, typ obsahu bude přidán jako položka podřazená pod ni.','Admin Menu Parent'=>'Nadřazené menu','Admin editor navigation in the sidebar menu.'=>'Navigace v editoru správce v menu postranního panelu.','Show In Admin Menu'=>'Zobrazit v menu správce','Items can be edited and managed in the admin dashboard.'=>'Položky lze upravovat a spravovat na nástěnce správce.','Show In UI'=>'Zobrazit v uživatelském rozhraní','A link to a post.'=>'Odkaz na příspěvek.','Description for a navigation link block variation.'=>'Popis varianty bloku navigačního odkazu.','Item Link Description'=>'Popis odkazu položky','A link to a %s.'=>'Odkaz na %s.','Post Link'=>'Odkaz příspěvku','Title for a navigation link block variation.'=>'Název varianty bloku navigačního odkazu.','Item Link'=>'Odkaz položky','%s Link'=>'Odkaz na %s','Post updated.'=>'Příspěvek byl aktualizován.','In the editor notice after an item is updated.'=>'V oznámení editoru po aktualizaci položky.','Item Updated'=>'Položka byla aktualizována','%s updated.'=>'%s aktualizován.','Post scheduled.'=>'Příspěvek byl naplánován.','In the editor notice after scheduling an item.'=>'V oznámení editoru po naplánování položky.','Item Scheduled'=>'Položka naplánována','%s scheduled.'=>'%s byl naplánován.','Post reverted to draft.'=>'Příspěvek byl převeden na koncept.','In the editor notice after reverting an item to draft.'=>'V oznámení editoru po převedení položky na koncept.','Item Reverted To Draft'=>'Položka převedena na koncept','%s reverted to draft.'=>'%s byl převeden na koncept.','Post published privately.'=>'Příspěvek byl publikován soukromě.','In the editor notice after publishing a private item.'=>'V oznámení editoru po publikování položky soukromě.','Item Published Privately'=>'Položka publikována soukromě','%s published privately.'=>'%s byl publikován soukromě.','Post published.'=>'Příspěvek byl publikován.','In the editor notice after publishing an item.'=>'V oznámení editoru po publikování položky.','Item Published'=>'Položka publikována','%s published.'=>'%s publikován.','Posts list'=>'Seznam příspěvků','Used by screen readers for the items list on the post type list screen.'=>'Používá se čtečkami obrazovky na obrazovce se seznamem položek daného typu obsahu.','Items List'=>'Seznam položek','%s list'=>'Seznam %s','Posts list navigation'=>'Navigace v seznamu příspěvků','Used by screen readers for the filter list pagination on the post type list screen.'=>'Používá se čtečkami obrazovky pro stránkování filtru na obrazovce se seznamem typů obsahu.','Items List Navigation'=>'Navigace v seznamu položek','%s list navigation'=>'Navigace v seznamu %s','Filter posts by date'=>'Filtrovat příspěvky podle data','Used by screen readers for the filter by date heading on the post type list screen.'=>'Používá se čtečkami obrazovky pro filtr podle data na obrazovce se seznamem typů obsahu.','Filter Items By Date'=>'Filtrovat položky podle data','Filter %s by date'=>'Filtrovat %s podle data','Filter posts list'=>'Filtrovat seznam příspěvků','Used by screen readers for the filter links heading on the post type list screen.'=>'Používá se čtečkami obrazovky pro odkazy filtru na obrazovce se seznamem typů obsahu.','Filter Items List'=>'Filtrovat seznam položek','Filter %s list'=>'Filtrovat seznam %s','In the media modal showing all media uploaded to this item.'=>'V modálním okně médií se zobrazí všechna média nahraná k této položce.','Uploaded To This Item'=>'Nahrané k této položce','Uploaded to this %s'=>'Nahráno do tohoto %s','Insert into post'=>'Vložit do příspěvku','As the button label when adding media to content.'=>'Jako popisek tlačítka při přidávání médií do obsahu.','Insert Into Media Button'=>'Tlačítko pro vložení médií','Insert into %s'=>'Vložit do %s','Use as featured image'=>'Použít jako náhledový obrázek','As the button label for selecting to use an image as the featured image.'=>'Jako popisek tlačítka pro výběr použití obrázku jako náhledového.','Use Featured Image'=>'Použít náhledový obrázek','Remove featured image'=>'Odstranit náhledový obrázek','As the button label when removing the featured image.'=>'Jako popisek tlačítka při odebrání náhledového obrázku.','Remove Featured Image'=>'Odstranit náhledový obrázek','Set featured image'=>'Zvolit náhledový obrázek','As the button label when setting the featured image.'=>'Jako popisek tlačítka při nastavení náhledového obrázku.','Set Featured Image'=>'Zvolit náhledový obrázek','Featured image'=>'Náhledový obrázek','In the editor used for the title of the featured image meta box.'=>'V editoru používán pro název metaboxu náhledového obrázku.','Featured Image Meta Box'=>'Metabox náhledového obrázku','Post Attributes'=>'Vlastnosti příspěvku','In the editor used for the title of the post attributes meta box.'=>'V editoru použitý pro název metaboxu s vlastnostmi příspěvku.','Attributes Meta Box'=>'Metabox pro vlastnosti','%s Attributes'=>'Vlastnosti %s','Post Archives'=>'Archivy příspěvků','Adds \'Post Type Archive\' items with this label to the list of posts shown when adding items to an existing menu in a CPT with archives enabled. Only appears when editing menus in \'Live Preview\' mode and a custom archive slug has been provided.'=>'Přidá položky „Archiv typu obsahu“ s tímto označením do seznamu příspěvků zobrazených při přidávání položek do existujícího menu v CPT s povolenými archivy. Zobrazuje se pouze při úpravách menu v režimu „Aktuální náhled“ a při zadání uživatelského názvu archivu v URL.','Archives Nav Menu'=>'Navigační menu archivu','%s Archives'=>'Archivy pro %s','No posts found in Trash'=>'V koši nebyly nalezeny žádné příspěvky','At the top of the post type list screen when there are no posts in the trash.'=>'V horní části obrazovky seznamu příspěvků daného typu, když v koši nejsou žádné příspěvky.','No Items Found in Trash'=>'V koši nebyly nalezeny žádné položky','No %s found in Trash'=>'V koši nebyly nalezeny žádné %s','No posts found'=>'Nebyly nalezeny žádné příspěvky','At the top of the post type list screen when there are no posts to display.'=>'V horní části obrazovky seznamu příspěvků daného typu, když nejsou žádné příspěvky k zobrazení.','No Items Found'=>'Nebyly nalezeny žádné položky','No %s found'=>'Nenalezeny žádné položky typu %s','Search Posts'=>'Hledat příspěvky','At the top of the items screen when searching for an item.'=>'V horní části obrazovky s položkami při hledání položky.','Search Items'=>'Hledat položky','Search %s'=>'Hledat %s','Parent Page:'=>'Nadřazená stránka:','For hierarchical types in the post type list screen.'=>'Pro hierarchické typy na obrazovce se seznamem typů obsahu.','Parent Item Prefix'=>'Předpona nadřazené položky','Parent %s:'=>'Nadřazené %s:','New Post'=>'Nový příspěvek','New Item'=>'Nová položka','New %s'=>'Nový %s','Add New Post'=>'Vytvořit příspěvek','At the top of the editor screen when adding a new item.'=>'V horní části obrazovky editoru při vytváření nové položky.','Add New Item'=>'Vytvořit novou položku','Add New %s'=>'Přidat nový %s','View Posts'=>'Zobrazit příspěvky','Appears in the admin bar in the \'All Posts\' view, provided the post type supports archives and the home page is not an archive of that post type.'=>'Zobrazí se na panelu správce v zobrazení „Přehled příspěvků“, pokud typ obsahu podporuje archivy a domovská stránka není archivem daného typu obsahu.','View Items'=>'Zobrazit položky','View Post'=>'Zobrazit příspěvek','In the admin bar to view item when editing it.'=>'Na panelu správce pro zobrazení položky při její úpravě.','View Item'=>'Zobrazit položku','View %s'=>'Zobrazit %s','Edit Post'=>'Upravit příspěvek','At the top of the editor screen when editing an item.'=>'V horní části obrazovky editoru při úpravě položky.','Edit Item'=>'Upravit položku','Edit %s'=>'Upravit %s','All Posts'=>'Přehled příspěvků','In the post type submenu in the admin dashboard.'=>'V podmenu typu obsahu na nástěnce správce.','All Items'=>'Všechny položky','All %s'=>'Přehled %s','Admin menu name for the post type.'=>'Název menu správce pro daný typ obsahu.','Menu Name'=>'Název menu','Regenerate all labels using the Singular and Plural labels'=>'Přegenerovat všechny štítky pomocí štítků pro jednotné a množné číslo','Regenerate'=>'Přegenerovat','Active post types are enabled and registered with WordPress.'=>'Aktivní typy obsahu jsou povoleny a zaregistrovány ve WordPressu.','A descriptive summary of the post type.'=>'Popisné shrnutí typu obsahu.','Add Custom'=>'Přidat vlastní','Enable various features in the content editor.'=>'Povolte různé funkce v editoru obsahu.','Post Formats'=>'Formáty příspěvků','Editor'=>'Editor','Trackbacks'=>'Trackbacky','Select existing taxonomies to classify items of the post type.'=>'Vyberte existující taxonomie pro klasifikaci položek daného typu obsahu.','Browse Fields'=>'Procházet pole','Nothing to import'=>'Nic k importu','. The Custom Post Type UI plugin can be deactivated.'=>'. Plugin Custom Post Type UI lze deaktivovat.','Imported %d item from Custom Post Type UI -'=>'Importována %d položka z Custom Post Type UI -' . "\0" . 'Importovány %d položky z Custom Post Type UI -' . "\0" . 'Importováno %d položek z Custom Post Type UI -','Failed to import taxonomies.'=>'Nepodařilo se importovat taxonomie.','Failed to import post types.'=>'Nepodařilo se importovat typy obsahu.','Nothing from Custom Post Type UI plugin selected for import.'=>'Nic z pluginu Custom Post Type UI vybráno pro import.','Imported 1 item'=>'Importována 1 položka' . "\0" . 'Importovány %s položky' . "\0" . 'Importováno %s položek','Importing a Post Type or Taxonomy with the same key as one that already exists will overwrite the settings for the existing Post Type or Taxonomy with those of the import.'=>'Importování typu obsahu nebo taxonomie se stejným klíčem jako u již existujícího typu obsahu nebo taxonomie přepíše nastavení existujícího typu obsahu nebo taxonomie těmi z importu.','Import from Custom Post Type UI'=>'Import z pluginu Custom Post Type UI','The following code can be used to register a local version of the selected items. Storing field groups, post types, or taxonomies locally can provide many benefits such as faster load times, version control & dynamic fields/settings. Simply copy and paste the following code to your theme\'s functions.php file or include it within an external file, then deactivate or delete the items from the ACF admin.'=>'Následující kód lze použít k registraci místní verze vybraných položek. Lokální uložení skupin polí, typů obsahu nebo taxonomií může přinést mnoho výhod, například rychlejší načítání, správu verzí a dynamická pole/nastavení. Jednoduše zkopírujte a vložte následující kód do souboru šablony functions.php nebo jej zahrňte do externího souboru a poté deaktivujte nebo odstraňte položky z administrace ACF.','Export - Generate PHP'=>'Exportovat - Vytvořit PHP','Export'=>'Export','Select Taxonomies'=>'Vybrat taxonomie','Select Post Types'=>'Vybrat typy obsahu','Exported 1 item.'=>'Exportována 1 položka.' . "\0" . 'Exportovány %s položky.' . "\0" . 'Exportováno %s položek.','Category'=>'Rubrika','Tag'=>'Štítek','%s taxonomy created'=>'Taxonomie %s vytvořena','%s taxonomy updated'=>'Taxonomie %s aktualizována','Taxonomy draft updated.'=>'Koncept taxonomie byl aktualizován.','Taxonomy scheduled for.'=>'Taxonomie byla naplánována.','Taxonomy submitted.'=>'Taxonomie odeslána.','Taxonomy saved.'=>'Taxonomie uložena.','Taxonomy deleted.'=>'Taxonomie smazána.','Taxonomy updated.'=>'Taxonomie aktualizována.','This taxonomy could not be registered because its key is in use by another taxonomy registered by another plugin or theme.'=>'Tuto taxonomii nebylo možné zaregistrovat, protože její klíč je používán jinou taxonomií registrovanou jiným pluginem nebo šablonou.','Taxonomy synchronized.'=>'Taxonomie synchronizována.' . "\0" . '%s taxonomie synchronizovány.' . "\0" . '%s taxonomií synchronizováno.','Taxonomy duplicated.'=>'Taxonomie duplikována.' . "\0" . '%s taxonomie duplikovány.' . "\0" . '%s taxonomií duplikováno.','Taxonomy deactivated.'=>'Taxonomie deaktivována.' . "\0" . '%s taxonomie deaktivovány.' . "\0" . '%s taxonomií deaktivováno.','Taxonomy activated.'=>'Taxonomie aktivována.' . "\0" . '%s taxonomie aktivovány.' . "\0" . '%s taxonomií aktivováno.','Terms'=>'Pojmy','Post type synchronized.'=>'Typ obsahu synchronizován.' . "\0" . '%s typy obsahu synchronizovány.' . "\0" . '%s typů obsahu synchronizováno.','Post type duplicated.'=>'Typ obsahu duplikován.' . "\0" . '%s typy obsahu duplikovány.' . "\0" . '%s typů obsahu duplikováno.','Post type deactivated.'=>'Typ obsahu deaktivován.' . "\0" . '%s typy obsahu deaktivovány.' . "\0" . '%s typů obsahu deaktivováno.','Post type activated.'=>'Typ obsahu aktivován.' . "\0" . '%s typy obsahu aktivovány.' . "\0" . '%s typů obsahu aktivováno.','Post Types'=>'Typy obsahu','Advanced Settings'=>'Pokročilá nastavení','Basic Settings'=>'Základní nastavení','This post type could not be registered because its key is in use by another post type registered by another plugin or theme.'=>'Tento typ obsahu nemohl být zaregistrován, protože jeho klíč je používán jiným typem obsahu registrovaným jiným pluginem nebo šablonou.','Pages'=>'Stránky','Link Existing Field Groups'=>'Propojení stávajících skupin polí','%s post type created'=>'Typ obsahu %s vytvořen','Add fields to %s'=>'Přidání polí do %s','%s post type updated'=>'Typ obsahu %s aktualizován','Post type draft updated.'=>'Koncept typu obsahu byl aktualizován.','Post type scheduled for.'=>'Typ obsahu byl naplánován.','Post type submitted.'=>'Typ obsahu byl odeslán.','Post type saved.'=>'Typ obsahu byl uložen.','Post type updated.'=>'Typ obsahu byl aktualizován.','Post type deleted.'=>'Typ obsahu smazán.','Type to search...'=>'Pište pro hledání...','PRO Only'=>'Pouze PRO','Field groups linked successfully.'=>'Skupiny polí byly úspěšně propojeny.','Import Post Types and Taxonomies registered with Custom Post Type UI and manage them with ACF. <a href="%s">Get Started</a>.'=>'Importujte typy obsahu a taxonomie registrované pomocí pluginu Custom Post Type UI a spravujte je s ACF. <a href="%s">Pusťme se do toho</a>.','ACF'=>'ACF','taxonomy'=>'taxonomie','post type'=>'typ obsahu','Done'=>'Hotovo','Field Group(s)'=>'Skupina(y) polí','Select one or many field groups...'=>'Vyberte jednu nebo více skupin polí...','Please select the field groups to link.'=>'Vyberte skupiny polí, které chcete propojit.','Field group linked successfully.'=>'Skupina polí úspěšně propojena.' . "\0" . 'Skupiny polí úspěšně propojeny.' . "\0" . 'Skupiny polí úspěšně propojeny.','post statusRegistration Failed'=>'Registrace se nezdařila','This item could not be registered because its key is in use by another item registered by another plugin or theme.'=>'Tuto položku nebylo možné zaregistrovat, protože její klíč je používán jinou položkou registrovanou jiným pluginem nebo šablonou.','REST API'=>'REST API','Permissions'=>'Oprávnění','URLs'=>'URL adresy','Visibility'=>'Viditelnost','Labels'=>'Štítky','Field Settings Tabs'=>'Karty nastavení pole','https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields'=>'https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields','[ACF shortcode value disabled for preview]'=>'[Hodnota zkráceného kódu ACF vypnuta pro náhled]','Close Modal'=>'Zavřít modální okno','Field moved to other group'=>'Pole přesunuto do jiné skupiny','Close modal'=>'Zavřít modální okno','Start a new group of tabs at this tab.'=>'Začněte novou skupinu karet na této kartě.','New Tab Group'=>'Nová skupina karet','Use a stylized checkbox using select2'=>'Použití stylizovaného zaškrtávacího políčka pomocí select2','Save Other Choice'=>'Uložit jinou volbu','Allow Other Choice'=>'Povolit jinou volbu','Add Toggle All'=>'Přidat Přepnout vše','Save Custom Values'=>'Uložit uživatelské hodnoty','Allow Custom Values'=>'Povolit uživatelské hodnoty','Checkbox custom values cannot be empty. Uncheck any empty values.'=>'Uživatelské hodnoty zaškrtávacího políčka nemohou být prázdné. Zrušte zaškrtnutí všech prázdných hodnot.','Updates'=>'Aktualizace','Advanced Custom Fields logo'=>'Logo Advanced Custom Fields','Save Changes'=>'Uložit změny','Field Group Title'=>'Název skupiny polí','Add title'=>'Zadejte název','New to ACF? Take a look at our <a href="%s" target="_blank">getting started guide</a>.'=>'Jste v ACF nováčkem? Podívejte se na našeho <a href="%s" target="_blank">průvodce pro začátečníky</a>.','Add Field Group'=>'Přidat skupinu polí','ACF uses <a href="%s" target="_blank">field groups</a> to group custom fields together, and then attach those fields to edit screens.'=>'ACF používá <a href="%s" target="_blank">skupiny polí</a> pro seskupení uživatelských polí a následné připojení těchto polí k obrazovkám úprav.','Add Your First Field Group'=>'Přidejte první skupinu polí','Options Pages'=>'Stránky konfigurace','ACF Blocks'=>'Bloky ACF','Gallery Field'=>'Pole Galerie','Flexible Content Field'=>'Pole Flexibilní obsah','Repeater Field'=>'Pole Opakovač','Unlock Extra Features with ACF PRO'=>'Odemkněte další funkce s ACF PRO','Delete Field Group'=>'Smazat skupinu polí','Created on %1$s at %2$s'=>'Vytvořeno %1$s v %2$s','Group Settings'=>'Nastavení skupiny','Location Rules'=>'Pravidla umístění','Choose from over 30 field types. <a href="%s" target="_blank">Learn more</a>.'=>'Zvolte si z více než 30 typů polí. <a href="%s" target="_blank">Zjistit více</a>.','Get started creating new custom fields for your posts, pages, custom post types and other WordPress content.'=>'Začněte vytvářet nová vlastní pole pro příspěvky, stránky, vlastní typy obsahu a další obsah WordPressu.','Add Your First Field'=>'Přidejte první pole','#'=>'#','Add Field'=>'Přidat pole','Presentation'=>'Prezentace','Validation'=>'Validace','General'=>'Obecné','Import JSON'=>'Importovat JSON','Export As JSON'=>'Exportovat jako JSON','Field group deactivated.'=>'Skupina polí deaktivována.' . "\0" . '%s skupiny polí deaktivovány.' . "\0" . '%s skupin polí deaktivováno.','Field group activated.'=>'Skupina polí aktivována.' . "\0" . '%s skupiny polí aktivovány.' . "\0" . '%s skupin polí aktivováno.','Deactivate'=>'Deaktivovat','Deactivate this item'=>'Deaktivovat tuto položku','Activate'=>'Aktivovat','Activate this item'=>'Aktivovat tuto položku','Move field group to trash?'=>'Přesunout skupinu polí do koše?','post statusInactive'=>'Neaktivní','WP Engine'=>'WP Engine','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields PRO.'=>'Pluginy Advanced Custom Fields a Advanced Custom Fields PRO by neměly být aktivní současně. Plugin Advanced Custom Fields PRO jsme automaticky deaktivovali.','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields.'=>'Pluginy Advanced Custom Fields a Advanced Custom Fields PRO by neměly být aktivní současně. Plugin Advanced Custom Fields jsme automaticky deaktivovali.','<strong>%1$s</strong> - We\'ve detected one or more calls to retrieve ACF field values before ACF has been initialized. This is not supported and can result in malformed or missing data. <a href="%2$s" target="_blank">Learn how to fix this</a>.'=>'<strong>%1$s</strong> – Zjistili jsme jedno nebo více volání k načtení hodnot polí ACF před inicializací ACF. Toto není podporováno a může mít za následek chybná nebo chybějící data. <a href="%2$s" target="_blank">Přečtěte si, jak to opravit</a>.','%1$s must have a user with the %2$s role.'=>'%1$s musí mít uživatele s rolí %2$s.' . "\0" . '%1$s musí mít uživatele s jednou z následujících rolí: %2$s' . "\0" . '%1$s musí mít uživatele s jednou z následujících rolí: %2$s','%1$s must have a valid user ID.'=>'%1$s musí mít platné ID uživatele.','Invalid request.'=>'Neplatný požadavek.','%1$s is not one of %2$s'=>'%1$s není jedním z %2$s','%1$s must have term %2$s.'=>'%1$s musí mít pojem %2$s.' . "\0" . '%1$s musí mít jeden z následujících pojmů: %2$s' . "\0" . '%1$s musí mít jeden z následujících pojmů: %2$s','%1$s must be of post type %2$s.'=>'%1$s musí být typu %2$s.' . "\0" . '%1$s musí být jeden z následujících typů obsahu: %2$s' . "\0" . '%1$s musí být jeden z následujících typů obsahu: %2$s','%1$s must have a valid post ID.'=>'%1$s musí mít platné ID příspěvku.','%s requires a valid attachment ID.'=>'%s vyžaduje platné ID přílohy.','Show in REST API'=>'Zobrazit v REST API','Enable Transparency'=>'Povolit průhlednost','RGBA Array'=>'Pole RGBA','RGBA String'=>'Řetězec RGBA','Hex String'=>'Řetězec Hex','Upgrade to PRO'=>'Zakoupit PRO verzi','post statusActive'=>'Aktivní','\'%s\' is not a valid email address'=>'\'%s\' není platná e-mailová adresa','Color value'=>'Hodnota barvy','Select default color'=>'Vyberte výchozí barvu','Clear color'=>'Zrušit barvu','Blocks'=>'Bloky','Options'=>'Konfigurace','Users'=>'Uživatelé','Menu items'=>'Položky menu','Widgets'=>'Widgety','Attachments'=>'Přílohy','Taxonomies'=>'Taxonomie','Posts'=>'Příspěvky','Last updated: %s'=>'Poslední aktualizace: %s','Sorry, this post is unavailable for diff comparison.'=>'Omlouváme se, ale tento příspěvek není k dispozici pro porovnání.','Invalid field group parameter(s).'=>'Jeden nebo více neplatných parametrů skupiny polí.','Awaiting save'=>'Čeká na uložení','Saved'=>'Uloženo','Import'=>'Import','Review changes'=>'Zkontrolovat změny','Located in: %s'=>'Umístěn v: %s','Located in plugin: %s'=>'Nachází se v pluginu: %s','Located in theme: %s'=>'Nachází se v šabloně: %s','Various'=>'Různé','Sync changes'=>'Synchronizovat změny','Loading diff'=>'Načítání diff','Review local JSON changes'=>'Přehled místních změn JSON','Visit website'=>'Navštívit stránky','View details'=>'Zobrazit podrobnosti','Version %s'=>'Verze %s','Information'=>'Informace','<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.'=>'<a href="%s" target="_blank">Nápověda</a>. Odborníci na podporu na našem help desku pomohou s hlubšími technickými problémy.','<a href="%s" target="_blank">Discussions</a>. We have an active and friendly community on our Community Forums who may be able to help you figure out the \'how-tos\' of the ACF world.'=>'<a href="%s" target="_blank">Diskuze</a>. Na našich komunitních fórech máme aktivní a přátelskou komunitu, která může pomoci zjistit „jak na to“ ve světě ACF.','<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.'=>'<a href="%s" target="_blank">Dokumentace</a>. Naše rozsáhlá dokumentace obsahuje odkazy a návody pro většinu situací, se kterými se můžete setkat.','We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:'=>'Jsme fanatici podpory a chceme, abyste ze svých webových stránek s ACF dostali to nejlepší. Pokud se setkáte s jakýmikoli potížemi, můžete najít pomoc na několika místech:','Help & Support'=>'Nápověda a podpora','Please use the Help & Support tab to get in touch should you find yourself requiring assistance.'=>'Pokud budete potřebovat pomoc, přejděte na záložku Nápověda a podpora.','Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.'=>'Před vytvořením první skupiny polí doporučujeme přečíst našeho průvodce <a href="%s" target="_blank">Začínáme</a>, abyste se seznámili s filozofií pluginu a osvědčenými postupy.','The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.'=>'Plugin Advanced Custom Fields poskytuje vizuální nástroj pro tvorbu formulářů, který umožňuje přizpůsobit editační obrazovky WordPressu pomocí dalších polí. Plugin nabízí intuitivní rozhraní API pro zobrazení hodnot vlastních polí v libovolném souboru šablony.','Overview'=>'Přehled','Location type "%s" is already registered.'=>'Typ umístění „%s“ je již zaregistrován.','Class "%s" does not exist.'=>'Třída "%s" neexistuje.','Invalid nonce.'=>'Neplatná hodnota.','Error loading field.'=>'Při načítání pole došlo k chybě.','Location not found: %s'=>'Umístění nenalezeno: %s','<strong>Error</strong>: %s'=>'<strong>Chyba</strong>: %s','Widget'=>'Widget','User Role'=>'Uživatelská úroveň','Comment'=>'Komentář','Post Format'=>'Formát příspěvku','Menu Item'=>'Položka nabídky','Post Status'=>'Stav příspěvku','Menus'=>'Nabídky','Menu Locations'=>'Umístění nabídky','Menu'=>'Nabídka','Post Taxonomy'=>'Taxonomie příspěvku','Child Page (has parent)'=>'Podřazená stránka (má rodiče)','Parent Page (has children)'=>'Rodičovská stránka (má potomky)','Top Level Page (no parent)'=>'Stránka nejvyšší úrovně (žádný nadřazený)','Posts Page'=>'Stránka příspěvku','Front Page'=>'Hlavní stránka','Page Type'=>'Typ stránky','Viewing back end'=>'Prohlížíte backend','Viewing front end'=>'Prohlížíte frontend','Logged in'=>'Přihlášen','Current User'=>'Aktuální uživatel','Page Template'=>'Šablona stránky','Register'=>'Registrovat','Add / Edit'=>'Přidat / Editovat','User Form'=>'Uživatelský formulář','Page Parent'=>'Rodičovská stránka','Super Admin'=>'Super Admin','Current User Role'=>'Aktuální uživatelská role','Default Template'=>'Výchozí šablona','Post Template'=>'Šablona příspěvku','Post Category'=>'Rubrika příspěvku','All %s formats'=>'Všechny formáty %s','Attachment'=>'Příloha','%s value is required'=>'%s hodnota je vyžadována','Show this field if'=>'Zobrazit toto pole, pokud','Conditional Logic'=>'Podmíněná logika','and'=>'a','Local JSON'=>'Lokální JSON','Clone Field'=>'Pole Klonování','Please also check all premium add-ons (%s) are updated to the latest version.'=>'Zkontrolujte také, zda jsou všechna prémiová rozšíření (%s) aktualizována na nejnovější verzi.','This version contains improvements to your database and requires an upgrade.'=>'Tato verze obsahuje vylepšení databáze a vyžaduje upgrade.','Thank you for updating to %1$s v%2$s!'=>'Děkujeme za aktualizaci na %1$s v%2$s!','Database Upgrade Required'=>'Vyžadován upgrade databáze','Options Page'=>'Stránka konfigurace','Gallery'=>'Galerie','Flexible Content'=>'Flexibilní obsah','Repeater'=>'Opakovač','Back to all tools'=>'Zpět na všechny nástroje','If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)'=>'Pokud se na obrazovce úprav objeví více skupin polí, použije se nastavení dle první skupiny polí (té s nejnižším pořadovým číslem)','<b>Select</b> items to <b>hide</b> them from the edit screen.'=>'<b>Zvolte</b> položky, které budou na obrazovce úprav <b>skryté</b>.','Hide on screen'=>'Skrýt na obrazovce','Send Trackbacks'=>'Odesílat zpětné linkování odkazů','Tags'=>'Štítky','Categories'=>'Kategorie','Page Attributes'=>'Atributy stránky','Format'=>'Formát','Author'=>'Autor','Slug'=>'Adresa','Revisions'=>'Revize','Comments'=>'Komentáře','Discussion'=>'Diskuze','Excerpt'=>'Stručný výpis','Content Editor'=>'Editor obsahu','Permalink'=>'Trvalý odkaz','Shown in field group list'=>'Zobrazit v seznamu skupin polí','Field groups with a lower order will appear first'=>'Skupiny polí s nižším pořadím se zobrazí první','Order No.'=>'Pořadové č.','Below fields'=>'Pod poli','Below labels'=>'Pod štítky','Side'=>'Na straně','Normal (after content)'=>'Normální (po obsahu)','High (after title)'=>'Vysoko (po nadpisu)','Position'=>'Pozice','Seamless (no metabox)'=>'Bezokrajové (bez metaboxu)','Standard (WP metabox)'=>'Standardní (WP metabox)','Style'=>'Styl','Type'=>'Typ','Key'=>'Klíč','Order'=>'Pořadí','Close Field'=>'Zavřít pole','id'=>'ID','class'=>'třída','width'=>'šířka','Wrapper Attributes'=>'Atributy obalového pole','Required'=>'Požadováno?','Instructions'=>'Instrukce','Field Type'=>'Typ pole','Single word, no spaces. Underscores and dashes allowed'=>'Jedno slovo, bez mezer. Podtržítka a pomlčky jsou povoleny','Field Name'=>'Jméno pole','This is the name which will appear on the EDIT page'=>'Toto je jméno, které se zobrazí na stránce úprav','Field Label'=>'Štítek pole','Delete'=>'Smazat','Delete field'=>'Smazat pole','Move'=>'Přesunout','Move field to another group'=>'Přesunout pole do jiné skupiny','Duplicate field'=>'Duplikovat pole','Edit field'=>'Upravit pole','Drag to reorder'=>'Přetažením změníte pořadí','Show this field group if'=>'Zobrazit tuto skupinu polí, pokud','No updates available.'=>'K dispozici nejsou žádné aktualizace.','Database upgrade complete. <a href="%s">See what\'s new</a>'=>'Upgrade databáze byl dokončen. <a href="%s">Podívejte se, co je nového</a>','Reading upgrade tasks...'=>'Čtení úkolů aktualizace...','Upgrade failed.'=>'Upgrade se nezdařil.','Upgrade complete.'=>'Aktualizace dokončena.','Upgrading data to version %s'=>'Aktualizace dat na verzi %s','It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?'=>'Důrazně doporučujeme zálohovat databázi před pokračováním. Opravdu chcete aktualizaci spustit?','Please select at least one site to upgrade.'=>'Vyberte alespoň jednu stránku, kterou chcete upgradovat.','Database Upgrade complete. <a href="%s">Return to network dashboard</a>'=>'Aktualizace databáze je dokončena. <a href="%s">Návrat na nástěnku sítě</a>','Site is up to date'=>'Stránky jsou aktuální','Site requires database upgrade from %1$s to %2$s'=>'Web vyžaduje aktualizaci databáze z %1$s na %2$s','Site'=>'Stránky','Upgrade Sites'=>'Upgradovat stránky','The following sites require a DB upgrade. Check the ones you want to update and then click %s.'=>'Následující stránky vyžadují upgrade DB. Zaškrtněte ty, které chcete aktualizovat, a poté klikněte na %s.','Add rule group'=>'Přidat skupinu pravidel','Create a set of rules to determine which edit screens will use these advanced custom fields'=>'Vytváří sadu pravidel pro určení, na kterých stránkách úprav budou použita tato vlastní pole','Rules'=>'Pravidla','Copied'=>'Zkopírováno','Copy to clipboard'=>'Zkopírovat od schránky','Select the items you would like to export and then select your export method. Export As JSON to export to a .json file which you can then import to another ACF installation. Generate PHP to export to PHP code which you can place in your theme.'=>'Vyberte skupiny polí, které chcete exportovat, a vyberte způsob exportu. Použijte tlačítko pro stažení pro exportování do souboru .json, který pak můžete importovat do jiné instalace ACF. Pomocí tlačítka generovat můžete exportovat do kódu PHP, který můžete umístit do vašeho tématu.','Select Field Groups'=>'Vybrat skupiny polí','No field groups selected'=>'Nebyly vybrány žádné skupiny polí','Generate PHP'=>'Vytvořit PHP','Export Field Groups'=>'Exportovat skupiny polí','Import file empty'=>'Importovaný soubor je prázdný','Incorrect file type'=>'Nesprávný typ souboru','Error uploading file. Please try again'=>'Chyba při nahrávání souboru. Prosím zkuste to znovu','Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the items in that file.'=>'Vyberte Advanced Custom Fields JSON soubor, který chcete importovat. Po klepnutí na tlačítko importu níže bude ACF importovat skupiny polí.','Import Field Groups'=>'Importovat skupiny polí','Sync'=>'Synchronizace','Select %s'=>'Zvolit %s','Duplicate'=>'Duplikovat','Duplicate this item'=>'Duplikovat tuto položku','Supports'=>'Podporuje','Documentation'=>'Dokumentace','Description'=>'Popis','Sync available'=>'Synchronizace je k dispozici','Field group synchronized.'=>'Skupina polí synchronizována.' . "\0" . '%s skupiny polí synchronizovány.' . "\0" . '%s skupin polí synchronizováno.','Field group duplicated.'=>'Skupina polí duplikována.' . "\0" . '%s skupiny polí duplikovány.' . "\0" . '%s skupin polí duplikováno.','Active <span class="count">(%s)</span>'=>'Aktivní <span class="count">(%s)</span>' . "\0" . 'Aktivní <span class="count">(%s)</span>' . "\0" . 'Aktivních <span class="count">(%s)</span>','Review sites & upgrade'=>'Zkontrolujte stránky a aktualizujte','Upgrade Database'=>'Aktualizovat databázi','Custom Fields'=>'Vlastní pole','Move Field'=>'Přesunout pole','Please select the destination for this field'=>'Prosím zvolte umístění pro toto pole','The %1$s field can now be found in the %2$s field group'=>'Pole %1$s lze nyní nalézt ve skupině polí %2$s.','Move Complete.'=>'Přesun hotov.','Active'=>'Aktivní','Field Keys'=>'Klíče polí','Settings'=>'Nastavení','Location'=>'Umístění','Null'=>'Nula','copy'=>'kopírovat','(this field)'=>'(toto pole)','Checked'=>'Zaškrtnuto','Move Custom Field'=>'Přesunout vlastní pole','No toggle fields available'=>'Žádné zapínatelné pole není k dispozici','Field group title is required'=>'Vyžadován nadpis pro skupinu polí','This field cannot be moved until its changes have been saved'=>'Toto pole nelze přesunout, dokud nebudou uloženy jeho změny','The string "field_" may not be used at the start of a field name'=>'Řetězec "pole_" nesmí být použit na začátku názvu pole','Field group draft updated.'=>'Koncept skupiny polí aktualizován.','Field group scheduled for.'=>'Skupina polí byla naplánována.','Field group submitted.'=>'Skupina polí odeslána.','Field group saved.'=>'Skupina polí uložena.','Field group published.'=>'Skupina polí publikována.','Field group deleted.'=>'Skupina polí smazána.','Field group updated.'=>'Skupina polí aktualizována.','Tools'=>'Nástroje','is not equal to'=>'není rovno','is equal to'=>'je rovno','Forms'=>'Formuláře','Page'=>'Stránka','Post'=>'Příspěvek','Relational'=>'Relační','Choice'=>'Volba','Basic'=>'Základní','Unknown'=>'Neznámý','Field type does not exist'=>'Typ pole neexistuje','Spam Detected'=>'Zjištěn spam','Post updated'=>'Příspěvek aktualizován','Update'=>'Aktualizace','Validate Email'=>'Ověřit e-mail','Content'=>'Obsah','Title'=>'Název','Edit field group'=>'Editovat skupinu polí','Selection is less than'=>'Výběr je menší než','Selection is greater than'=>'Výběr je větší než','Value is less than'=>'Hodnota je menší než','Value is greater than'=>'Hodnota je větší než','Value contains'=>'Hodnota obsahuje','Value matches pattern'=>'Hodnota odpovídá masce','Value is not equal to'=>'Hodnota není rovna','Value is equal to'=>'Hodnota je rovna','Has no value'=>'Nemá hodnotu','Has any value'=>'Má libovolnou hodnotu','Cancel'=>'Zrušit','Are you sure?'=>'Jste si jistí?','%d fields require attention'=>'Několik polí vyžaduje pozornost (%d)','1 field requires attention'=>'1 pole vyžaduje pozornost','Validation failed'=>'Ověření selhalo','Validation successful'=>'Ověření úspěšné','Restricted'=>'Omezeno','Collapse Details'=>'Sbalit podrobnosti','Expand Details'=>'Rozbalit podrobnosti','Uploaded to this post'=>'Nahrán k tomuto příspěvku','verbUpdate'=>'Aktualizace','verbEdit'=>'Upravit','The changes you made will be lost if you navigate away from this page'=>'Pokud opustíte tuto stránku, změny, které jste provedli, budou ztraceny','File type must be %s.'=>'Typ souboru musí být %s.','or'=>'nebo','File size must not exceed %s.'=>'Velikost souboru nesmí překročit %s.','File size must be at least %s.'=>'Velikost souboru musí být alespoň %s.','Image height must not exceed %dpx.'=>'Výška obrázku nesmí přesáhnout %dpx.','Image height must be at least %dpx.'=>'Výška obrázku musí být alespoň %dpx.','Image width must not exceed %dpx.'=>'Šířka obrázku nesmí přesáhnout %dpx.','Image width must be at least %dpx.'=>'Šířka obrázku musí být alespoň %dpx.','(no title)'=>'(bez názvu)','Full Size'=>'Plná velikost','Large'=>'Velký','Medium'=>'Střední','Thumbnail'=>'Miniatura','(no label)'=>'(bez štítku)','Sets the textarea height'=>'Nastavuje výšku textového pole','Rows'=>'Řádky','Text Area'=>'Textové pole','Prepend an extra checkbox to toggle all choices'=>'Přidat zaškrtávátko navíc pro přepnutí všech možností','Save \'custom\' values to the field\'s choices'=>'Uložit \'vlastní\' hodnoty do voleb polí','Allow \'custom\' values to be added'=>'Povolit přidání \'vlastních\' hodnot','Add new choice'=>'Přidat novou volbu','Toggle All'=>'Přepnout vše','Allow Archives URLs'=>'Umožnit URL adresy archivu','Archives'=>'Archivy','Page Link'=>'Odkaz stránky','Add'=>'Přidat','Name'=>'Jméno','%s added'=>'%s přidán','%s already exists'=>'%s již existuje','User unable to add new %s'=>'Uživatel není schopen přidat nové %s','Term ID'=>'ID pojmu','Term Object'=>'Objekt pojmu','Load value from posts terms'=>'Nahrát pojmy z příspěvků','Load Terms'=>'Nahrát pojmy','Connect selected terms to the post'=>'Připojte vybrané pojmy k příspěvku','Save Terms'=>'Uložit pojmy','Allow new terms to be created whilst editing'=>'Povolit vytvoření nových pojmů během editace','Create Terms'=>'Vytvořit pojmy','Radio Buttons'=>'Radio přepínače','Single Value'=>'Jednotlivá hodnota','Multi Select'=>'Vícenásobný výběr','Checkbox'=>'Zaškrtávátko','Multiple Values'=>'Více hodnot','Select the appearance of this field'=>'Vyberte vzhled tohoto pole','Appearance'=>'Vzhled','Select the taxonomy to be displayed'=>'Zvolit zobrazovanou taxonomii','No TermsNo %s'=>'Nic pro %s','Value must be equal to or lower than %d'=>'Hodnota musí být rovna nebo menší než %d','Value must be equal to or higher than %d'=>'Hodnota musí být rovna nebo větší než %d','Value must be a number'=>'Hodnota musí být číslo','Number'=>'Číslo','Save \'other\' values to the field\'s choices'=>'Uložit \'jiné\' hodnoty do voleb polí','Add \'other\' choice to allow for custom values'=>'Přidat volbu \'jiné\', která umožňuje vlastní hodnoty','Other'=>'Jiné','Radio Button'=>'Přepínač','Define an endpoint for the previous accordion to stop. This accordion will not be visible.'=>'Definujte koncový bod pro předchozí akordeon. Tento akordeon nebude viditelný.','Allow this accordion to open without closing others.'=>'Povolit otevření tohoto akordeonu bez zavření ostatních.','Display this accordion as open on page load.'=>'Zobrazit tento akordeon jako otevřený při načtení stránky.','Open'=>'Otevřít','Accordion'=>'Akordeon','Restrict which files can be uploaded'=>'Omezte, které typy souborů lze nahrát','File ID'=>'ID souboru','File URL'=>'Adresa souboru','File Array'=>'Pole souboru','Add File'=>'Přidat soubor','No file selected'=>'Dokument nevybrán','File name'=>'Jméno souboru','Update File'=>'Aktualizovat soubor','Edit File'=>'Upravit soubor','Select File'=>'Vybrat soubor','File'=>'Soubor','Password'=>'Heslo','Specify the value returned'=>'Zadat konkrétní návratovou hodnotu','Use AJAX to lazy load choices?'=>'K načtení volby použít AJAX lazy load?','Enter each default value on a new line'=>'Zadejte každou výchozí hodnotu na nový řádek','verbSelect'=>'Vybrat','Select2 JS load_failLoading failed'=>'Načítání selhalo','Select2 JS searchingSearching&hellip;'=>'Vyhledávání&hellip;','Select2 JS load_moreLoading more results&hellip;'=>'Načítání dalších výsledků&hellip;','Select2 JS selection_too_long_nYou can only select %d items'=>'Můžete vybrat pouze %d položek','Select2 JS selection_too_long_1You can only select 1 item'=>'Můžete vybrat pouze 1 položku','Select2 JS input_too_long_nPlease delete %d characters'=>'Prosím odstraňte %d znaků','Select2 JS input_too_long_1Please delete 1 character'=>'Prosím odstraňte 1 znak','Select2 JS input_too_short_nPlease enter %d or more characters'=>'Prosím zadejte %d nebo více znaků','Select2 JS input_too_short_1Please enter 1 or more characters'=>'Prosím zadejte 1 nebo více znaků','Select2 JS matches_0No matches found'=>'Nebyly nalezeny žádné výsledky','Select2 JS matches_n%d results are available, use up and down arrow keys to navigate.'=>'%d výsledků je k dispozici, použijte šipky nahoru a dolů pro navigaci.','Select2 JS matches_1One result is available, press enter to select it.'=>'Jeden výsledek je k dispozici, stiskněte klávesu enter pro jeho vybrání.','nounSelect'=>'Vybrat','User ID'=>'ID uživatele','User Object'=>'Objekt uživatele','User Array'=>'Pole uživatelů','All user roles'=>'Všechny uživatelské role','User'=>'Uživatel','Separator'=>'Oddělovač','Select Color'=>'Výběr barvy','Default'=>'Výchozí nastavení','Clear'=>'Vymazat','Color Picker'=>'Výběr barvy','Date Time Picker JS pmTextShortP'=>'do','Date Time Picker JS pmTextPM'=>'odp','Date Time Picker JS amTextShortA'=>'od','Date Time Picker JS amTextAM'=>'dop','Date Time Picker JS selectTextSelect'=>'Vybrat','Date Time Picker JS closeTextDone'=>'Hotovo','Date Time Picker JS currentTextNow'=>'Nyní','Date Time Picker JS timezoneTextTime Zone'=>'Časové pásmo','Date Time Picker JS microsecTextMicrosecond'=>'Mikrosekunda','Date Time Picker JS millisecTextMillisecond'=>'Milisekunda','Date Time Picker JS secondTextSecond'=>'Vteřina','Date Time Picker JS minuteTextMinute'=>'Minuta','Date Time Picker JS hourTextHour'=>'Hodina','Date Time Picker JS timeTextTime'=>'Čas','Date Time Picker JS timeOnlyTitleChoose Time'=>'Zvolit čas','Date Time Picker'=>'Výběr data a času','Endpoint'=>'Koncový bod','Left aligned'=>'Zarovnat zleva','Top aligned'=>'Zarovnat shora','Placement'=>'Umístění','Tab'=>'Záložka','Value must be a valid URL'=>'Hodnota musí být validní adresa URL','Link URL'=>'URL adresa odkazu','Link Array'=>'Pole odkazů','Opens in a new window/tab'=>'Otevřít v novém okně/záložce','Select Link'=>'Vybrat odkaz','Link'=>'Odkaz','Email'=>'E-mail','Step Size'=>'Velikost kroku','Maximum Value'=>'Maximální hodnota','Minimum Value'=>'Minimální hodnota','Range'=>'Rozmezí','Both (Array)'=>'Obě (pole)','Label'=>'Štítek','Value'=>'Hodnota','Vertical'=>'Vertikální','Horizontal'=>'Horizontální','red : Red'=>'cervena : Červená','For more control, you may specify both a value and label like this:'=>'Pro větší kontrolu můžete zadat jak hodnotu, tak štítek:','Enter each choice on a new line.'=>'Zadejte každou volbu na nový řádek.','Choices'=>'Možnosti','Button Group'=>'Skupina tlačítek','Parent'=>'Rodič','TinyMCE will not be initialized until field is clicked'=>'TinyMCE se inicializuje až po kliknutí na pole','Toolbar'=>'Lišta nástrojů','Text Only'=>'Pouze text','Visual Only'=>'Pouze grafika','Visual & Text'=>'Grafika a text','Tabs'=>'Záložky','Click to initialize TinyMCE'=>'Klikněte pro inicializaci TinyMCE','Name for the Text editor tab (formerly HTML)Text'=>'Text','Visual'=>'Grafika','Value must not exceed %d characters'=>'Hodnota nesmí překročit %d znaků','Leave blank for no limit'=>'Nechte prázdné pro nastavení bez omezení','Character Limit'=>'Limit znaků','Appears after the input'=>'Zobrazí se za inputem','Append'=>'Zobrazit po','Appears before the input'=>'Zobrazí se před inputem','Prepend'=>'Zobrazit před','Appears within the input'=>'Zobrazí se v inputu','Placeholder Text'=>'Zástupný text','Appears when creating a new post'=>'Objeví se při vytváření nového příspěvku','Text'=>'Text','%1$s requires at least %2$s selection'=>'%1$s vyžaduje alespoň %2$s volbu' . "\0" . '%1$s vyžaduje alespoň %2$s volby' . "\0" . '%1$s vyžaduje alespoň %2$s voleb','Post ID'=>'ID příspěvku','Post Object'=>'Objekt příspěvku','Featured Image'=>'Uživatelský obrázek','Selected elements will be displayed in each result'=>'Vybrané prvky se zobrazí v každém výsledku','Elements'=>'Prvky','Taxonomy'=>'Taxonomie','Post Type'=>'Typ příspěvku','Filters'=>'Filtry','All taxonomies'=>'Všechny taxonomie','Filter by Taxonomy'=>'Filtrovat dle taxonomie','All post types'=>'Všechny typy příspěvků','Filter by Post Type'=>'Filtrovat dle typu příspěvku','Search...'=>'Hledat...','Select taxonomy'=>'Zvolit taxonomii','Select post type'=>'Zvolit typ příspěvku','No matches found'=>'Nebyly nalezeny žádné výsledky','Loading'=>'Načítání','Maximum values reached ( {max} values )'=>'Dosaženo maximálního množství hodnot ( {max} hodnot )','Relationship'=>'Vztah','Comma separated list. Leave blank for all types'=>'Seznam oddělený čárkami. Nechte prázdné pro povolení všech typů','Maximum'=>'Maximum','File size'=>'Velikost souboru','Restrict which images can be uploaded'=>'Omezte, které typy obrázků je možné nahrát','Minimum'=>'Minimum','Uploaded to post'=>'Nahráno k příspěvku','All'=>'Vše','Limit the media library choice'=>'Omezit výběr knihovny médií','Library'=>'Knihovna','Preview Size'=>'Velikost náhledu','Image ID'=>'ID obrázku','Image URL'=>'Adresa obrázku','Image Array'=>'Pole obrázku','Specify the returned value on front end'=>'Zadat konkrétní návratovou hodnotu na frontendu','Return Value'=>'Vrátit hodnotu','Add Image'=>'Přidat obrázek','No image selected'=>'Není vybrán žádný obrázek','Remove'=>'Odstranit','Edit'=>'Upravit','All images'=>'Všechny obrázky','Update Image'=>'Aktualizovat obrázek','Edit Image'=>'Upravit obrázek','Select Image'=>'Vybrat obrázek','Image'=>'Obrázek','Allow HTML markup to display as visible text instead of rendering'=>'Nevykreslovat efekt, ale zobrazit značky HTML jako prostý text','Escape HTML'=>'Escapovat HTML','No Formatting'=>'Žádné formátování','Automatically add &lt;br&gt;'=>'Automaticky přidávat &lt;br&gt;','Automatically add paragraphs'=>'Automaticky přidávat odstavce','Controls how new lines are rendered'=>'Řídí, jak se vykreslují nové řádky','New Lines'=>'Nové řádky','Week Starts On'=>'Týden začíná','The format used when saving a value'=>'Formát použitý při ukládání hodnoty','Save Format'=>'Uložit formát','Date Picker JS weekHeaderWk'=>'Týden','Date Picker JS prevTextPrev'=>'Předchozí','Date Picker JS nextTextNext'=>'Následující','Date Picker JS currentTextToday'=>'Dnes','Date Picker JS closeTextDone'=>'Hotovo','Date Picker'=>'Výběr data','Width'=>'Šířka','Embed Size'=>'Velikost pro Embed','Enter URL'=>'Vložte URL','oEmbed'=>'oEmbed','Text shown when inactive'=>'Text zobrazený při neaktivním poli','Off Text'=>'Text (neaktivní)','Text shown when active'=>'Text zobrazený při aktivním poli','On Text'=>'Text (aktivní)','Stylized UI'=>'Stylizované uživatelské rozhraní','Default Value'=>'Výchozí hodnota','Displays text alongside the checkbox'=>'Zobrazí text vedle zaškrtávacího políčka','Message'=>'Zpráva','No'=>'Ne','Yes'=>'Ano','True / False'=>'Pravda / Nepravda','Row'=>'Řádek','Table'=>'Tabulka','Block'=>'Blok','Specify the style used to render the selected fields'=>'Určení stylu použitého pro vykreslení vybraných polí','Layout'=>'Typ zobrazení','Sub Fields'=>'Podřazená pole','Group'=>'Skupina','Customize the map height'=>'Přizpůsobení výšky mapy','Height'=>'Výška','Set the initial zoom level'=>'Nastavit počáteční úroveň přiblížení','Zoom'=>'Přiblížení','Center the initial map'=>'Vycentrovat počáteční zobrazení mapy','Center'=>'Vycentrovat','Search for address...'=>'Vyhledat adresu...','Find current location'=>'Najít aktuální umístění','Clear location'=>'Vymazat polohu','Search'=>'Hledat','Sorry, this browser does not support geolocation'=>'Je nám líto, ale tento prohlížeč nepodporuje geolokaci','Google Map'=>'Mapa Google','The format returned via template functions'=>'Formát vrácen pomocí funkcí šablony','Return Format'=>'Formát návratové hodnoty','Custom:'=>'Vlastní:','The format displayed when editing a post'=>'Formát zobrazený při úpravě příspěvku','Display Format'=>'Formát zobrazení','Time Picker'=>'Výběr času','Inactive <span class="count">(%s)</span>'=>'Neaktivní <span class="count">(%s)</span>' . "\0" . 'Neaktivní <span class="count">(%s)</span>' . "\0" . 'Neaktivní <span class="count">(%s)</span>','No Fields found in Trash'=>'V koši nenalezeno žádné pole','No Fields found'=>'Nenalezeno žádné pole','Search Fields'=>'Vyhledat pole','View Field'=>'Zobrazit pole','New Field'=>'Nové pole','Edit Field'=>'Upravit pole','Add New Field'=>'Přidat nové pole','Field'=>'Pole','Fields'=>'Pole','No Field Groups found in Trash'=>'V koši nebyly nalezeny žádné skupiny polí','No Field Groups found'=>'Nebyly nalezeny žádné skupiny polí','Search Field Groups'=>'Hledat skupiny polí','View Field Group'=>'Prohlížet skupinu polí','New Field Group'=>'Nová skupina polí','Edit Field Group'=>'Upravit skupinu polí','Add New Field Group'=>'Přidat novou skupinu polí','Add New'=>'Přidat nové','Field Group'=>'Skupina polí','Field Groups'=>'Skupiny polí','Customize WordPress with powerful, professional and intuitive fields.'=>'Přizpůsobte si WordPress pomocí efektivních, profesionálních a intuitivních polí.','https://www.advancedcustomfields.com'=>'https://www.advancedcustomfields.com','Advanced Custom Fields'=>'Advanced Custom Fields','Advanced Custom Fields PRO'=>'Advanced Custom Fields PRO','Block type name is required.'=>'%s hodnota je vyžadována','%s settings'=>'Nastavení','Options Updated'=>'Nastavení aktualizováno','To enable updates, please enter your license key on the <a href="%1$s">Updates</a> page. If you don\'t have a licence key, please see <a href="%2$s" target="_blank">details & pricing</a>.'=>'Chcete-li povolit aktualizace, zadejte prosím licenční klíč na stránce <a href="%s">Aktualizace</a>. Pokud nemáte licenční klíč, přečtěte si <a href="%s">podrobnosti a ceny</a>.','<b>ACF Activation Error</b>. An error occurred when connecting to activation server'=>'<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat','Check Again'=>'Zkontrolujte znovu','<b>ACF Activation Error</b>. Could not connect to activation server'=>'<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat','Publish'=>'Publikovat','No Custom Field Groups found for this options page. <a href="%s">Create a Custom Field Group</a>'=>'Nebyly nalezeny žádné vlastní skupiny polí. <a href="%s">Vytvořit vlastní skupinu polí</a>','<b>Error</b>. Could not connect to update server'=>'<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat','Select one or more fields you wish to clone'=>'Vyberte jedno nebo více polí, které chcete klonovat','Display'=>'Zobrazovat','Specify the style used to render the clone field'=>'Určení stylu použitého pro vykreslení klonovaných polí','Group (displays selected fields in a group within this field)'=>'Skupina (zobrazuje vybrané pole ve skupině v tomto poli)','Seamless (replaces this field with selected fields)'=>'Bezešvé (nahradí toto pole vybranými poli)','Labels will be displayed as %s'=>'Štítky budou zobrazeny jako %s','Prefix Field Labels'=>'Prefix štítku pole','Values will be saved as %s'=>'Hodnoty budou uloženy jako %s','Prefix Field Names'=>'Prefix jména pole','Unknown field'=>'Neznámé pole','Unknown field group'=>'Skupina neznámých polí','All fields from %s field group'=>'Všechna pole z skupiny polí %s','Add Row'=>'Přidat řádek','layout'=>'typ zobrazení' . "\0" . 'typ zobrazení' . "\0" . 'typ zobrazení','layouts'=>'typy zobrazení','This field requires at least {min} {label} {identifier}'=>'Toto pole vyžaduje alespoň {min} {label} {identifier}','This field has a limit of {max} {label} {identifier}'=>'Toto pole má limit {max}{label}  {identifier}','{available} {label} {identifier} available (max {max})'=>'{available} {label} {identifier} dostupný (max {max})','{required} {label} {identifier} required (min {min})'=>'{required} {label} {identifier} povinný (min {min})','Flexible Content requires at least 1 layout'=>'Flexibilní obsah vyžaduje minimálně jedno rozložení obsahu','Click the "%s" button below to start creating your layout'=>'Klikněte na tlačítko "%s" níže pro vytvoření vlastního typu zobrazení','Add layout'=>'Přidat typ zobrazení','Duplicate layout'=>'Duplikovat typ zobrazení','Remove layout'=>'Odstranit typ zobrazení','Click to toggle'=>'Klikněte pro přepnutí','Delete Layout'=>'Smazat typ zobrazení','Duplicate Layout'=>'Duplikovat typ zobrazení','Add New Layout'=>'Přidat nový typ zobrazení','Add Layout'=>'Přidat typ zobrazení','Min'=>'Min','Max'=>'Max','Minimum Layouts'=>'Minimální rozložení','Maximum Layouts'=>'Maximální rozložení','Button Label'=>'Nápis tlačítka','Add Image to Gallery'=>'Přidat obrázek do galerie','Maximum selection reached'=>'Maximální výběr dosažen','Length'=>'Délka','Caption'=>'Popisek','Alt Text'=>'Alternativní text','Add to gallery'=>'Přidat do galerie','Bulk actions'=>'Hromadné akce','Sort by date uploaded'=>'Řadit dle data nahrání','Sort by date modified'=>'Řadit dle data změny','Sort by title'=>'Řadit dle názvu','Reverse current order'=>'Převrátit aktuální pořadí','Close'=>'Zavřít','Minimum Selection'=>'Minimální výběr','Maximum Selection'=>'Maximální výběr','Allowed file types'=>'Povolené typy souborů','Insert'=>'Vložit','Specify where new attachments are added'=>'Určete, kde budou přidány nové přílohy','Append to the end'=>'Přidat na konec','Prepend to the beginning'=>'Přidat na začátek','Minimum rows not reached ({min} rows)'=>'Minimální počet řádků dosažen ({min} řádků)','Maximum rows reached ({max} rows)'=>'Maximální počet řádků dosažen ({max} řádků)','Rows Per Page'=>'Stránka příspěvku','Set the number of rows to be displayed on a page.'=>'Zvolit zobrazovanou taxonomii','Minimum Rows'=>'Minimum řádků','Maximum Rows'=>'Maximum řádků','Collapsed'=>'Sbaleno','Select a sub field to show when row is collapsed'=>'Zvolte dílčí pole, které se zobrazí při sbalení řádku','Click to reorder'=>'Přetažením změníte pořadí','Add row'=>'Přidat řádek','Duplicate row'=>'Duplikovat','Remove row'=>'Odebrat řádek','Current Page'=>'Rodičovská stránka','First Page'=>'Hlavní stránka','Previous Page'=>'Stránka příspěvku','Next Page'=>'Rodičovská stránka','Last Page'=>'Stránka příspěvku','No block types exist'=>'Neexistuje stránka nastavení','No options pages exist'=>'Neexistuje stránka nastavení','Deactivate License'=>'Deaktivujte licenci','Activate License'=>'Aktivujte licenci','License Information'=>'Informace o licenci','To unlock updates, please enter your license key below. If you don\'t have a licence key, please see <a href="%s" target="_blank">details & pricing</a>.'=>'Chcete-li povolit aktualizace, zadejte prosím licenční klíč. Pokud nemáte licenční klíč, přečtěte si <a href="%s">podrobnosti a ceny</a>.','License Key'=>'Licenční klíč','Retry Activation'=>'Aktivační kód','Update Information'=>'Aktualizovat informace','Current Version'=>'Současná verze','Latest Version'=>'Nejnovější verze','Update Available'=>'Aktualizace je dostupná','Upgrade Notice'=>'Upozornění na aktualizaci','Enter your license key to unlock updates'=>'Pro odemčení aktualizací zadejte prosím výše svůj licenční klíč','Update Plugin'=>'Aktualizovat plugin','Please reactivate your license to unlock updates'=>'Pro odemčení aktualizací zadejte prosím výše svůj licenční klíč']];