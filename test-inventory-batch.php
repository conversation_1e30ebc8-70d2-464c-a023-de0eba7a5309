<?php
/**
 * Test script for SAP Inventory API - Single and Batch Updates
 * This script demonstrates both single material updates and batch updates
 */

// Test 1: Single Material Update (Original Format - Backward Compatible)
echo "=== Test 1: Single Material Update (Original Format) ===\n";

$single_request = [
    'inventory' => [
        'materialNumber' => 'M12345',
        'stock' => [
            'US' => [
                'quantity' => 100,
                'allowBackorders' => false
            ],
            'EU' => [
                'quantity' => 200,
                'allowBackorders' => true
            ]
        ]
    ]
];

echo "Request JSON:\n";
echo wp_json_encode( $single_request, JSON_PRETTY_PRINT ) . "\n\n";

// Test 2: Batch Material Update (New Format)
echo "=== Test 2: Batch Material Update (New Format) ===\n";

$batch_request = [
    'inventories' => [
        [
            'materialNumber' => 'M12345',
            'stock' => [
                'US' => [
                    'quantity' => 150,
                    'allowBackorders' => false
                ],
                'EU' => [
                    'quantity' => 250,
                    'allowBackorders' => true
                ],
                'CA' => [
                    'quantity' => 75,
                    'allowBackorders' => false
                ]
            ]
        ],
        [
            'materialNumber' => 'M67890',
            'stock' => [
                'US' => [
                    'quantity' => 300,
                    'allowBackorders' => true
                ],
                'EU' => [
                    'quantity' => 400,
                    'allowBackorders' => false
                ]
            ]
        ],
        [
            'materialNumber' => 'M11111',
            'stock' => [
                'US' => [
                    'quantity' => 50,
                    'allowBackorders' => true
                ],
                'EU' => [
                    'quantity' => 60,
                    'allowBackorders' => true
                ],
                'CA' => [
                    'quantity' => 25,
                    'allowBackorders' => false
                ],
                'AU' => [
                    'quantity' => 35,
                    'allowBackorders' => true
                ]
            ]
        ]
    ]
];

echo "Request JSON:\n";
echo wp_json_encode( $batch_request, JSON_PRETTY_PRINT ) . "\n\n";

// Test 3: Large Batch Update Example
echo "=== Test 3: Large Batch Update Example ===\n";

$large_batch_request = [
    'inventories' => []
];

// Generate 10 materials for testing
for ( $i = 1; $i <= 10; $i++ ) {
    $material_number = sprintf( 'M%05d', $i );
    
    $large_batch_request['inventories'][] = [
        'materialNumber' => $material_number,
        'stock' => [
            'US' => [
                'quantity' => rand( 50, 500 ),
                'allowBackorders' => (bool) rand( 0, 1 )
            ],
            'EU' => [
                'quantity' => rand( 50, 500 ),
                'allowBackorders' => (bool) rand( 0, 1 )
            ],
            'CA' => [
                'quantity' => rand( 25, 250 ),
                'allowBackorders' => (bool) rand( 0, 1 )
            ]
        ]
    ];
}

echo "Large batch request with " . count( $large_batch_request['inventories'] ) . " materials:\n";
echo wp_json_encode( $large_batch_request, JSON_PRETTY_PRINT ) . "\n\n";

// Function to make API request (for testing purposes)
function test_inventory_api_request( $data, $description ) {
    echo "=== Making API Request: {$description} ===\n";
    
    $response = wp_remote_post( home_url( '/wp-json/wc/v3/sap-inventory' ), [
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode( 'admin:password' ) // Replace with valid credentials
        ],
        'body' => wp_json_encode( $data ),
        'timeout' => 60 // Increase timeout for batch requests
    ]);

    if ( is_wp_error( $response ) ) {
        echo "Request failed: " . $response->get_error_message() . "\n";
    } else {
        $body = wp_remote_retrieve_body( $response );
        $status = wp_remote_retrieve_response_code( $response );
        echo "Status: {$status}\n";
        
        $response_data = json_decode( $body, true );
        if ( $response_data ) {
            echo "Response Summary:\n";
            echo "- Success: " . ( $response_data['success'] ? 'Yes' : 'No' ) . "\n";
            echo "- Is Batch: " . ( $response_data['is_batch'] ? 'Yes' : 'No' ) . "\n";
            echo "- Materials Processed: " . $response_data['total_materials_processed'] . "\n";
            echo "- Materials Requested: " . $response_data['total_materials_requested'] . "\n";
            echo "- Total Regions Updated: " . $response_data['total_regions_updated'] . "\n";
            
            if ( ! empty( $response_data['errors'] ) ) {
                echo "- Errors: " . count( $response_data['errors'] ) . "\n";
                foreach ( $response_data['errors'] as $error ) {
                    echo "  * {$error}\n";
                }
            }
            
            echo "\nFull Response:\n";
            echo wp_json_encode( $response_data, JSON_PRETTY_PRINT ) . "\n";
        } else {
            echo "Raw Response: {$body}\n";
        }
    }
    
    echo "\n" . str_repeat( "=", 80 ) . "\n\n";
}

// Uncomment the lines below to actually make the API requests
// test_inventory_api_request( $single_request, "Single Material Update" );
// test_inventory_api_request( $batch_request, "Batch Material Update" );
// test_inventory_api_request( $large_batch_request, "Large Batch Update" );

echo "=== API Request Examples Complete ===\n";
echo "To test these requests:\n";
echo "1. Uncomment the test_inventory_api_request() calls above\n";
echo "2. Update the Authorization header with valid credentials\n";
echo "3. Ensure the material numbers exist as products in WooCommerce\n";
echo "4. Run this script\n\n";

echo "Expected Response Format for Batch Requests:\n";
$expected_batch_response = [
    'success' => true,
    'is_batch' => true,
    'total_materials_processed' => 3,
    'total_materials_requested' => 3,
    'total_regions_updated' => 8,
    'processed_materials' => [
        [
            'product_id' => 123,
            'material_number' => 'M12345',
            'updated_regions' => [
                ['region' => 'US', 'stock_quantity' => 150, 'allow_backorders' => false],
                ['region' => 'EU', 'stock_quantity' => 250, 'allow_backorders' => true],
                ['region' => 'CA', 'stock_quantity' => 75, 'allow_backorders' => false]
            ],
            'total_regions_updated' => 3,
            'errors' => []
        ]
        // ... more materials
    ],
    'errors' => []
];

echo wp_json_encode( $expected_batch_response, JSON_PRETTY_PRINT ) . "\n";
?>
