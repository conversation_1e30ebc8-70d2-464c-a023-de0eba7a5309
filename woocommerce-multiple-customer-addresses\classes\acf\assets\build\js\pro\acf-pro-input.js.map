{"version": 3, "file": "acf-pro-input.js", "mappings": ";;;;;;;;;AAAA,CAAE,UAAWA,CAAC,EAAG;EAChB,IAAIC,KAAK,GAAGC,GAAG,CAACD,KAAK,CAACE,MAAM,CAAE;IAC7BC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,EAAE;IAERC,MAAM,EAAE;MACP,gCAAgC,EAAE,YAAY;MAC9C,sCAAsC,EAAE,kBAAkB;MAC1D,mCAAmC,EAAE,eAAe;MACpD,qCAAqC,EAAE,iBAAiB;MACxDC,SAAS,EAAE,QAAQ;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;IACZ,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACV,CAAC,CAAE,6BAA8B,CAAC;IAC/C,CAAC;IAEDW,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,OAAO,IAAI,CAACX,CAAC,CAAE,uCAAwC,CAAC;IACzD,CAAC;IAEDY,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACZ,CAAC,CAAE,iDAAkD,CAAC;IACnE,CAAC;IAEDa,OAAO,EAAE,SAAAA,CAAWC,KAAK,EAAG;MAC3B,OAAO,IAAI,CAACd,CAAC,CACZ,qDAAqD,GACpDc,KAAK,GACL,GACF,CAAC;IACF,CAAC;IAEDC,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAACf,CAAC,CAAE,uCAAwC,CAAC;IACzD,CAAC;IAEDgB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAChB,CAAC,CAAE,kDAAmD,CAAC;IACpE,CAAC;IAEDiB,MAAM,EAAE,SAAAA,CAAWC,IAAI,EAAG;MACzB,OAAO,IAAI,CAAClB,CAAC,CACZ,gEAAgE,GAC/DkB,IAAI,GACJ,IACF,CAAC;IACF,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACnB,CAAC,CAAE,mBAAoB,CAAC;IACrC,CAAC;IAEDoB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACpB,CAAC,CAAE,2BAA4B,CAAC;IAC7C,CAAC;IAEDqB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACrB,CAAC,CAAE,kBAAmB,CAAC;IACpC,CAAC;IAEDsB,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAIC,IAAI,GAAG,IAAI,CAACF,MAAM,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;MAC/B,IAAIC,KAAK,GAAGxB,CAAC,CAAEuB,IAAK,CAAC;MACrB,IAAIE,IAAI,GAAG,IAAI;;MAEf;MACAD,KAAK,CAACE,IAAI,CAAE,eAAgB,CAAC,CAACC,IAAI,CAAE,YAAY;QAC/C,IAAIC,EAAE,GAAG5B,CAAC,CAAE,IAAK,CAAC;QAClB,IAAI6B,GAAG,GAAGD,EAAE,CAACE,IAAI,CAAE,KAAM,CAAC,IAAI,CAAC;QAC/B,IAAIC,GAAG,GAAGH,EAAE,CAACE,IAAI,CAAE,KAAM,CAAC,IAAI,CAAC;QAC/B,IAAIZ,IAAI,GAAGU,EAAE,CAACE,IAAI,CAAE,QAAS,CAAC,IAAI,EAAE;QACpC,IAAIE,KAAK,GAAGP,IAAI,CAACQ,YAAY,CAAEf,IAAK,CAAC;;QAErC;QACA,IAAKa,GAAG,IAAIC,KAAK,IAAID,GAAG,EAAG;UAC1BH,EAAE,CAACM,QAAQ,CAAE,UAAW,CAAC;UACzB;QACD;;QAEA;QACA,IAAKL,GAAG,IAAIG,KAAK,GAAGH,GAAG,EAAG;UACzB,IAAIM,QAAQ,GAAGN,GAAG,GAAGG,KAAK;UAC1B,IAAII,KAAK,GAAGlC,GAAG,CAACmC,EAAE,CACjB,sDACD,CAAC;UACD,IAAIC,UAAU,GAAGpC,GAAG,CAACqC,EAAE,CAAE,QAAQ,EAAE,SAAS,EAAEJ,QAAS,CAAC;;UAExD;UACAC,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,YAAY,EAAEL,QAAS,CAAC;UAC/CC,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,SAAS,EAAEtB,IAAK,CAAC,CAAC,CAAC;UAC1CkB,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,cAAc,EAAEF,UAAW,CAAC;UACnDF,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,OAAO,EAAEX,GAAI,CAAC;;UAErC;UACAD,EAAE,CAACa,MAAM,CACR,6BAA6B,GAC5BL,KAAK,GACL,IAAI,GACJD,QAAQ,GACR,SACF,CAAC;QACF;MACD,CAAE,CAAC;;MAEH;MACAZ,IAAI,GAAGC,KAAK,CAACkB,SAAS,CAAC,CAAC;MAExB,OAAOnB,IAAI;IACZ,CAAC;IAEDoB,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAAC/B,QAAQ,CAAC,CAAC,CAACgC,MAAM;IAC9B,CAAC;IAEDC,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,IAAIhB,GAAG,GAAGiB,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAM,CAAE,CAAC;MACvC,OAAO,CAAElB,GAAG,IAAIA,GAAG,GAAG,IAAI,CAACmB,GAAG,CAAC,CAAC;IACjC,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAIlB,GAAG,GAAGe,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAM,CAAE,CAAC;MACvC,OAAO,CAAEhB,GAAG,IAAIA,GAAG,GAAG,IAAI,CAACiB,GAAG,CAAC,CAAC;IACjC,CAAC;IAEDE,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAInB,GAAG,GAAGe,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAM,CAAE,CAAC;MACvC,OAAOhB,GAAG,IAAI,IAAI,CAACiB,GAAG,CAAC,CAAC,IAAIjB,GAAG;IAChC,CAAC;IAEDoB,WAAW,EAAE,SAAAA,CAAW1B,IAAI,EAAG;MAC9B;MACA,IAAK,IAAI,CAACsB,GAAG,CAAE,KAAM,CAAC,IAAI,CAAC,EAAG;QAC7B;MACD;;MAEA;MACA,IAAI,CAACpC,YAAY,CAAC,CAAC,CAACyC,QAAQ,CAAE;QAC7BC,KAAK,EAAE,WAAW;QAClBC,MAAM,EAAE,yBAAyB;QACjCC,eAAe,EAAE,IAAI;QACrBC,oBAAoB,EAAE,IAAI;QAC1BC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,SAAAA,CAAWC,KAAK,EAAEC,EAAE,EAAG;UAC5BnC,IAAI,CAACoC,MAAM,CAAC,CAAC;QACd,CAAC;QACDC,MAAM,EAAE,SAAAA,CAAWH,KAAK,EAAEC,EAAE,EAAG;UAC9BnC,IAAI,CAACsC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;QAClC;MACD,CAAE,CAAC;IACJ,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAIC,OAAO,GAAGC,UAAU,CAACC,IAAI,CAAE,IAAI,CAACrB,GAAG,CAAE,KAAM,CAAE,CAAC;;MAElD;MACA,IAAK,CAAEmB,OAAO,EAAG;QAChB,OAAO,KAAK;MACb;;MAEA;MACA,IAAI,CAACtD,QAAQ,CAAC,CAAC,CAACe,IAAI,CAAE,UAAW0C,CAAC,EAAG;QACpC,IAAKH,OAAO,CAACI,OAAO,CAAED,CAAE,CAAC,GAAG,CAAC,CAAC,EAAG;UAChCrE,CAAC,CAAE,IAAK,CAAC,CAACkC,QAAQ,CAAE,YAAa,CAAC;QACnC;MACD,CAAE,CAAC;IACJ,CAAC;IAEDqC,iBAAiB,EAAE,SAAAA,CAAW9C,IAAI,EAAG;MACpC;MACA,IAAI,CAAC+C,EAAE,CAAE,cAAc,EAAE,SAAS,EAAE,UAAWC,CAAC,EAAG;QAClDhD,IAAI,CAACiD,cAAc,CAAED,CAAC,EAAEzE,CAAC,CAAE,IAAK,CAAE,CAAC;MACpC,CAAE,CAAC;IACJ,CAAC;IAED2E,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAI,CAACJ,iBAAiB,CAAE,IAAK,CAAC;;MAE9B;MACA,IAAI,CAACN,YAAY,CAAC,CAAC;;MAEnB;MACA/D,GAAG,CAAC0E,OAAO,CAAE,IAAI,CAAC7D,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC8D,GAAI,CAAC;;MAE3C;MACA,IAAI,CAAChB,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAI,CAACjD,QAAQ,CAAC,CAAC,CAACe,IAAI,CAAE,UAAW0C,CAAC,EAAG;QACpCrE,CAAC,CAAE,IAAK,CAAC,CACP0B,IAAI,CAAE,4BAA6B,CAAC,CACpCH,IAAI,CAAE8C,CAAC,GAAG,CAAE,CAAC;MAChB,CAAE,CAAC;;MAEH;MACA,IAAK,IAAI,CAACrB,GAAG,CAAC,CAAC,IAAI,CAAC,EAAG;QACtB,IAAI,CAACtC,QAAQ,CAAC,CAAC,CAACwB,QAAQ,CAAE,QAAS,CAAC;MACrC,CAAC,MAAM;QACN,IAAI,CAACxB,QAAQ,CAAC,CAAC,CAACoE,WAAW,CAAE,QAAS,CAAC;MACxC;;MAEA;MACA,IAAK,IAAI,CAAC5B,MAAM,CAAC,CAAC,EAAG;QACpB,IAAI,CAAC9B,OAAO,CAAC,CAAC,CAACc,QAAQ,CAAE,UAAW,CAAC;MACtC,CAAC,MAAM;QACN,IAAI,CAACd,OAAO,CAAC,CAAC,CAAC0D,WAAW,CAAE,UAAW,CAAC;MACzC;IACD,CAAC;IAEDC,MAAM,EAAE,SAAAA,CAAWN,CAAC,EAAEO,GAAG,EAAEC,OAAO,EAAG;MACpC;MACA,IAAIC,MAAM,GAAGhF,GAAG,CAACiF,SAAS,CAAE;QAC3BC,EAAE,EAAE,UAAU;QACdC,MAAM,EAAE,IAAI,CAACL;MACd,CAAE,CAAC;;MAEH;MACA;MACA;MACA9E,GAAG,CAACoF,QAAQ,CAAE,aAAa,EAAEJ,MAAO,CAAC;IACtC,CAAC;IAEDjD,YAAY,EAAE,SAAAA,CAAWf,IAAI,EAAG;MAC/B,OAAO,IAAI,CAACN,QAAQ,CAAC,CAAC,CAAC2E,MAAM,CAAE,YAAY;QAC1C,OAAOvF,CAAC,CAAE,IAAK,CAAC,CAAC8B,IAAI,CAAE,QAAS,CAAC,KAAKZ,IAAI;MAC3C,CAAE,CAAC,CAAC0B,MAAM;IACX,CAAC;IAED4C,kBAAkB,EAAE,SAAAA,CAAWC,aAAa,EAAG;MAC9C,MAAMC,SAAS,GAAGD,aAAa,CAAC3D,IAAI,CAAE,KAAM,CAAC;MAC7C,IAAK,CAAE4D,SAAS,EAAG;QAClB,OAAO,IAAI;MACZ;MACA,MAAMxE,IAAI,GAAGuE,aAAa,CAAC3D,IAAI,CAAE,QAAS,CAAC,IAAI,EAAE;MACjD,MAAME,KAAK,GAAG,IAAI,CAACC,YAAY,CAAEf,IAAK,CAAC;MAEvC,IAAIc,KAAK,IAAI0D,SAAS,EAAG;QACxB,IAAIC,IAAI,GAAGzF,GAAG,CAACmC,EAAE,CAChB,sDACD,CAAC;QACD,MAAMC,UAAU,GAAGpC,GAAG,CAACqC,EAAE,CAAE,QAAQ,EAAE,SAAS,EAAEmD,SAAU,CAAC;QAC3D,MAAME,WAAW,GAAG,GAAG,GAAGH,aAAa,CAAC3D,IAAI,CAAE,OAAQ,CAAC,GAAG,GAAG;QAC7D6D,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,OAAO,EAAEkD,SAAU,CAAC;QACzCC,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,SAAS,EAAEoD,WAAY,CAAC;QAC7CD,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,cAAc,EAAEF,UAAW,CAAC;QAEjD,IAAI,CAACuD,UAAU,CAAE;UAChBF,IAAI,EAAEA,IAAI;UACVvF,IAAI,EAAE;QACP,CAAE,CAAC;QAEH,OAAO,KAAK;MACb;MAEA,OAAO,IAAI;IACZ,CAAC;IAED0F,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAK,IAAI,CAAC7C,QAAQ,CAAC,CAAC,EAAG;QACtB,OAAO,IAAI;MACZ;MAEA,IAAIlB,GAAG,GAAG,IAAI,CAACgB,GAAG,CAAE,KAAM,CAAC;MAC3B,IAAI4C,IAAI,GAAGzF,GAAG,CAACmC,EAAE,CAChB,sDACD,CAAC;MACD,IAAIC,UAAU,GAAGpC,GAAG,CAACqC,EAAE,CAAE,QAAQ,EAAE,SAAS,EAAER,GAAI,CAAC;MAEnD4D,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,OAAO,EAAET,GAAI,CAAC;MACnC4D,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,SAAS,EAAE,EAAG,CAAC;MACpCmD,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,cAAc,EAAEF,UAAW,CAAC;MAEjD,IAAI,CAACuD,UAAU,CAAE;QAChBF,IAAI,EAAEA,IAAI;QACVvF,IAAI,EAAE;MACP,CAAE,CAAC;MAEH,OAAO,KAAK;IACb,CAAC;IAED2F,UAAU,EAAE,SAAAA,CAAWtB,CAAC,EAAEO,GAAG,EAAG;MAC/B;MACA,IAAK,CAAE,IAAI,CAACc,WAAW,CAAC,CAAC,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAIjF,OAAO,GAAG,IAAI;MAClB,IAAKmE,GAAG,CAACgB,QAAQ,CAAE,UAAW,CAAC,EAAG;QACjCnF,OAAO,GAAGmE,GAAG,CAACiB,OAAO,CAAE,SAAU,CAAC;QAClCpF,OAAO,CAACqB,QAAQ,CAAE,QAAS,CAAC;MAC7B;;MAEA;MACA,IAAIgE,KAAK,GAAG,IAAIC,KAAK,CAAE;QACtBC,MAAM,EAAEpB,GAAG;QACXqB,aAAa,EAAE,KAAK;QACpBV,IAAI,EAAE,IAAI,CAACrE,YAAY,CAAC,CAAC;QACzB2D,OAAO,EAAE,IAAI;QACbqB,OAAO,EAAE,SAAAA,CAAW7B,CAAC,EAAEO,GAAG,EAAG;UAC5B;UACA,IAAKA,GAAG,CAACgB,QAAQ,CAAE,UAAW,CAAC,EAAG;YACjC;UACD;;UAEA;UACA,IAAI,CAACO,GAAG,CAAE;YACTC,MAAM,EAAExB,GAAG,CAAClD,IAAI,CAAE,QAAS,CAAC;YAC5B2E,MAAM,EAAE5F;UACT,CAAE,CAAC;QACJ,CAAC;QACD6F,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB,IAAK7F,OAAO,EAAG;YACdA,OAAO,CAACiE,WAAW,CAAE,QAAS,CAAC;UAChC;QACD;MACD,CAAE,CAAC;;MAEH;MACAoB,KAAK,CAAC1B,EAAE,CAAE,OAAO,EAAE,eAAe,EAAE,WAAY,CAAC;IAClD,CAAC;IAED+B,GAAG,EAAE,SAAAA,CAAWI,IAAI,EAAG;MACtB;MACAA,IAAI,GAAGzG,GAAG,CAAC0G,SAAS,CAAED,IAAI,EAAE;QAC3BH,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE;MACT,CAAE,CAAC;;MAEH;MACA,IAAK,CAAE,IAAI,CAACxD,QAAQ,CAAC,CAAC,EAAG;QACxB,OAAO,KAAK;MACb;;MAEA;MACA,IAAI+B,GAAG,GAAG9E,GAAG,CAAC2G,SAAS,CAAE;QACxBT,MAAM,EAAE,IAAI,CAACnF,MAAM,CAAE0F,IAAI,CAACH,MAAO,CAAC;QAClC/D,MAAM,EAAE,IAAI,CAACqE,KAAK,CAAE,UAAW9B,GAAG,EAAE+B,IAAI,EAAG;UAC1C;UACA,IAAKJ,IAAI,CAACF,MAAM,EAAG;YAClBE,IAAI,CAACF,MAAM,CAACA,MAAM,CAAEM,IAAK,CAAC;UAC3B,CAAC,MAAM;YACN,IAAI,CAACpG,YAAY,CAAC,CAAC,CAAC8B,MAAM,CAAEsE,IAAK,CAAC;UACnC;;UAEA;UACA7G,GAAG,CAAC8G,MAAM,CAAED,IAAI,EAAE,IAAI,CAAClC,GAAI,CAAC;;UAE5B;UACA,IAAI,CAAChB,MAAM,CAAC,CAAC;QACd,CAAE;MACH,CAAE,CAAC;;MAEH;MACA,IAAI,CAACE,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;MAEjC,OAAOgB,GAAG;IACX,CAAC;IAEDiC,gBAAgB,EAAE,SAAAA,CAAWxC,CAAC,EAAEO,GAAG,EAAG;MACrC,IAAInE,OAAO,GAAGmE,GAAG,CAACiB,OAAO,CAAE,SAAU,CAAC;MACtC;MACA,IAAK,CAAE,IAAI,CAACT,kBAAkB,CAAE3E,OAAO,CAACqG,KAAK,CAAC,CAAE,CAAC,EAAG;QACnD,OAAO,KAAK;MACb;;MAEA;MACA,IAAK,CAAE,IAAI,CAACpB,WAAW,CAAC,CAAC,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAI,CAACqB,eAAe,CAAEtG,OAAQ,CAAC;IAChC,CAAC;IAEDsG,eAAe,EAAE,SAAAA,CAAWtG,OAAO,EAAG;MACrC;MACA,IAAK,CAAE,IAAI,CAACoC,QAAQ,CAAC,CAAC,EAAG;QACxB,OAAO,KAAK;MACb;MAEA,IAAImE,QAAQ,GAAG,IAAI,CAACrE,GAAG,CAAE,KAAM,CAAC;;MAEhC;MACA,IAAIiC,GAAG,GAAG9E,GAAG,CAAC2G,SAAS,CAAE;QACxBT,MAAM,EAAEvF,OAAO;QAEf;QACAwG,MAAM,EAAE,SAAAA,CAAWnG,IAAI,EAAEoG,KAAK,EAAEC,MAAM,EAAE/E,OAAO,EAAG;UACjD;UACA,IAAKtB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAG;YACtC,OAAOoG,KAAK,CAAC9E,OAAO,CACnB4E,QAAQ,GAAG,GAAG,GAAGG,MAAM,EACvBH,QAAQ,GAAG,GAAG,GAAG5E,OAClB,CAAC;;YAED;UACD,CAAC,MAAM;YACN,OAAO8E,KAAK,CAAC9E,OAAO,CACnB4E,QAAQ,GAAG,IAAI,GAAGG,MAAM,EACxBH,QAAQ,GAAG,IAAI,GAAG5E,OACnB,CAAC;UACF;QACD,CAAC;QACDiE,MAAM,EAAE,SAAAA,CAAWzB,GAAG,EAAG;UACxB9E,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEN,GAAI,CAAC;QAC/B,CAAC;QACDwC,KAAK,EAAE,SAAAA,CAAWxC,GAAG,EAAE+B,IAAI,EAAG;UAC7B7G,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEN,GAAI,CAAC;QAC/B;MACD,CAAE,CAAC;;MAEH;MACA,IAAI,CAACjB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;;MAEjC;MACA,IAAI,CAACH,MAAM,CAAC,CAAC;;MAEb;MACA3D,GAAG,CAACuH,cAAc,CAAEzC,GAAI,CAAC;;MAEzB;MACA,OAAOA,GAAG;IACX,CAAC;IAED0C,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B;MACA,IAAK,IAAI,CAAC7E,WAAW,CAAC,CAAC,EAAG;QACzB,OAAO,IAAI;MACZ;MAEA,IAAIhB,GAAG,GAAG,IAAI,CAACkB,GAAG,CAAE,KAAM,CAAC;MAC3B,IAAI4C,IAAI,GAAGzF,GAAG,CAACmC,EAAE,CAChB,yDACD,CAAC;MACD,IAAIC,UAAU,GAAGpC,GAAG,CAACqC,EAAE,CAAE,QAAQ,EAAE,SAAS,EAAEV,GAAI,CAAC;;MAEnD;MACA8D,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,OAAO,EAAEX,GAAI,CAAC;MACnC8D,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,SAAS,EAAE,EAAG,CAAC;MACpCmD,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,cAAc,EAAEF,UAAW,CAAC;;MAEjD;MACA,IAAI,CAACuD,UAAU,CAAE;QAChBF,IAAI,EAAEA,IAAI;QACVvF,IAAI,EAAE;MACP,CAAE,CAAC;MAEH,OAAO,KAAK;IACb,CAAC;IAEDuH,aAAa,EAAE,SAAAA,CAAWlD,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAInE,OAAO,GAAGmE,GAAG,CAACiB,OAAO,CAAE,SAAU,CAAC;;MAEtC;MACA,IAAKxB,CAAC,CAACmD,QAAQ,EAAG;QACjB,OAAO,IAAI,CAACC,YAAY,CAAEhH,OAAQ,CAAC;MACpC;;MAEA;MACAA,OAAO,CAACqB,QAAQ,CAAE,QAAS,CAAC;;MAE5B;MACA,IAAI4F,OAAO,GAAG5H,GAAG,CAAC6H,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnB5B,MAAM,EAAEpB,GAAG;QACXC,OAAO,EAAE,IAAI;QACbqB,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB,IAAI,CAACuB,YAAY,CAAEhH,OAAQ,CAAC;QAC7B,CAAC;QACD6F,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB7F,OAAO,CAACiE,WAAW,CAAE,QAAS,CAAC;QAChC;MACD,CAAE,CAAC;IACJ,CAAC;IAED+C,YAAY,EAAE,SAAAA,CAAWhH,OAAO,EAAG;MAClC;MACA,IAAIY,IAAI,GAAG,IAAI;MAEf,IAAIwG,SAAS,GAAG,IAAI,CAACtF,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;;MAE7C;MACAzC,GAAG,CAACgI,MAAM,CAAE;QACX9B,MAAM,EAAEvF,OAAO;QACfoH,SAAS,EAAEA,SAAS;QACpBE,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB;UACA1G,IAAI,CAACsC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;;UAEjC;UACAvC,IAAI,CAACoC,MAAM,CAAC,CAAC;QACd;MACD,CAAE,CAAC;IACJ,CAAC;IAEDuE,eAAe,EAAE,SAAAA,CAAW3D,CAAC,EAAEO,GAAG,EAAG;MACpC,IAAInE,OAAO,GAAGmE,GAAG,CAACiB,OAAO,CAAE,SAAU,CAAC;;MAEtC;MACA,IAAK,IAAI,CAACoC,cAAc,CAAExH,OAAQ,CAAC,EAAG;QACrC,IAAI,CAACyH,UAAU,CAAEzH,OAAQ,CAAC;MAC3B,CAAC,MAAM;QACN,IAAI,CAAC0H,WAAW,CAAE1H,OAAQ,CAAC;MAC5B;IACD,CAAC;IAEDwH,cAAc,EAAE,SAAAA,CAAWxH,OAAO,EAAG;MACpC,OAAOA,OAAO,CAACmF,QAAQ,CAAE,YAAa,CAAC;IACxC,CAAC;IAEDsC,UAAU,EAAE,SAAAA,CAAWzH,OAAO,EAAG;MAChCA,OAAO,CAACiE,WAAW,CAAE,YAAa,CAAC;MACnC5E,GAAG,CAACoF,QAAQ,CAAE,MAAM,EAAEzE,OAAO,EAAE,UAAW,CAAC;IAC5C,CAAC;IAED0H,WAAW,EAAE,SAAAA,CAAW1H,OAAO,EAAG;MACjCA,OAAO,CAACqB,QAAQ,CAAE,YAAa,CAAC;MAChChC,GAAG,CAACoF,QAAQ,CAAE,MAAM,EAAEzE,OAAO,EAAE,UAAW,CAAC;;MAE3C;MACA;MACA,IAAI,CAAC2H,YAAY,CAAE3H,OAAQ,CAAC;IAC7B,CAAC;IAED2H,YAAY,EAAE,SAAAA,CAAW3H,OAAO,EAAG;MAClC,IAAIkD,MAAM,GAAGlD,OAAO,CAAC4H,QAAQ,CAAE,OAAQ,CAAC;MACxC,IAAIC,MAAM,GAAG3E,MAAM,CAAC4E,IAAI,CAAE,MAAO,CAAC,CAACnG,OAAO,CAAE,iBAAiB,EAAE,EAAG,CAAC;;MAEnE;MACA,IAAIoG,QAAQ,GAAG;QACdC,MAAM,EAAE,0CAA0C;QAClDC,SAAS,EAAE,IAAI,CAAC/F,GAAG,CAAE,KAAM,CAAC;QAC5BsB,CAAC,EAAExD,OAAO,CAACC,KAAK,CAAC,CAAC;QAClB0F,MAAM,EAAE3F,OAAO,CAACiB,IAAI,CAAE,QAAS,CAAC;QAChCwF,KAAK,EAAEpH,GAAG,CAAC6I,SAAS,CAAElI,OAAO,EAAE6H,MAAO;MACvC,CAAC;;MAED;MACA1I,CAAC,CAACgJ,IAAI,CAAE;QACPC,GAAG,EAAE/I,GAAG,CAAC6C,GAAG,CAAE,SAAU,CAAC;QACzBjB,IAAI,EAAE5B,GAAG,CAACgJ,cAAc,CAAEN,QAAS,CAAC;QACpCO,QAAQ,EAAE,MAAM;QAChB/I,IAAI,EAAE,MAAM;QACZgJ,OAAO,EAAE,SAAAA,CAAW7H,IAAI,EAAG;UAC1B,IAAKA,IAAI,EAAG;YACXV,OAAO,CACL4H,QAAQ,CAAE,uBAAwB,CAAC,CACnClH,IAAI,CAAEA,IAAK,CAAC;UACf;QACD;MACD,CAAE,CAAC;IACJ,CAAC;IAED8H,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAInF,OAAO,GAAG,EAAE;;MAEhB;MACA,IAAI,CAACtD,QAAQ,CAAC,CAAC,CAACe,IAAI,CAAE,UAAW0C,CAAC,EAAG;QACpC,IAAKrE,CAAC,CAAE,IAAK,CAAC,CAACgG,QAAQ,CAAE,YAAa,CAAC,EAAG;UACzC9B,OAAO,CAACoF,IAAI,CAAEjF,CAAE,CAAC;QAClB;MACD,CAAE,CAAC;;MAEH;MACAH,OAAO,GAAGA,OAAO,CAACtB,MAAM,GAAGsB,OAAO,GAAG,IAAI;;MAEzC;MACAC,UAAU,CAACoF,IAAI,CAAE,IAAI,CAACxG,GAAG,CAAE,KAAM,CAAC,EAAEmB,OAAQ,CAAC;IAC9C,CAAC;IAEDQ,cAAc,EAAE,SAAAA,CAAWD,CAAC,EAAE5D,OAAO,EAAG;MACvC;MACA,IAAK,IAAI,CAACwH,cAAc,CAAExH,OAAQ,CAAC,EAAG;QACrC,IAAI,CAACyH,UAAU,CAAEzH,OAAQ,CAAC;MAC3B;IACD,CAAC;IAED2I,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB;MACA,IAAI,CAACrG,WAAW,CAAE,IAAK,CAAC;;MAExB;MACA,IAAI,CAACsG,GAAG,CAAE,WAAY,CAAC;IACxB;EACD,CAAE,CAAC;EAEHvJ,GAAG,CAACwJ,iBAAiB,CAAEzJ,KAAM,CAAC;;EAE9B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIkG,KAAK,GAAGjG,GAAG,CAACyJ,MAAM,CAACC,cAAc,CAACzJ,MAAM,CAAE;IAC7CG,MAAM,EAAE;MACP,qBAAqB,EAAE,WAAW;MAClC,6BAA6B,EAAE;IAChC,CAAC;IAEDuD,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAI,CAACtC,IAAI,CAAE,IAAI,CAACwB,GAAG,CAAE,MAAO,CAAE,CAAC;;MAE/B;MACA,IAAI,CAACiC,GAAG,CAAC9C,QAAQ,CAAE,cAAe,CAAC;IACpC;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC;EACAhC,GAAG,CAAC2J,6BAA6B,CAAE,UAAU,EAAE,kBAAmB,CAAC;EACnE3J,GAAG,CAAC2J,6BAA6B,CAAE,YAAY,EAAE,kBAAmB,CAAC;EACrE3J,GAAG,CAAC2J,6BAA6B,CAAE,UAAU,EAAE,kBAAmB,CAAC;EACnE3J,GAAG,CAAC2J,6BAA6B,CAAE,aAAa,EAAE,kBAAmB,CAAC;;EAEtE;EACA,IAAI1F,UAAU,GAAG,IAAIjE,GAAG,CAAC4J,KAAK,CAAE;IAC/B5I,IAAI,EAAE,uBAAuB;IAE7B6I,GAAG,EAAE,SAAAA,CAAWA,GAAG,EAAE9E,OAAO,EAAG;MAC9B,IAAIjD,KAAK,GAAG,IAAI,CAACe,GAAG,CAAEgH,GAAG,GAAG9E,OAAQ,CAAC,IAAI,CAAC;;MAE1C;MACAjD,KAAK,EAAE;MACP,IAAI,CAACgI,GAAG,CAAED,GAAG,GAAG9E,OAAO,EAAEjD,KAAK,EAAE,IAAK,CAAC;;MAEtC;MACA,IAAKA,KAAK,GAAG,CAAC,EAAG;QAChB+H,GAAG,IAAI,GAAG,GAAG/H,KAAK;MACnB;MAEA,OAAO+H,GAAG;IACX,CAAC;IAED3F,IAAI,EAAE,SAAAA,CAAW2F,GAAG,EAAG;MACtB,IAAIA,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAO,CAAC;MACjC,IAAIjI,IAAI,GAAG5B,GAAG,CAAC+J,aAAa,CAAE,IAAI,CAAC/I,IAAK,CAAC;MAEzC,IAAKY,IAAI,IAAIA,IAAI,CAAEiI,GAAG,CAAE,EAAG;QAC1B,OAAOjI,IAAI,CAAEiI,GAAG,CAAE;MACnB,CAAC,MAAM;QACN,OAAO,KAAK;MACb;IACD,CAAC;IAEDR,IAAI,EAAE,SAAAA,CAAWQ,GAAG,EAAEzC,KAAK,EAAG;MAC7B,IAAIyC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAO,CAAC;MACjC,IAAIjI,IAAI,GAAG5B,GAAG,CAAC+J,aAAa,CAAE,IAAI,CAAC/I,IAAK,CAAC,IAAI,CAAC,CAAC;;MAE/C;MACA,IAAKoG,KAAK,KAAK,IAAI,EAAG;QACrB,OAAOxF,IAAI,CAAEiI,GAAG,CAAE;;QAElB;MACD,CAAC,MAAM;QACNjI,IAAI,CAAEiI,GAAG,CAAE,GAAGzC,KAAK;MACpB;;MAEA;MACA,IAAKtH,CAAC,CAACkK,aAAa,CAAEpI,IAAK,CAAC,EAAG;QAC9BA,IAAI,GAAG,IAAI;MACZ;;MAEA;MACA5B,GAAG,CAACiK,aAAa,CAAE,IAAI,CAACjJ,IAAI,EAAEY,IAAK,CAAC;IACrC;EACD,CAAE,CAAC;AACJ,CAAC,EAAIsI,MAAO,CAAC;;;;;;;;;;ACprBb,CAAE,UAAWpK,CAAC,EAAG;EAChB,IAAIC,KAAK,GAAGC,GAAG,CAACD,KAAK,CAACE,MAAM,CAAE;IAC7BC,IAAI,EAAE,SAAS;IAEfE,MAAM,EAAE;MACP,wBAAwB,EAAE,YAAY;MACtC,yBAAyB,EAAE,aAAa;MACxC,2BAA2B,EAAE,eAAe;MAC5C,+BAA+B,EAAE,eAAe;MAChD,0BAA0B,EAAE,cAAc;MAC1C,0BAA0B,EAAE,cAAc;MAC1C,2BAA2B,EAAE,UAAU;MACvCG,SAAS,EAAE,SAAS;MACpBF,SAAS,EAAE;IACZ,CAAC;IAED8J,OAAO,EAAE;MACRC,gBAAgB,EAAE,mBAAmB;MACrCC,kBAAkB,EAAE,qBAAqB;MACzCC,MAAM,EAAE;IACT,CAAC;IAEDC,iBAAiB,EAAE,SAAAA,CAAA,EAAY;MAC9BvK,GAAG,CAAC0E,OAAO,CAAE,IAAI,CAAC8F,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC7F,GAAI,CAAC;IAC1C,CAAC;IAED8F,mBAAmB,EAAE,SAAAA,CAAA,EAAY;MAChCzK,GAAG,CAAC8G,MAAM,CAAE,IAAI,CAAC0D,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC7F,GAAI,CAAC;IACzC,CAAC;IAEDnE,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACV,CAAC,CAAE,cAAe,CAAC;IAChC,CAAC;IAED4K,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAAC5K,CAAC,CAAE,0BAA2B,CAAC;IAC5C,CAAC;IAED6K,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,OAAO,IAAI,CAAC7K,CAAC,CAAE,yBAA0B,CAAC;IAC3C,CAAC;IAED8K,WAAW,EAAE,SAAAA,CAAWC,EAAE,EAAG;MAC5B,OAAO,IAAI,CAAC/K,CAAC,CAAE,mCAAmC,GAAG+K,EAAE,GAAG,IAAK,CAAC;IACjE,CAAC;IAEDC,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAChL,CAAC,CAAE,gCAAiC,CAAC;IAClD,CAAC;IAEDiL,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAACjL,CAAC,CAAE,mBAAoB,CAAC;IACrC,CAAC;IAEDkL,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAAClL,CAAC,CAAE,mBAAoB,CAAC;IACrC,CAAC;IAED0K,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAO,IAAI,CAAC1K,CAAC,CAAE,wBAAyB,CAAC;IAC1C,CAAC;IAEDkD,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAInB,GAAG,GAAGe,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAM,CAAE,CAAC;MACvC,IAAIf,KAAK,GAAG,IAAI,CAAC6I,YAAY,CAAC,CAAC,CAACjI,MAAM;MACtC,OAAOb,GAAG,IAAIC,KAAK,IAAID,GAAG;IAC3B,CAAC;IAEDY,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIK,GAAG,GAAG,EAAE;;MAEZ;MACA,IAAI,CAAC6H,YAAY,CAAC,CAAC,CAAClJ,IAAI,CAAE,YAAY;QACrCqB,GAAG,CAACsG,IAAI,CAAEtJ,CAAC,CAAE,IAAK,CAAC,CAAC8B,IAAI,CAAE,IAAK,CAAE,CAAC;MACnC,CAAE,CAAC;;MAEH;MACA,OAAOkB,GAAG,CAACJ,MAAM,GAAGI,GAAG,GAAG,KAAK;IAChC,CAAC;IAEDuB,iBAAiB,EAAE,SAAAA,CAAW9C,IAAI,EAAG;MACpC;MACA,IAAI,CAAC+C,EAAE,CAAE,QAAQ,EAAE,mBAAmB,EAAE,UAAWC,CAAC,EAAG;QACtDhD,IAAI,CAAC0J,QAAQ,CAAE1G,CAAC,EAAEzE,CAAC,CAAE,IAAK,CAAE,CAAC;MAC9B,CAAE,CAAC;IACJ,CAAC;IAEDmD,WAAW,EAAE,SAAAA,CAAW1B,IAAI,EAAG;MAC9B;MACA,IAAI,CAACmJ,WAAW,CAAC,CAAC,CAACxH,QAAQ,CAAE;QAC5BC,KAAK,EAAE,yBAAyB;QAChCE,eAAe,EAAE,IAAI;QACrBC,oBAAoB,EAAE,IAAI;QAC1BC,MAAM,EAAE,IAAI;QACZ2H,KAAK,EAAE,SAAAA,CAAWzH,KAAK,EAAEC,EAAE,EAAG;UAC7BA,EAAE,CAACyH,WAAW,CAAC9J,IAAI,CAAEqC,EAAE,CAAC0H,IAAI,CAAC/J,IAAI,CAAC,CAAE,CAAC;UACrCqC,EAAE,CAACyH,WAAW,CAACE,UAAU,CAAE,OAAQ,CAAC;QACrC,CAAC;QACDzH,MAAM,EAAE,SAAAA,CAAWH,KAAK,EAAEC,EAAE,EAAG;UAC9BnC,IAAI,CAACsC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;QAClC;MACD,CAAE,CAAC;;MAEH;MACA,IAAI,CAACtD,QAAQ,CAAC,CAAC,CAAC8K,SAAS,CAAE;QAC1BC,OAAO,EAAE,GAAG;QACZC,SAAS,EAAE,GAAG;QACdhI,IAAI,EAAE,SAAAA,CAAWC,KAAK,EAAEC,EAAE,EAAG;UAC5B1D,GAAG,CAACyL,mBAAmB,CAAE,gBAAgB,EAAE/H,EAAE,CAACgI,IAAI,CAACC,MAAO,CAAC;QAC5D;MACD,CAAE,CAAC;IACJ,CAAC;IAEDlH,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAI,CAACJ,iBAAiB,CAAE,IAAK,CAAC;;MAE9B;MACA,IAAI,CAACV,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIiI,KAAK,GAAG,IAAI,CAAC9L,CAAC,CAAE,mBAAoB,CAAC;MACzC,IAAI+L,IAAI,GAAG,IAAI,CAAC/L,CAAC,CAAE,kBAAmB,CAAC;MACvC,IAAIgC,KAAK,GAAG,IAAI,CAAC6I,YAAY,CAAC,CAAC,CAACjI,MAAM;;MAEtC;MACA,IAAK,IAAI,CAACM,MAAM,CAAC,CAAC,EAAG;QACpB6I,IAAI,CAAC7J,QAAQ,CAAE,UAAW,CAAC;MAC5B,CAAC,MAAM;QACN6J,IAAI,CAACjH,WAAW,CAAE,UAAW,CAAC;MAC/B;;MAEA;MACA,IAAK,CAAE9C,KAAK,EAAG;QACd8J,KAAK,CAAC5J,QAAQ,CAAE,UAAW,CAAC;MAC7B,CAAC,MAAM;QACN4J,KAAK,CAAChH,WAAW,CAAE,UAAW,CAAC;MAChC;;MAEA;MACA,IAAI,CAAC0F,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIwB,KAAK,GAAG,IAAI,CAACtL,QAAQ,CAAC,CAAC,CAACsL,KAAK,CAAC,CAAC;MACnC,IAAI5F,MAAM,GAAG,GAAG;MAChB,IAAI6F,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAEH,KAAK,GAAG5F,MAAO,CAAC;;MAE1C;MACA6F,OAAO,GAAGC,IAAI,CAACrK,GAAG,CAAEoK,OAAO,EAAE,CAAE,CAAC;;MAEhC;MACA,IAAI,CAACvL,QAAQ,CAAC,CAAC,CAACiI,IAAI,CAAE,cAAc,EAAEsD,OAAQ,CAAC;IAChD,CAAC;IAEDG,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAI,CAAC5B,MAAM,CAAC,CAAC;IACd,CAAC;IAED6B,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAI,CAAC3L,QAAQ,CAAC,CAAC,CAACwB,QAAQ,CAAE,OAAQ,CAAC;;MAEnC;MACA;MACA;;MAEA;MACA,IAAI8J,KAAK,GAAG,IAAI,CAACtL,QAAQ,CAAC,CAAC,CAACsL,KAAK,CAAC,CAAC,GAAG,CAAC;MACvCA,KAAK,GAAGlJ,QAAQ,CAAEkJ,KAAM,CAAC;MACzBA,KAAK,GAAGE,IAAI,CAACnK,GAAG,CAAEiK,KAAK,EAAE,GAAI,CAAC;;MAE9B;MACA,IAAI,CAAChM,CAAC,CAAE,yBAA0B,CAAC,CAACsM,GAAG,CAAE;QAAEN,KAAK,EAAEA,KAAK,GAAG;MAAE,CAAE,CAAC;MAC/D,IAAI,CAACd,KAAK,CAAC,CAAC,CAACqB,OAAO,CAAE;QAAEP,KAAK,EAAEA,KAAK,GAAG;MAAE,CAAC,EAAE,GAAI,CAAC;MACjD,IAAI,CAACf,KAAK,CAAC,CAAC,CAACsB,OAAO,CAAE;QAAEC,KAAK,EAAER;MAAM,CAAC,EAAE,GAAI,CAAC;IAC9C,CAAC;IAEDS,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAI,CAAC/L,QAAQ,CAAC,CAAC,CAACoE,WAAW,CAAE,OAAQ,CAAC;;MAEtC;MACA,IAAI,CAACkG,OAAO,CAAC,CAAC,CAAClG,WAAW,CAAE,QAAS,CAAC;;MAEtC;MACA5E,GAAG,CAAC0E,OAAO,CAAE,IAAI,CAACsG,KAAK,CAAC,CAAE,CAAC;;MAE3B;MACA,IAAIR,SAAS,GAAG,IAAI,CAAC1K,CAAC,CAAE,wBAAyB,CAAC;MAClD,IAAI,CAACiL,KAAK,CAAC,CAAC,CAACsB,OAAO,CAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EAAE,GAAI,CAAC;MACzC,IAAI,CAACtB,KAAK,CAAC,CAAC,CAACqB,OAAO,CAAE;QAAEP,KAAK,EAAE;MAAE,CAAC,EAAE,GAAG,EAAE,YAAY;QACpDtB,SAAS,CAACnJ,IAAI,CAAE,EAAG,CAAC;MACrB,CAAE,CAAC;IACJ,CAAC;IAEDwE,UAAU,EAAE,SAAAA,CAAWtB,CAAC,EAAEO,GAAG,EAAG;MAC/B;MACA,IAAK,IAAI,CAAC9B,MAAM,CAAC,CAAC,EAAG;QACpB,IAAI,CAAC2C,UAAU,CAAE;UAChBF,IAAI,EAAEzF,GAAG,CAACmC,EAAE,CAAE,2BAA4B,CAAC;UAC3CjC,IAAI,EAAE;QACP,CAAE,CAAC;QACH;MACD;;MAEA;MACA,IAAIsM,KAAK,GAAGxM,GAAG,CAACyM,aAAa,CAAE;QAC9BC,IAAI,EAAE,QAAQ;QACdxK,KAAK,EAAElC,GAAG,CAACmC,EAAE,CAAE,sBAAuB,CAAC;QACvCwK,KAAK,EAAE,IAAI,CAAC9J,GAAG,CAAE,KAAM,CAAC;QACxB+J,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,IAAI,CAAChK,GAAG,CAAE,SAAU,CAAC;QAC9BiK,YAAY,EAAE,IAAI,CAACjK,GAAG,CAAE,YAAa,CAAC;QACtCkK,QAAQ,EAAE,IAAI,CAACjK,GAAG,CAAC,CAAC;QACpBkK,MAAM,EAAElN,CAAC,CAAC8G,KAAK,CAAE,UAAWqG,UAAU,EAAE9I,CAAC,EAAG;UAC3C,IAAI,CAAC+I,gBAAgB,CAAED,UAAU,EAAE9I,CAAE,CAAC;QACvC,CAAC,EAAE,IAAK;MACT,CAAE,CAAC;IACJ,CAAC;IAED+I,gBAAgB,EAAE,SAAAA,CAAWD,UAAU,EAAE9I,CAAC,EAAG;MAC5C;MACA8I,UAAU,GAAG,IAAI,CAACE,kBAAkB,CAAEF,UAAW,CAAC;;MAElD;MACA,IAAK,IAAI,CAACjK,MAAM,CAAC,CAAC,EAAG;QACpB;MACD;;MAEA;MACA,IAAK,IAAI,CAAC4H,WAAW,CAAEqC,UAAU,CAACpC,EAAG,CAAC,CAACnI,MAAM,EAAG;QAC/C;MACD;;MAEA;MACA,IAAIrB,IAAI,GAAG,CACV,+CAA+C,GAC9C4L,UAAU,CAACpC,EAAE,GACb,IAAI,EACL,8BAA8B,GAC7BoC,UAAU,CAACpC,EAAE,GACb,UAAU,GACV,IAAI,CAACuC,YAAY,CAAC,CAAC,GACnB,MAAM,EACP,+BAA+B,EAC/B,yBAAyB,EACzB,qBAAqB,EACrB,QAAQ,EACR,8BAA8B,EAC9B,QAAQ,EACR,uBAAuB,EACvB,wEAAwE,GACvEH,UAAU,CAACpC,EAAE,GACb,QAAQ,EACT,QAAQ,EACR,QAAQ,CACR,CAACwC,IAAI,CAAE,EAAG,CAAC;MACZ,IAAI/L,KAAK,GAAGxB,CAAC,CAAEuB,IAAK,CAAC;;MAErB;MACA,IAAI,CAACqJ,WAAW,CAAC,CAAC,CAACnI,MAAM,CAAEjB,KAAM,CAAC;;MAElC;MACA,IAAK,IAAI,CAACuB,GAAG,CAAE,QAAS,CAAC,KAAK,SAAS,EAAG;QACzC,IAAIyK,OAAO,GAAG,IAAI,CAAC3C,YAAY,CAAC,CAAC,CAAC4C,EAAE,CAAEpJ,CAAE,CAAC;QACzC,IAAKmJ,OAAO,CAAC5K,MAAM,EAAG;UACrB4K,OAAO,CAAC/G,MAAM,CAAEjF,KAAM,CAAC;QACxB;MACD;;MAEA;MACA,IAAI,CAACkM,gBAAgB,CAAEP,UAAW,CAAC;;MAEnC;MACA,IAAI,CAACtJ,MAAM,CAAC,CAAC;;MAEb;MACA,IAAI,CAACE,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;IAClC,CAAC;IAEDqJ,kBAAkB,EAAE,SAAAA,CAAWF,UAAU,EAAG;MAC3C;MACAA,UAAU,GAAGjN,GAAG,CAAC0G,SAAS,CAAEuG,UAAU,EAAE;QACvCpC,EAAE,EAAE,EAAE;QACN9B,GAAG,EAAE,EAAE;QACP0E,GAAG,EAAE,EAAE;QACPvL,KAAK,EAAE,EAAE;QACTwL,QAAQ,EAAE,EAAE;QACZxN,IAAI,EAAE;MACP,CAAE,CAAC;;MAEH;MACA,IAAK+M,UAAU,CAACU,UAAU,EAAG;QAC5BV,UAAU,GAAGA,UAAU,CAACU,UAAU;;QAElC;QACA,IAAI5E,GAAG,GAAG/I,GAAG,CAAC4N,KAAK,CAClBX,UAAU,EACV,OAAO,EACP,IAAI,CAACpK,GAAG,CAAE,cAAe,CAAC,EAC1B,KACD,CAAC;QACD,IAAKkG,GAAG,KAAK,IAAI,EAAG;UACnBkE,UAAU,CAAClE,GAAG,GAAGA,GAAG;QACrB;MACD;;MAEA;MACA,OAAOkE,UAAU;IAClB,CAAC;IAEDO,gBAAgB,EAAE,SAAAA,CAAWP,UAAU,EAAG;MACzC;MACAA,UAAU,GAAG,IAAI,CAACE,kBAAkB,CAAEF,UAAW,CAAC;;MAElD;MACA,IAAInI,GAAG,GAAG,IAAI,CAAC8F,WAAW,CAAEqC,UAAU,CAACpC,EAAG,CAAC;;MAE3C;MACA,IAAKoC,UAAU,CAAC/M,IAAI,IAAI,OAAO,EAAG;QACjC;QACA4E,GAAG,CAACtD,IAAI,CAAE,WAAY,CAAC,CAACwG,MAAM,CAAC,CAAC;;QAEhC;MACD,CAAC,MAAM;QACN;QACA,IAAI6F,KAAK,GAAG7N,GAAG,CAAC4N,KAAK,CAAEX,UAAU,EAAE,OAAO,EAAE,KAAM,CAAC;QACnD,IAAKY,KAAK,KAAK,IAAI,EAAG;UACrBZ,UAAU,CAAClE,GAAG,GAAG8E,KAAK;QACvB;;QAEA;QACA/I,GAAG,CAACtD,IAAI,CAAE,WAAY,CAAC,CAACiE,IAAI,CAAEwH,UAAU,CAACS,QAAS,CAAC;MACpD;;MAEA;MACA,IAAK,CAAET,UAAU,CAAClE,GAAG,EAAG;QACvBkE,UAAU,CAAClE,GAAG,GAAG/I,GAAG,CAAC6C,GAAG,CAAE,cAAe,CAAC;QAC1CiC,GAAG,CAAC9C,QAAQ,CAAE,OAAQ,CAAC;MACxB;;MAEA;MACA8C,GAAG,CAACtD,IAAI,CAAE,KAAM,CAAC,CAACiH,IAAI,CAAE;QACvBqF,GAAG,EAAEb,UAAU,CAAClE,GAAG;QACnB0E,GAAG,EAAER,UAAU,CAACQ,GAAG;QACnBvL,KAAK,EAAE+K,UAAU,CAAC/K;MACnB,CAAE,CAAC;;MAEH;MACAlC,GAAG,CAAC8C,GAAG,CAAEgC,GAAG,CAACtD,IAAI,CAAE,OAAQ,CAAC,EAAEyL,UAAU,CAACpC,EAAG,CAAC;IAC9C,CAAC;IAEDkD,cAAc,EAAE,SAAAA,CAAWlD,EAAE,EAAG;MAC/B;MACA,IAAI2B,KAAK,GAAGxM,GAAG,CAACyM,aAAa,CAAE;QAC9BC,IAAI,EAAE,MAAM;QACZxK,KAAK,EAAElC,GAAG,CAACmC,EAAE,CAAE,YAAa,CAAC;QAC7B6L,MAAM,EAAEhO,GAAG,CAACmC,EAAE,CAAE,cAAe,CAAC;QAChC8K,UAAU,EAAEpC,EAAE;QACd8B,KAAK,EAAE,IAAI,CAAC9J,GAAG,CAAE,KAAM,CAAC;QACxBmK,MAAM,EAAElN,CAAC,CAAC8G,KAAK,CAAE,UAAWqG,UAAU,EAAE9I,CAAC,EAAG;UAC3C,IAAI,CAACqJ,gBAAgB,CAAEP,UAAW,CAAC;UACnC;QACD,CAAC,EAAE,IAAK;MACT,CAAE,CAAC;IACJ,CAAC;IAEDgB,WAAW,EAAE,SAAAA,CAAW1J,CAAC,EAAEO,GAAG,EAAG;MAChC,IAAI+F,EAAE,GAAG/F,GAAG,CAAClD,IAAI,CAAE,IAAK,CAAC;MACzB,IAAKiJ,EAAE,EAAG;QACT,IAAI,CAACkD,cAAc,CAAElD,EAAG,CAAC;MAC1B;IACD,CAAC;IAEDqD,gBAAgB,EAAE,SAAAA,CAAWrD,EAAE,EAAG;MACjC;MACA,IAAI,CAAC0B,YAAY,CAAC,CAAC;;MAEnB;MACA,IAAI,CAAC3B,WAAW,CAAEC,EAAG,CAAC,CAAC7C,MAAM,CAAC,CAAC;;MAE/B;MACA,IAAI,CAACrE,MAAM,CAAC,CAAC;;MAEb;MACA,IAAI,CAACE,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;IAClC,CAAC;IAED2D,aAAa,EAAE,SAAAA,CAAWlD,CAAC,EAAEO,GAAG,EAAG;MAClC;MACAP,CAAC,CAAC4J,cAAc,CAAC,CAAC;MAClB5J,CAAC,CAAC6J,eAAe,CAAC,CAAC;;MAEnB;MACA,IAAIvD,EAAE,GAAG/F,GAAG,CAAClD,IAAI,CAAE,IAAK,CAAC;MACzB,IAAKiJ,EAAE,EAAG;QACT,IAAI,CAACqD,gBAAgB,CAAErD,EAAG,CAAC;MAC5B;IACD,CAAC;IAEDwD,gBAAgB,EAAE,SAAAA,CAAWxD,EAAE,EAAG;MACjC;MACA,IAAI/F,GAAG,GAAG,IAAI,CAAC8F,WAAW,CAAEC,EAAG,CAAC;;MAEhC;MACA,IAAK/F,GAAG,CAACgB,QAAQ,CAAE,QAAS,CAAC,EAAG;QAC/B;MACD;;MAEA;MACA,IAAIwI,KAAK,GAAG,IAAI,CAAC1H,KAAK,CAAE,YAAY;QACnC;QACA,IAAI,CAACoE,KAAK,CAAC,CAAC,CAACxJ,IAAI,CAAE,QAAS,CAAC,CAACsC,OAAO,CAAE,MAAO,CAAC;;QAE/C;QACA,IAAI,CAACgH,OAAO,CAAC,CAAC,CAAClG,WAAW,CAAE,QAAS,CAAC;;QAEtC;QACAE,GAAG,CAAC9C,QAAQ,CAAE,QAAS,CAAC;;QAExB;QACA,IAAI,CAACmK,WAAW,CAAC,CAAC;;QAElB;QACAoC,KAAK,CAAC,CAAC;MACR,CAAE,CAAC;;MAEH;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC3H,KAAK,CAAE,YAAY;QACnC,MAAM8B,QAAQ,GAAG;UAChBC,MAAM,EAAE,mCAAmC;UAC3C6F,KAAK,EAAE,IAAI,CAAC3L,GAAG,CAAE,OAAQ,CAAC;UAC1B+F,SAAS,EAAE,IAAI,CAAC/F,GAAG,CAAE,KAAM,CAAC;UAC5BgI,EAAE,EAAEA;QACL,CAAC;;QAED;QACA,IAAK,IAAI,CAAC4D,GAAG,CAAE,KAAM,CAAC,EAAG;UACxB,IAAI,CAAC5L,GAAG,CAAE,KAAM,CAAC,CAAC6L,KAAK,CAAC,CAAC;QAC1B;;QAEA;QACA1O,GAAG,CAAC2O,WAAW,CAAE,IAAI,CAACnE,SAAS,CAAC,CAAE,CAAC;;QAEnC;QACA,IAAIoE,GAAG,GAAG9O,CAAC,CAACgJ,IAAI,CAAE;UACjBC,GAAG,EAAE/I,GAAG,CAAC6C,GAAG,CAAE,SAAU,CAAC;UACzBjB,IAAI,EAAE5B,GAAG,CAACgJ,cAAc,CAAEN,QAAS,CAAC;UACpCxI,IAAI,EAAE,MAAM;UACZ+I,QAAQ,EAAE,MAAM;UAChB4F,KAAK,EAAE,KAAK;UACZ3F,OAAO,EAAE4F;QACV,CAAE,CAAC;;QAEH;QACA,IAAI,CAAChF,GAAG,CAAE,KAAK,EAAE8E,GAAI,CAAC;MACvB,CAAE,CAAC;;MAEH;MACA,IAAIE,KAAK,GAAG,IAAI,CAAClI,KAAK,CAAE,UAAWvF,IAAI,EAAG;QACzC;QACA,IAAK,CAAEA,IAAI,EAAG;UACb;QACD;;QAEA;QACA,IAAI2J,KAAK,GAAG,IAAI,CAACR,SAAS,CAAC,CAAC;;QAE5B;QACAQ,KAAK,CAAC3J,IAAI,CAAEA,IAAK,CAAC;;QAElB;QACA2J,KAAK,CAACxJ,IAAI,CAAE,6BAA8B,CAAC,CAACwG,MAAM,CAAC,CAAC;;QAEpD;QACAgD,KAAK,CACHxJ,IAAI,CAAE,4BAA6B,CAAC,CACpCe,MAAM,CACNyI,KAAK,CAACxJ,IAAI,CAAE,0CAA2C,CACxD,CAAC;;QAEF;QACAxB,GAAG,CAACoF,QAAQ,CAAE,QAAQ,EAAE4F,KAAM,CAAC;MAChC,CAAE,CAAC;;MAEH;MACAsD,KAAK,CAAC,CAAC;IACR,CAAC;IAEDS,aAAa,EAAE,SAAAA,CAAWxK,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAI+F,EAAE,GAAG/F,GAAG,CAAClD,IAAI,CAAE,IAAK,CAAC;MACzB,IAAKiJ,EAAE,EAAG;QACT,IAAI,CAACwD,gBAAgB,CAAExD,EAAG,CAAC;MAC5B;IACD,CAAC;IAEDmE,YAAY,EAAE,SAAAA,CAAWzK,CAAC,EAAEO,GAAG,EAAG;MACjC,IAAI,CAACyH,YAAY,CAAC,CAAC;IACpB,CAAC;IAED0C,YAAY,EAAE,SAAAA,CAAW1K,CAAC,EAAEO,GAAG,EAAG;MACjC;MACA,IAAKA,GAAG,CAACgB,QAAQ,CAAE,UAAW,CAAC,EAAG;QACjC;MACD;;MAEA;MACA,IAAIhD,GAAG,GAAGgC,GAAG,CAAChC,GAAG,CAAC,CAAC;MACnB,IAAK,CAAEA,GAAG,EAAG;QACZ;MACD;;MAEA;MACA,IAAIoM,GAAG,GAAG,EAAE;MACZ,IAAI,CAACvE,YAAY,CAAC,CAAC,CAAClJ,IAAI,CAAE,YAAY;QACrCyN,GAAG,CAAC9F,IAAI,CAAEtJ,CAAC,CAAE,IAAK,CAAC,CAAC8B,IAAI,CAAE,IAAK,CAAE,CAAC;MACnC,CAAE,CAAC;;MAEH;MACA,IAAI0M,KAAK,GAAG,IAAI,CAAC1H,KAAK,CAAE,YAAY;QACnC,MAAM8B,QAAQ,GAAG;UAChBC,MAAM,EAAE,mCAAmC;UAC3C6F,KAAK,EAAE,IAAI,CAAC3L,GAAG,CAAE,OAAQ,CAAC;UAC1B+F,SAAS,EAAE,IAAI,CAAC/F,GAAG,CAAE,KAAM,CAAC;UAC5BqM,GAAG,EAAEA,GAAG;UACRC,IAAI,EAAErM;QACP,CAAC;;QAED;QACA,IAAI8L,GAAG,GAAG9O,CAAC,CAACgJ,IAAI,CAAE;UACjBC,GAAG,EAAE/I,GAAG,CAAC6C,GAAG,CAAE,SAAU,CAAC;UACzBoG,QAAQ,EAAE,MAAM;UAChB/I,IAAI,EAAE,MAAM;UACZ2O,KAAK,EAAE,KAAK;UACZjN,IAAI,EAAE5B,GAAG,CAACgJ,cAAc,CAAEN,QAAS,CAAC;UACpCQ,OAAO,EAAEqF;QACV,CAAE,CAAC;MACJ,CAAE,CAAC;;MAEH;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC3H,KAAK,CAAE,UAAWwI,IAAI,EAAG;QACzC;QACA,IAAK,CAAEpP,GAAG,CAACqP,aAAa,CAAED,IAAK,CAAC,EAAG;UAClC;QACD;;QAEA;QACAA,IAAI,CAACxN,IAAI,CAAC0N,OAAO,CAAC,CAAC;;QAEnB;QACAF,IAAI,CAACxN,IAAI,CAAC2N,GAAG,CAAE,UAAW1E,EAAE,EAAG;UAC9B,IAAI,CAACH,WAAW,CAAC,CAAC,CAAC8E,OAAO,CAAE,IAAI,CAAC5E,WAAW,CAAEC,EAAG,CAAE,CAAC;QACrD,CAAC,EAAE,IAAK,CAAC;MACV,CAAE,CAAC;;MAEH;MACAyD,KAAK,CAAC,CAAC;IACR,CAAC;IAEDrD,QAAQ,EAAE,SAAAA,CAAW1G,CAAC,EAAEO,GAAG,EAAG;MAC7B;MACA,IAAI2K,OAAO,GAAG,IAAI,CAAC3P,CAAC,CAAE,qBAAsB,CAAC;;MAE7C;MACA,IAAK2P,OAAO,CAAC3J,QAAQ,CAAE,UAAW,CAAC,EAAG;QACrC;MACD;;MAEA;MACA,MAAM4C,QAAQ,GAAG1I,GAAG,CAAC6I,SAAS,CAAE,IAAI,CAAC2B,SAAS,CAAC,CAAE,CAAC;;MAElD;MACAiF,OAAO,CAACzN,QAAQ,CAAE,UAAW,CAAC;MAC9ByN,OAAO,CAAClJ,MAAM,CAAE,8BAA+B,CAAC;;MAEhD;MACAmC,QAAQ,CAACC,MAAM,GAAG,sCAAsC;MACxDD,QAAQ,CAAC8F,KAAK,GAAG,IAAI,CAAC3L,GAAG,CAAE,OAAQ,CAAC;MACpC6F,QAAQ,CAACE,SAAS,GAAG,IAAI,CAAC/F,GAAG,CAAE,KAAM,CAAC;;MAEtC;MACA/C,CAAC,CAACgJ,IAAI,CAAE;QACPC,GAAG,EAAE/I,GAAG,CAAC6C,GAAG,CAAE,SAAU,CAAC;QACzBjB,IAAI,EAAE5B,GAAG,CAACgJ,cAAc,CAAEN,QAAS,CAAC;QACpCxI,IAAI,EAAE,MAAM;QACZ+I,QAAQ,EAAE,MAAM;QAChBhB,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrBwH,OAAO,CAAC7K,WAAW,CAAE,UAAW,CAAC;UACjC6K,OAAO,CAACC,IAAI,CAAE,cAAe,CAAC,CAAC1H,MAAM,CAAC,CAAC;QACxC;MACD,CAAE,CAAC;IACJ,CAAC;IAEDsB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB;MACA,IAAI,CAACrG,WAAW,CAAE,IAAK,CAAC;;MAExB;MACA,IAAI,CAACsG,GAAG,CAAE,WAAY,CAAC;IACxB;EACD,CAAE,CAAC;EAEHvJ,GAAG,CAACwJ,iBAAiB,CAAEzJ,KAAM,CAAC;;EAE9B;EACAC,GAAG,CAAC2J,6BAA6B,CAAE,UAAU,EAAE,SAAU,CAAC;EAC1D3J,GAAG,CAAC2J,6BAA6B,CAAE,YAAY,EAAE,SAAU,CAAC;EAC5D3J,GAAG,CAAC2J,6BAA6B,CAAE,mBAAmB,EAAE,SAAU,CAAC;EACnE3J,GAAG,CAAC2J,6BAA6B,CAAE,sBAAsB,EAAE,SAAU,CAAC;AACvE,CAAC,EAAIO,MAAO,CAAC;;;;;;;;;;ACtmBb,CAAE,UAAWpK,CAAC,EAAG;EAChB,IAAIC,KAAK,GAAGC,GAAG,CAACD,KAAK,CAACE,MAAM,CAAE;IAC7BC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,EAAE;IACRwP,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,CAAC;IAEbxP,MAAM,EAAE;MACP,+BAA+B,EAAE,YAAY;MAC7C,qCAAqC,EAAE,kBAAkB;MACzD,kCAAkC,EAAE,eAAe;MACnD,oCAAoC,EAAE,iBAAiB;MACvD,iDAAiD,EAAG,kBAAkB;MACtE,gDAAgD,EAAG,iBAAiB;MACpE,gDAAgD,EAAE,iBAAiB;MACnE,gDAAgD,EAAE,iBAAiB;MACnE,sBAAsB,EAAE,qBAAqB;MAC7C,6BAA6B,EAAE,iBAAiB;MAChD,uBAAuB,EAAE,gBAAgB;MACzC,yBAAyB,EAAE,kBAAkB;MAC7C,oBAAoB,EAAE,mBAAmB;MACzCC,SAAS,EAAE,QAAQ;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE,SAAS;MACpBsP,MAAM,EAAE;IACT,CAAC;IAEDrP,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACV,CAAC,CAAE,qBAAsB,CAAC;IACvC,CAAC;IAEDgQ,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAAChQ,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDiQ,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACjQ,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDkQ,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAAClQ,CAAC,CAAE,kBAAmB,CAAC,CAACmQ,GAAG,CAAE,0BAA2B,CAAC;IACtE,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAWtP,KAAK,EAAG;MACxB,OAAO,IAAI,CAACd,CAAC,CAAE,sBAAsB,GAAGc,KAAK,GAAG,GAAI,CAAC;IACtD,CAAC;IAEDG,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACjB,CAAC,CAAE,4BAA6B,CAAC;IAC9C,CAAC;IAEDmB,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACnB,CAAC,CAAE,mBAAoB,CAAC;IACrC,CAAC;IAEDoB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACpB,CAAC,CAAE,2BAA4B,CAAC;IAC7C,CAAC;IAEDqQ,gBAAgB,EAAE,SAAAA,CAAA,EAAW;MAC5B,OAAO,IAAI,CAACrQ,CAAC,CAAE,gCAAiC,CAAC;IAClD,CAAC;IAEDsQ,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,OAAO,IAAI,CAACtQ,CAAC,CAAE,+BAAgC,CAAC;IACjD,CAAC;IAEDuQ,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,OAAO,IAAI,CAACvQ,CAAC,CAAE,+BAAgC,CAAC;IACjD,CAAC;IAEDwQ,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,OAAO,IAAI,CAACxQ,CAAC,CAAE,+BAAgC,CAAC;IACjD,CAAC;IAEDyQ,UAAU,EAAE,SAAAA,CAAA,EAAW;MACtB,OAAO,IAAI,CAACzQ,CAAC,CAAE,oBAAqB,CAAC;IACtC,CAAC;IAED0Q,UAAU,EAAE,SAAAA,CAAA,EAAW;MACtB,MAAMA,UAAU,GAAG,IAAI,CAAC1Q,CAAC,CAAE,uBAAwB,CAAC,CAAC2F,IAAI,CAAC,CAAC;MAC3D,OAAO7C,QAAQ,CAAE4N,UAAW,CAAC;IAC9B,CAAC;IAED/N,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACuN,KAAK,CAAC,CAAC,CAACtN,MAAM;IAC3B,CAAC;IAEDC,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,IAAI8N,OAAO,GAAG,IAAI,CAAC3N,GAAG,CAAC,CAAC;MACxB,IAAI4N,OAAO,GAAG9N,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAM,CAAE,CAAC;MAE3C,IAAK,IAAI,CAACA,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B4N,OAAO,GAAG,IAAI,CAAC5N,GAAG,CAAE,YAAa,CAAC;MACnC;MAEA,OAAO,CAAE6N,OAAO,IAAIA,OAAO,GAAGD,OAAO;IACtC,CAAC;IAED1N,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAI0N,OAAO,GAAG,IAAI,CAAC3N,GAAG,CAAC,CAAC;MACxB,IAAI6N,OAAO,GAAG/N,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAM,CAAE,CAAC;MAE3C,IAAK,IAAI,CAACA,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B4N,OAAO,GAAG,IAAI,CAAC5N,GAAG,CAAE,YAAa,CAAC;MACnC;MAEA,OAAO,CAAE8N,OAAO,IAAIA,OAAO,GAAGF,OAAO;IACtC,CAAC;IAEDxN,WAAW,EAAE,SAAAA,CAAW1B,IAAI,EAAG;MAC9B;MACA,IAAK,IAAI,CAACsB,GAAG,CAAE,KAAM,CAAC,IAAI,CAAC,EAAG;QAC7B;MACD;;MAEA;MACA,IAAK,IAAI,CAACA,GAAG,CAAE,YAAY,CAAC,EAAG;QAC9B;MACD;;MAEA;MACA,IAAI,CAACkN,MAAM,CAAC,CAAC,CAAC7M,QAAQ,CAAE;QACvBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,YAAY;QACpBC,eAAe,EAAE,IAAI;QACrBC,oBAAoB,EAAE,IAAI;QAC1BC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,SAAAA,CAAWC,KAAK,EAAEC,EAAE,EAAG;UAC5BnC,IAAI,CAACoC,MAAM,CAAC,CAAC;QACd,CAAC;QACDC,MAAM,EAAE,SAAAA,CAAWH,KAAK,EAAEC,EAAE,EAAG;UAC9BnC,IAAI,CAACsC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;QAClC;MACD,CAAE,CAAC;IACJ,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAIC,OAAO,GAAGC,UAAU,CAACC,IAAI,CAAE,IAAI,CAACrB,GAAG,CAAE,KAAM,CAAE,CAAC;;MAElD;MACA,IAAK,CAAEmB,OAAO,EAAG;QAChB,OAAO,KAAK;MACb;;MAEA;MACA,IAAI,CAACgM,KAAK,CAAC,CAAC,CAACvO,IAAI,CAAE,UAAW0C,CAAC,EAAG;QACjC,IAAKH,OAAO,CAACI,OAAO,CAAED,CAAE,CAAC,GAAG,CAAC,CAAC,EAAG;UAChC,IAAKrE,CAAC,CAAE,IAAK,CAAC,CAAC0B,IAAI,CAAE,oBAAqB,CAAC,CAACkB,MAAM,EAAG;YACpD5C,CAAC,CAAE,IAAK,CAAC,CAACkC,QAAQ,CAAE,YAAa,CAAC;UACnC;QACD;MACD,CAAE,CAAC;IACJ,CAAC;IAEDqC,iBAAiB,EAAE,SAAAA,CAAW9C,IAAI,EAAG;MACpC;MACA,IAAI,CAAC+C,EAAE,CAAE,cAAc,EAAE,UAAU,EAAE,UAAWC,CAAC,EAAG;QACnD,IAAI2L,IAAI,GAAGpQ,CAAC,CAAE,IAAK,CAAC;QACpB,IAAKyB,IAAI,CAACqP,WAAW,CAAEV,IAAK,CAAC,EAAG;UAC/B3O,IAAI,CAACsP,MAAM,CAAEX,IAAK,CAAC;QACpB;MACD,CAAE,CAAC;;MAEH;MACA,IAAK,IAAI,CAACrN,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B,IAAI,CAACyB,EAAE,CAAE,QAAQ,EAAE,yBAAyB,EAAE,UAAWC,CAAC,EAAG;UAC5D,MAAMuM,QAAQ,GAAGhR,CAAC,CAAEyE,CAAC,CAACwM,aAAc,CAAC;UACrC,IAAK,CAAED,QAAQ,CAAChL,QAAQ,CAAE,iBAAkB,CAAC,IAAI,CAAEgL,QAAQ,CAAChL,QAAQ,CAAE,gBAAiB,CAAC,EAAG;YAC1FvE,IAAI,CAACyP,aAAa,CAAEzM,CAAC,EAAEzE,CAAC,CAAE,IAAK,CAAE,CAAC;UACnC;QACD,CAAE,CAAC;MACJ;MAEA,IAAI,CAACmR,uBAAuB,CAAC,CAAC;IAC/B,CAAC;IAEDxM,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAI,CAACJ,iBAAiB,CAAE,IAAK,CAAC;;MAE9B;MACA,IAAI,CAACN,YAAY,CAAC,CAAC;;MAEnB;MACA/D,GAAG,CAAC0E,OAAO,CAAE,IAAI,CAAC3D,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC4D,GAAI,CAAC;;MAEtC;MACA,IAAK,IAAI,CAAC9B,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B,IAAI,CAAC+M,UAAU,GAAG,IAAI,CAAC/M,GAAG,CAAE,YAAa,CAAC;MAC3C;;MAEA;MACA,IAAI,CAACc,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAWuN,oBAAoB,GAAG,IAAI,EAAG;MAChD;MACA,IAAKA,oBAAoB,EAAG;QAC3B,IAAI,CAAClB,KAAK,CAAC,CAAC,CAACvO,IAAI,CAAE,UAAW0C,CAAC,EAAG;UACjCrE,CAAC,CAAE,IAAK,CAAC,CACP0B,IAAI,CAAE,iBAAkB,CAAC,CACzBH,IAAI,CAAE8C,CAAC,GAAG,CAAE,CAAC;QAChB,CAAE,CAAC;MACJ;;MAEA;MACA,IAAI3D,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;MAC9B,IAAIU,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;;MAE5B;MACA,IAAK,IAAI,CAAC4B,GAAG,CAAC,CAAC,IAAI,CAAC,EAAG;QACtBtC,QAAQ,CAACwB,QAAQ,CAAE,QAAS,CAAC;MAC9B,CAAC,MAAM;QACNxB,QAAQ,CAACoE,WAAW,CAAE,QAAS,CAAC;MACjC;;MAEA;MACA,IAAK,CAAE,IAAI,CAAC7B,QAAQ,CAAC,CAAC,EAAG;QACxBvC,QAAQ,CAACwB,QAAQ,CAAE,MAAO,CAAC;QAC3Bd,OAAO,CAACc,QAAQ,CAAE,UAAW,CAAC;MAC/B,CAAC,MAAM;QACNxB,QAAQ,CAACoE,WAAW,CAAE,MAAO,CAAC;QAC9B1D,OAAO,CAAC0D,WAAW,CAAE,UAAW,CAAC;MAClC;MAEA,IAAK,IAAI,CAAC/B,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B,IAAI,CAACsO,sBAAsB,CAAC,CAAC;MAC9B;;MAEA;MACA;MACA;MACA;MACA;MACA;IACD,CAAC;IAEDF,uBAAuB,EAAE,SAAAA,CAAA,EAAW;MACnC,IAAK,CAAEjR,GAAG,CAACoR,qBAAqB,CAAC,CAAC,IAAI,CAAE,IAAI,CAACvO,GAAG,CAAE,YAAa,CAAC,EAAG;QAClE;MACD;MAEA,IAAIwO,gBAAgB,GAAG,IAAI;MAC3BC,EAAE,CAAC1P,IAAI,CAAC2P,SAAS,CAAE,MAAM;QACxB,IAAKD,EAAE,CAAC1P,IAAI,CAACoL,MAAM,CAAE,gBAAiB,CAAC,CAACwE,iBAAiB,CAAC,CAAC,EAAG;UAC7DH,gBAAgB,GAAG,KAAK;QACzB,CAAC,MAAM;UACN,IAAK,CAAEA,gBAAgB,EAAG;YACzBA,gBAAgB,GAAG,IAAI;YACvB,IAAI,CAACvH,GAAG,CAAE,YAAY,EAAE,CAAC,EAAE,IAAK,CAAC;YACjC,IAAI,CAAC2H,YAAY,CAAE,IAAK,CAAC;UAC1B;QACD;MACD,CAAE,CAAC;IACJ,CAAC;IAEDC,kBAAkB,EAAE,SAAAA,CAAA,EAAW;MAC9B,IAAIC,SAAS,GAAG,IAAI,CAAC9O,GAAG,CAAE,YAAa,CAAC;MACxC,IAAI,CAACiH,GAAG,CAAE,YAAY,EAAE,EAAE6H,SAAS,EAAE,IAAK,CAAC;IAC5C,CAAC;IAEDC,kBAAkB,EAAE,SAAAA,CAAA,EAAW;MAC9B,IAAID,SAAS,GAAG,IAAI,CAAC9O,GAAG,CAAE,YAAa,CAAC;MACxC,IAAI,CAACiH,GAAG,CAAE,YAAY,EAAE,EAAE6H,SAAS,EAAE,IAAK,CAAC;IAC5C,CAAC;IAED/L,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAK,IAAI,CAAC7C,QAAQ,CAAC,CAAC,EAAG;QACtB,OAAO,IAAI;MACZ;;MAEA;MACA,IAAIlB,GAAG,GAAG,IAAI,CAACgB,GAAG,CAAE,KAAM,CAAC;MAC3B,IAAI4C,IAAI,GAAGzF,GAAG,CAACmC,EAAE,CAAE,mCAAoC,CAAC;;MAExD;MACAsD,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,OAAO,EAAET,GAAI,CAAC;;MAEnC;MACA,IAAI,CAAC8D,UAAU,CAAE;QAChBF,IAAI,EAAEA,IAAI;QACVvF,IAAI,EAAE;MACP,CAAE,CAAC;;MAEH;MACA,OAAO,KAAK;IACb,CAAC;IAED2F,UAAU,EAAE,SAAAA,CAAWtB,CAAC,EAAEO,GAAG,EAAG;MAC/B;MACA,IAAK,CAAE,IAAI,CAACc,WAAW,CAAC,CAAC,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAKd,GAAG,CAACgB,QAAQ,CAAE,UAAW,CAAC,EAAG;QACjC,IAAI,CAACO,GAAG,CAAE;UACTE,MAAM,EAAEzB,GAAG,CAACiB,OAAO,CAAE,UAAW;QACjC,CAAE,CAAC;;QAEH;MACD,CAAC,MAAM;QACN,IAAI,CAACM,GAAG,CAAC,CAAC;MACX;IACD,CAAC;IAEDA,GAAG,EAAE,SAAAA,CAAWI,IAAI,EAAG;MACtB;MACA,IAAK,CAAE,IAAI,CAAC1D,QAAQ,CAAC,CAAC,EAAG;QACxB,OAAO,KAAK;MACb;;MAEA;MACA0D,IAAI,GAAGzG,GAAG,CAAC0G,SAAS,CAAED,IAAI,EAAE;QAC3BF,MAAM,EAAE;MACT,CAAE,CAAC;;MAEH;MACA,IAAIzB,GAAG,GAAG9E,GAAG,CAAC2G,SAAS,CAAE;QACxBT,MAAM,EAAE,IAAI,CAACnF,MAAM,CAAC,CAAC;QACrBwB,MAAM,EAAE,IAAI,CAACqE,KAAK,CAAE,UAAW9B,GAAG,EAAE+B,IAAI,EAAG;UAC1C;UACA,IAAKJ,IAAI,CAACF,MAAM,EAAG;YAClBE,IAAI,CAACF,MAAM,CAACA,MAAM,CAAEM,IAAK,CAAC;UAC3B,CAAC,MAAM;YACN/B,GAAG,CAACyB,MAAM,CAAEM,IAAK,CAAC;UACnB;;UAEA;UACAA,IAAI,CAACjC,WAAW,CAAE,WAAY,CAAC;;UAE/B;UACA5E,GAAG,CAAC8G,MAAM,CAAED,IAAI,EAAE,IAAI,CAAClC,GAAI,CAAC;QAC7B,CAAE;MACH,CAAE,CAAC;MAEH,IAAK,IAAI,CAAC9B,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B,IAAI,CAAC6O,kBAAkB,CAAC,CAAC;QAEzB,IAAK,KAAK,KAAKjL,IAAI,CAACF,MAAM,EAAG;UAC5B;UACA,MAAMsL,UAAU,GAAGjP,QAAQ,CAAE6D,IAAI,CAACF,MAAM,CAAC/E,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACvB,IAAI,CAAC,CAAE,CAAC,IAAI,CAAC;UACxF,IAAIqM,SAAS,GAAGD,UAAU;UAE1B,IAAKC,SAAS,IAAI,CAAErL,IAAI,CAACF,MAAM,CAACT,QAAQ,CAAE,cAAe,CAAC,IAAI,CAAEW,IAAI,CAACF,MAAM,CAACT,QAAQ,CAAE,WAAY,CAAC,EAAG;YACrG,EAAEgM,SAAS;UACZ;UAEA,IAAKrL,IAAI,CAACF,MAAM,CAACT,QAAQ,CAAE,aAAc,CAAC,EAAG;YAC5CW,IAAI,CAACF,MAAM,CAAC3B,WAAW,CAAE,aAAc,CAAC;YACxCE,GAAG,CAAC9C,QAAQ,CAAE,aAAc,CAAC;UAC9B;UAEA,IAAI,CAAC+P,eAAe,CAAEjN,GAAG,EAAE,UAAW,CAAC;UACvC,IAAI,CAACiN,eAAe,CAAEjN,GAAG,EAAE,WAAW,EAAEgN,SAAU,CAAC;;UAEnD;UACAhN,GAAG,CAACtD,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACgL,IAAI,CAAC,CAAC,CAACvM,IAAI,CAAEqM,SAAU,CAAC;UAC9D,IAAK,CAAEhN,GAAG,CAACtD,IAAI,CAAE,uBAAwB,CAAC,CAACsE,QAAQ,CAAE,UAAW,CAAC,EAAG;YACnE,IAAImM,OAAO,GAAIjS,GAAG,CAACmC,EAAE,CAAE,kCAAmC,CAAC;YAC3D2C,GAAG,CAACtD,IAAI,CAAE,uBAAwB,CAAC,CAACQ,QAAQ,CAAE,UAAW,CAAC;YAC1D8C,GAAG,CAACtD,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACM,KAAK,CAAE,eAAe,GAAG2K,OAAO,GAAG,YAAa,CAAC;UACxF;UACAnN,GAAG,CAACtD,IAAI,CAAE,kBAAmB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACgL,IAAI,CAAC,CAAC;UAC7ClN,GAAG,CAAC2D,IAAI,CAAE,eAAe,EAAEqJ,SAAU,CAAC;QACvC,CAAC,MAAM;UACN,IAAI,CAAClC,UAAU,EAAE;UAEjB9K,GAAG,CAACtD,IAAI,CAAE,kBAAmB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAAClE,GAAG,CAAE,IAAI,CAAC8M,UAAW,CAAC;UAC7D9K,GAAG,CAACtD,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACvB,IAAI,CAAE,IAAI,CAACmK,UAAW,CAAC;UAC7D,IAAI,CAACmC,eAAe,CAAEjN,GAAG,EAAE,OAAQ,CAAC;UAEpC,IAAK,CAAE,IAAI,CAACiL,MAAM,CAAC,CAAC,CAACvO,IAAI,CAAE,cAAe,CAAC,CAACkB,MAAM,EAAG;YACpDoC,GAAG,CAAC9C,QAAQ,CAAE,aAAc,CAAC;UAC9B;QACD;QAEA8C,GAAG,CAACtD,IAAI,CAAE,kBAAmB,CAAC,CAC5BA,IAAI,CAAE,4CAA6C,CAAC,CACpDwF,KAAK,CAAC,CAAC,CACPlD,OAAO,CAAE,OAAQ,CAAC;MACrB;;MAEA;MACA,IAAI,CAACH,MAAM,CAAC,CAAC;MACb,IAAI,CAACE,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;MAEjC,OAAOgB,GAAG;IACX,CAAC;IAEDiC,gBAAgB,EAAE,SAAAA,CAAWxC,CAAC,EAAEO,GAAG,EAAG;MACrC;MACA,IAAK,CAAE,IAAI,CAACc,WAAW,CAAC,CAAC,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAIsK,IAAI,GAAGpL,GAAG,CAACiB,OAAO,CAAE,UAAW,CAAC;MACpC,IAAI,CAACmM,YAAY,CAAEhC,IAAK,CAAC;IAC1B,CAAC;IAEDgC,YAAY,EAAE,SAAAA,CAAWhC,IAAI,EAAG;MAC/B;MACA,IAAK,CAAE,IAAI,CAACnN,QAAQ,CAAC,CAAC,EAAG;QACxB,OAAO,KAAK;MACb;;MAEA;MACA,IAAImE,QAAQ,GAAG,IAAI,CAACrE,GAAG,CAAE,KAAM,CAAC;;MAEhC;MACA,IAAIiC,GAAG,GAAG9E,GAAG,CAAC2G,SAAS,CAAE;QACxBT,MAAM,EAAEgK,IAAI;QAEZ;QACA/I,MAAM,EAAE,SAAAA,CAAWnG,IAAI,EAAEoG,KAAK,EAAEC,MAAM,EAAE/E,OAAO,EAAG;UACjD;UACA,IAAKtB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAG;YACtC,OAAOoG,KAAK,CAAC9E,OAAO,CACnB4E,QAAQ,GAAG,GAAG,GAAGG,MAAM,EACvBH,QAAQ,GAAG,GAAG,GAAG5E,OAClB,CAAC;;YAED;UACD,CAAC,MAAM;YACN,OAAO8E,KAAK,CAAC9E,OAAO,CACnB4E,QAAQ,GAAG,IAAI,GAAGG,MAAM,EACxBH,QAAQ,GAAG,IAAI,GAAG5E,OACnB,CAAC;UACF;QACD,CAAC;QACDiE,MAAM,EAAE,SAAAA,CAAWzB,GAAG,EAAG;UACxB9E,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEN,GAAI,CAAC;QAC/B,CAAC;QACDwC,KAAK,EAAE,SAAAA,CAAWxC,GAAG,EAAE+B,IAAI,EAAG;UAC7B7G,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEN,GAAI,CAAC;QAC/B;MACD,CAAE,CAAC;MAEH,IAAK,IAAI,CAACjC,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B,IAAI,CAAC6O,kBAAkB,CAAC,CAAC;;QAEzB;QACA,MAAMG,UAAU,GAAGjP,QAAQ,CAAEsN,IAAI,CAAC1O,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACvB,IAAI,CAAC,CAAE,CAAC,IAAI,CAAC;QAEjF,IAAI,CAACsM,eAAe,CAAEjN,GAAG,EAAE,UAAW,CAAC;QACvC,IAAI,CAACiN,eAAe,CAAEjN,GAAG,EAAE,WAAW,EAAE+M,UAAW,CAAC;;QAEpD;QACA/M,GAAG,CAACtD,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACgL,IAAI,CAAC,CAAC;QAC5C,IAAK,CAAElN,GAAG,CAACtD,IAAI,CAAE,uBAAwB,CAAC,CAACsE,QAAQ,CAAE,UAAW,CAAC,EAAG;UACnE,IAAImM,OAAO,GAAIjS,GAAG,CAACmC,EAAE,CAAE,kCAAmC,CAAC;UAC3D2C,GAAG,CAACtD,IAAI,CAAE,uBAAwB,CAAC,CAACQ,QAAQ,CAAE,UAAW,CAAC;UAC1D8C,GAAG,CAACtD,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACM,KAAK,CAAE,eAAe,GAAG2K,OAAO,GAAG,YAAa,CAAC;QACxF;QACAnN,GAAG,CAACtD,IAAI,CAAE,kBAAmB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACgL,IAAI,CAAC,CAAC;QAC7ClN,GAAG,CAAC2D,IAAI,CAAE,eAAe,EAAEoJ,UAAW,CAAC;QACvC/M,GAAG,CAACF,WAAW,CAAE,aAAc,CAAC;MACjC;;MAEA;MACA,IAAI,CAACf,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;;MAEjC;MACA,IAAI,CAACH,MAAM,CAAC,CAAC;;MAEb;MACA3D,GAAG,CAACuH,cAAc,CAAEzC,GAAI,CAAC;;MAEzB;MACA,OAAOA,GAAG;IACX,CAAC;IAED0C,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B;MACA,IAAK,IAAI,CAAC7E,WAAW,CAAC,CAAC,EAAG;QACzB,OAAO,IAAI;MACZ;;MAEA;MACA,IAAIhB,GAAG,GAAG,IAAI,CAACkB,GAAG,CAAE,KAAM,CAAC;MAC3B,IAAI4C,IAAI,GAAGzF,GAAG,CAACmC,EAAE,CAAE,uCAAwC,CAAC;;MAE5D;MACAsD,IAAI,GAAGA,IAAI,CAACnD,OAAO,CAAE,OAAO,EAAEX,GAAI,CAAC;;MAEnC;MACA,IAAI,CAACgE,UAAU,CAAE;QAChBF,IAAI,EAAEA,IAAI;QACVvF,IAAI,EAAE;MACP,CAAE,CAAC;;MAEH;MACA,OAAO,KAAK;IACb,CAAC;IAEDuH,aAAa,EAAE,SAAAA,CAAWlD,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAIoL,IAAI,GAAGpL,GAAG,CAACiB,OAAO,CAAE,UAAW,CAAC;;MAEpC;MACA,IAAKxB,CAAC,CAACmD,QAAQ,EAAG;QACjB,OAAO,IAAI,CAACM,MAAM,CAAEkI,IAAK,CAAC;MAC3B;;MAEA;MACAA,IAAI,CAAClO,QAAQ,CAAE,QAAS,CAAC;;MAEzB;MACA,IAAI4F,OAAO,GAAG5H,GAAG,CAAC6H,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnB5B,MAAM,EAAEpB,GAAG;QACXC,OAAO,EAAE,IAAI;QACbqB,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB,IAAI,CAAC4B,MAAM,CAAEkI,IAAK,CAAC;QACpB,CAAC;QACD1J,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB0J,IAAI,CAACtL,WAAW,CAAE,QAAS,CAAC;QAC7B;MACD,CAAE,CAAC;IACJ,CAAC;IAEDuN,eAAe,EAAE,SAAAA,CAAU5N,CAAC,EAAEO,GAAG,EAAG;MACnC,IAAK,CAAE,IAAI,CAACjC,GAAG,CAAE,YAAa,CAAC,EAAG;QACjC;MACD;MAEA,IAAKiC,GAAG,CAACgB,QAAQ,CAAE,UAAW,CAAC,EAAG;QACjC;MACD;MAEAhB,GAAG,CAACtD,IAAI,CAAE,iBAAkB,CAAC,CAACwQ,IAAI,CAAC,CAAC;MACpClN,GAAG,CAACtD,IAAI,CAAE,kBAAmB,CAAC,CAAC4Q,IAAI,CAAC,CAAC,CAACtO,OAAO,CAAE,QAAS,CAAC;IAC1D,CAAC;IAEDuO,cAAc,EAAE,SAAAA,CAAU9N,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAI,CAACwN,gBAAgB,CAAE/N,CAAC,EAAEO,GAAG,EAAE,KAAM,CAAC;IACvC,CAAC;IAEDwN,gBAAgB,EAAE,SAAAA,CAAU/N,CAAC,EAAEO,GAAG,EAAElB,MAAM,GAAG,IAAI,EAAG;MACnD,IAAK,CAAE,IAAI,CAACf,GAAG,CAAE,YAAa,CAAC,EAAG;QACjC;MACD;MAEA,MAAMqN,IAAI,GAAGpL,GAAG,CAACiB,OAAO,CAAE,UAAW,CAAC;MACtC,MAAMwM,UAAU,GAAGrC,IAAI,CAAC1O,IAAI,CAAE,iBAAkB,CAAC,CAACwF,KAAK,CAAC,CAAC;MACzD,IAAIwL,OAAO,GAAG1N,GAAG,CAAChC,GAAG,CAAC,CAAC;MAEvBoN,IAAI,CAAC1O,IAAI,CAAE,kBAAmB,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACgL,IAAI,CAAC,CAAC;MAE9C,IAAK,CAAEhS,GAAG,CAACyS,SAAS,CAAED,OAAQ,CAAC,IAAIE,UAAU,CAAEF,OAAQ,CAAC,GAAG,CAAC,EAAG;QAC9DD,UAAU,CAACH,IAAI,CAAC,CAAC;QACjB;MACD;MAEAI,OAAO,GAAGxG,IAAI,CAACC,KAAK,CAAEuG,OAAQ,CAAC;MAE/B,MAAMG,QAAQ,GAAGH,OAAO,GAAG,CAAC;MAE5B1N,GAAG,CAAChC,GAAG,CAAE0P,OAAQ,CAAC;MAClBD,UAAU,CAAC9M,IAAI,CAAE+M,OAAQ,CAAC,CAACJ,IAAI,CAAC,CAAC;MAEjC,IAAKxO,MAAM,EAAG;QACb,IAAI,CAACmO,eAAe,CAAE7B,IAAI,EAAE,WAAW,EAAEyC,QAAS,CAAC;MACpD;IACD,CAAC;IAEDC,iBAAiB,EAAE,SAAAA,CAAA,EAAW;MAC7B,MAAMC,OAAO,GAAGjQ,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,UAAW,CAAE,CAAC,IAAI,EAAE;MACxD,MAAM8O,SAAS,GAAG/O,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,YAAa,CAAE,CAAC,IAAI,CAAC;MAC3D,MAAM2N,UAAU,GAAGxE,IAAI,CAAC8G,IAAI,CAAEnB,SAAS,GAAGkB,OAAQ,CAAC;;MAEnD;MACA,IAAI,CAAC/S,CAAC,CAAE,uBAAwB,CAAC,CAAC2F,IAAI,CAAE+K,UAAW,CAAC;MACpD,IAAI,CAACZ,UAAU,GAAG+B,SAAS;;MAE3B;MACA,IAAK,IAAI,CAAChC,IAAI,GAAGa,UAAU,EAAG;QAC7B,IAAI,CAACb,IAAI,GAAGa,UAAU;QACtB,IAAI,CAACiB,YAAY,CAAC,CAAC;MACpB;IACD,CAAC;IAEDzJ,MAAM,EAAE,SAAAA,CAAWkI,IAAI,EAAG;MACzB,MAAM3O,IAAI,GAAG,IAAI;MAEjB,IAAK,IAAI,CAACsB,GAAG,CAAE,YAAa,CAAC,EAAG;QAC/B,IAAI,CAAC+O,kBAAkB,CAAC,CAAC;;QAEzB;QACA,IAAK1B,IAAI,CAACtO,IAAI,CAAE,IAAK,CAAC,CAACmR,QAAQ,CAAE,MAAO,CAAC,EAAE;UAC1C,IAAI,CAAChB,eAAe,CAAE7B,IAAI,EAAE,SAAU,CAAC;UAEvCA,IAAI,CAAC8B,IAAI,CAAC,CAAC;UACXzQ,IAAI,CAACsC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;UACjCvC,IAAI,CAACoC,MAAM,CAAE,KAAM,CAAC;UACpB;QACD,CAAC,MAAM,IAAKuM,IAAI,CAACpK,QAAQ,CAAE,aAAc,CAAC,EAAG;UAC5CoK,IAAI,CAAC8C,IAAI,CAAE,YAAa,CAAC,CAAChR,QAAQ,CAAE,aAAc,CAAC;QACpD;MACD;;MAEA;MACAhC,GAAG,CAACgI,MAAM,CAAE;QACX9B,MAAM,EAAEgK,IAAI;QACZnI,SAAS,EAAE,CAAC;QACZE,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB;UACA1G,IAAI,CAACsC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAE,QAAS,CAAC;;UAEjC;UACAvC,IAAI,CAACoC,MAAM,CAAC,CAAC;;UAEb;UACA;QACD;MACD,CAAE,CAAC;IACJ,CAAC;IAEDiN,WAAW,EAAE,SAAAA,CAAWV,IAAI,EAAG;MAC9B,OAAOA,IAAI,CAACpK,QAAQ,CAAE,YAAa,CAAC;IACrC,CAAC;IAEDmN,QAAQ,EAAE,SAAAA,CAAW/C,IAAI,EAAG;MAC3BA,IAAI,CAAClO,QAAQ,CAAE,YAAa,CAAC;MAC7BhC,GAAG,CAACoF,QAAQ,CAAE,MAAM,EAAE8K,IAAI,EAAE,UAAW,CAAC;IACzC,CAAC;IAEDW,MAAM,EAAE,SAAAA,CAAWX,IAAI,EAAG;MACzBA,IAAI,CAACtL,WAAW,CAAE,YAAa,CAAC;MAChC5E,GAAG,CAACoF,QAAQ,CAAE,MAAM,EAAE8K,IAAI,EAAE,UAAW,CAAC;IACzC,CAAC;IAEDhI,eAAe,EAAE,SAAAA,CAAW3D,CAAC,EAAEO,GAAG,EAAG;MACpC;MACA,IAAIoL,IAAI,GAAGpL,GAAG,CAACiB,OAAO,CAAE,UAAW,CAAC;MACpC,IAAImN,WAAW,GAAG,IAAI,CAACtC,WAAW,CAAEV,IAAK,CAAC;;MAE1C;MACA,IAAK3L,CAAC,CAACmD,QAAQ,EAAG;QACjBwI,IAAI,GAAG,IAAI,CAACF,KAAK,CAAC,CAAC;MACpB;;MAEA;MACA,IAAKkD,WAAW,EAAG;QAClB,IAAI,CAACrC,MAAM,CAAEX,IAAK,CAAC;MACpB,CAAC,MAAM;QACN,IAAI,CAAC+C,QAAQ,CAAE/C,IAAK,CAAC;MACtB;IACD,CAAC;IAEDrL,MAAM,EAAE,SAAAA,CAAWN,CAAC,EAAEO,GAAG,EAAEC,OAAO,EAAG;MACpC;MACA,IAAIC,MAAM,GAAGhF,GAAG,CAACiF,SAAS,CAAE;QAC3BC,EAAE,EAAE,UAAU;QACdC,MAAM,EAAE,IAAI,CAACL;MACd,CAAE,CAAC;;MAEH;MACA;MACA;MACA9E,GAAG,CAACoF,QAAQ,CAAE,aAAa,EAAEJ,MAAO,CAAC;IACtC,CAAC;IAEDmE,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAInF,OAAO,GAAG,EAAE;;MAEhB;MACA,IAAI,CAACgM,KAAK,CAAC,CAAC,CAACvO,IAAI,CAAE,UAAW0C,CAAC,EAAG;QACjC,IAAKrE,CAAC,CAAE,IAAK,CAAC,CAACgG,QAAQ,CAAE,YAAa,CAAC,EAAG;UACzC9B,OAAO,CAACoF,IAAI,CAAEjF,CAAE,CAAC;QAClB;MACD,CAAE,CAAC;;MAEH;MACAH,OAAO,GAAGA,OAAO,CAACtB,MAAM,GAAGsB,OAAO,GAAG,IAAI;;MAEzC;MACAC,UAAU,CAACoF,IAAI,CAAE,IAAI,CAACxG,GAAG,CAAE,KAAM,CAAC,EAAEmB,OAAQ,CAAC;IAC9C,CAAC;IAEDsF,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB;MACA,IAAI,CAACrG,WAAW,CAAE,IAAK,CAAC;;MAExB;MACA,IAAI,CAACsG,GAAG,CAAE,WAAY,CAAC;IACxB,CAAC;IAEDyH,aAAa,EAAE,SAAAA,CAAUzM,CAAC,EAAEO,GAAG,EAAG;MACjC,MAAMqO,OAAO,GAAGrT,CAAC,CAAEyE,CAAC,CAAC6O,cAAe,CAAC;MACrC,IAAIlD,IAAI,GAAGpL,GAAG,CAACiB,OAAO,CAAE,UAAW,CAAC;MAEpC,IAAKmK,IAAI,CAACnK,OAAO,CAAE,qBAAsB,CAAC,CAACnE,IAAI,CAAE,KAAM,CAAC,KAAKuR,OAAO,CAACvR,IAAI,CAAE,KAAM,CAAC,EAAG;QACpFsO,IAAI,GAAGA,IAAI,CAAC/K,MAAM,CAAC,CAAC,CAACY,OAAO,CAAE,UAAW,CAAC;MAC3C;MAEA,IAAI,CAACgM,eAAe,CAAE7B,IAAI,EAAE,SAAU,CAAC;IACxC,CAAC;IAED6B,eAAe,EAAE,SAAAA,CAAU7B,IAAI,EAAEmD,MAAM,EAAEzR,IAAI,GAAG,IAAI,EAAG;MACtD,IAAK,CAAE,IAAI,CAACiB,GAAG,CAAE,YAAa,CAAC,EAAG;QACjC;MACD;MAEA,MAAMyQ,UAAU,GAAGpD,IAAI,CAACqD,OAAO,CAAE,qBAAsB,CAAC,CAAC3R,IAAI,CAAE,KAAM,CAAC;MAEtE,IAAK,IAAI,CAACuD,MAAM,CAAC,CAAC,IAAImO,UAAU,KAAK,IAAI,CAACzQ,GAAG,CAAE,KAAM,CAAC,EAAG;QACxD;MACD;MAEA,MAAM2Q,MAAM,GAAGtD,IAAI,CAACtO,IAAI,CAAE,IAAK,CAAC;MAChC,MAAM6R,UAAU,GAAG,IAAI,CAAC3O,GAAG,CAACtD,IAAI,CAAE,kCAAmC,CAAC,CAACiH,IAAI,CAAE,MAAO,CAAC;MACrF,MAAMiL,WAAW,GAAG,GAAGD,UAAU,IAAID,MAAM,SAASH,MAAM,GAAG;MAC7D,MAAMM,YAAY,GAAG,qDAAqDD,WAAW,YAAY9R,IAAI,MAAM;MAE3G,IAAK,CAAEsO,IAAI,CAACpK,QAAQ,CAAE,MAAM,GAAGuN,MAAO,CAAC,EAAG;QACzCnD,IAAI,CAAClO,QAAQ,CAAE,MAAM,GAAGqR,MAAO,CAAC;MACjC;;MAEA;MACA,MAAMO,gBAAgB,GAAG1D,IAAI,CAAC1O,IAAI,CAAE,eAAekS,WAAW,IAAK,CAAC;MACpE,IAAK,CAAEE,gBAAgB,CAAClR,MAAM,EAAG;QAChCwN,IAAI,CAAC1O,IAAI,CAAE,IAAK,CAAC,CAACwF,KAAK,CAAC,CAAC,CAACzE,MAAM,CAAEoR,YAAa,CAAC;MACjD,CAAC,MAAM;QACNC,gBAAgB,CAAC9Q,GAAG,CAAElB,IAAK,CAAC;MAC7B;IACD,CAAC;IAEDiS,gBAAgB,EAAE,SAAAA,CAAA,EAAW;MAC5B,IAAI,CAACC,YAAY,CAAE,CAAE,CAAC;IACvB,CAAC;IAEDC,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,IAAI,CAACD,YAAY,CAAE,IAAI,CAACnE,IAAI,GAAG,CAAE,CAAC;IACnC,CAAC;IAEDqE,eAAe,EAAE,SAAAA,CAAUzP,CAAC,EAAG;MAC9B,IAAI,CAACuP,YAAY,CAAE,IAAI,CAACnE,IAAI,GAAG,CAAE,CAAC;IACnC,CAAC;IAEDsE,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,IAAI,CAACH,YAAY,CAAE,IAAI,CAACtD,UAAU,CAAC,CAAE,CAAC;IACvC,CAAC;IAED0D,mBAAmB,EAAE,SAAAA,CAAA,EAAW;MAC/B,IAAI,CAACJ,YAAY,CAAE,IAAI,CAACvD,UAAU,CAAC,CAAC,CAACzN,GAAG,CAAC,CAAE,CAAC;IAC7C,CAAC;IAEDqO,sBAAsB,EAAE,SAAAA,CAAA,EAAW;MAClC,IAAI,CAAClQ,QAAQ,CAAC,CAAC,CAACO,IAAI,CAAE,UAAW,CAAC,CAACoD,WAAW,CAAE,UAAW,CAAC;MAE5D,IAAK,IAAI,CAAC+K,IAAI,IAAI,CAAC,EAAG;QACrB,IAAI,CAACQ,gBAAgB,CAAC,CAAC,CAACnO,QAAQ,CAAE,UAAW,CAAC;QAC9C,IAAI,CAACoO,eAAe,CAAC,CAAC,CAACpO,QAAQ,CAAE,UAAW,CAAC;MAC9C;MAEA,IAAK,IAAI,CAAC2N,IAAI,IAAI,IAAI,CAACa,UAAU,CAAC,CAAC,EAAG;QACrC,IAAI,CAACH,eAAe,CAAC,CAAC,CAACrO,QAAQ,CAAE,UAAW,CAAC;QAC7C,IAAI,CAACsO,eAAe,CAAC,CAAC,CAACtO,QAAQ,CAAE,UAAW,CAAC;MAC9C;IACD,CAAC;IAED8R,YAAY,EAAE,SAAAA,CAAUK,QAAQ,EAAG;MAClC,MAAM5S,IAAI,GAAG,IAAI;;MAEjB;MACAvB,GAAG,CAACoU,YAAY,CAAE;QACjBC,IAAI,EAAE,IAAI,CAAC7T,QAAQ,CAAC,CAAC;QACrBiD,KAAK,EAAE,EAAE;QACT6Q,KAAK,EAAE,IAAI;QACXpL,OAAO,EAAE,SAAAA,CAAUqL,KAAK,EAAG;UAC1BhT,IAAI,CAACoO,IAAI,GAAGwE,QAAQ;;UAEpB;UACA,IAAK5S,IAAI,CAACoO,IAAI,IAAI,CAAC,EAAG;YACrBpO,IAAI,CAACoO,IAAI,GAAG,CAAC;UACd;UACA,IAAKpO,IAAI,CAACoO,IAAI,IAAIpO,IAAI,CAACiP,UAAU,CAAC,CAAC,EAAG;YACrCjP,IAAI,CAACoO,IAAI,GAAGpO,IAAI,CAACiP,UAAU,CAAC,CAAC;UAC9B;UAEAjP,IAAI,CAACkQ,YAAY,CAAC,CAAC;QACpB,CAAC;QACD+C,OAAO,EAAE,SAAAA,CAAUD,KAAK,EAAG;UAC1BhT,IAAI,CAACgP,UAAU,CAAC,CAAC,CAACzN,GAAG,CAAEvB,IAAI,CAACoO,IAAK,CAAC;UAClC,OAAO,KAAK;QACb;MACD,CAAE,CAAC;IACJ,CAAC;IAED8B,YAAY,EAAE,SAAAA,CAAUgD,YAAY,GAAG,KAAK,EAAG;MAC9C,MAAM/L,QAAQ,GAAG1I,GAAG,CAACgJ,cAAc,CAAE;QACpCL,MAAM,EAAE,yBAAyB;QACjC+L,KAAK,EAAE,IAAI,CAAC/E,IAAI;QAChB/G,SAAS,EAAE,IAAI,CAAC/F,GAAG,CAAE,KAAM,CAAC;QAC5B8R,UAAU,EAAE,IAAI,CAAC9R,GAAG,CAAE,WAAY,CAAC;QACnC+R,aAAa,EAAEhS,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,UAAW,CAAE,CAAC;QACjDgS,OAAO,EAAEJ,YAAY;QACrBjG,KAAK,EAAE,IAAI,CAAC3L,GAAG,CAAE,OAAQ;MAC1B,CAAE,CAAC;MAEH/C,CAAC,CAACgJ,IAAI,CACL;QACCC,GAAG,EAAE+L,OAAO;QACZC,MAAM,EAAE,MAAM;QACd9L,QAAQ,EAAE,MAAM;QAChBrH,IAAI,EAAE8G,QAAQ;QACd3D,OAAO,EAAE;MACV,CACD,CAAC,CAACiQ,IAAI,CACL,UAAUC,QAAQ,EAAG;QACpB,MAAM;UAAEC;QAAK,CAAC,GAAGD,QAAQ,CAACrT,IAAI;QAC9B,MAAMuT,aAAa,GAAG,IAAI,CAACpF,MAAM,CAAC,CAAC,CAACvO,IAAI,CAAE,MAAO,CAAC;QAElD2T,aAAa,CAAClF,GAAG,CAAE,YAAa,CAAC,CAAC+B,IAAI,CAAC,CAAC;QAExC,IAAKyC,YAAY,EAAG;UACnB;UACAU,aAAa,CAAClF,GAAG,CAAE,YAAa,CAAC,CAACjI,MAAM,CAAC,CAAC;;UAE1C;UACA,IAAI,CAAC8B,GAAG,CAAE,YAAY,EAAEmL,QAAQ,CAACrT,IAAI,CAACwT,UAAU,EAAE,KAAM,CAAC;QAC1D,CAAC,MAAM;UACND,aAAa,CAAClF,GAAG,CAAE,mFAAoF,CAAC,CAACjI,MAAM,CAAC,CAAC;QAClH;QAEAqN,MAAM,CAACC,IAAI,CAAEJ,IAAK,CAAC,CAACK,OAAO,CAAE3U,KAAK,IAAI;UACrC,IAAIsP,IAAI,GAAW,KAAK;UACxB,IAAIsF,WAAW,GAAI,IAAI,CAACzF,MAAM,CAAC,CAAC,CAACvO,IAAI,CAAE,kBAAkB,GAAGZ,KAAK,GAAG,GAAI,CAAC;UACzE,IAAI6U,YAAY,GAAG,IAAI,CAAC1F,MAAM,CAAC,CAAC,CAACvO,IAAI,CAAE,oBAAoB,GAAGZ,KAAK,GAAG,GAAI,CAAC;;UAE3E;UACA,IAAK6U,YAAY,CAAC/S,MAAM,EAAG;YAC1B+S,YAAY,CAACC,QAAQ,CAAE,IAAI,CAAC3F,MAAM,CAAC,CAAE,CAAC,CAACqC,IAAI,CAAC,CAAC;YAC7CpS,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEqQ,YAAa,CAAC;UACxC;;UAEA;UACA,IAAKD,WAAW,CAAC1P,QAAQ,CAAE,aAAc,CAAC,EAAG;YAC5C;UACD;;UAEA;UACA,IAAK0P,WAAW,CAAC9S,MAAM,EAAG;YACzB1C,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEoQ,WAAY,CAAC;YACtCA,WAAW,CAACE,QAAQ,CAAE,IAAI,CAAC3F,MAAM,CAAC,CAAE,CAAC,CAACqC,IAAI,CAAC,CAAC;YAC5CpS,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEoQ,WAAY,CAAC;YACtC;UACD;;UAEA;UACAtF,IAAI,GAAGpQ,CAAC,CAAEoV,IAAI,CAAEtU,KAAK,CAAG,CAAC;UACzB,IAAI,CAACmP,MAAM,CAAC,CAAC,CAACxN,MAAM,CAAE2N,IAAK,CAAC,CAACkC,IAAI,CAAC,CAAC;UACnCpS,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAE8K,IAAK,CAAC;;UAE/B;UACA,IAAI,CAACnP,MAAM,CAAC,CAAC,CAAC2U,QAAQ,CAAE,IAAI,CAAC3F,MAAM,CAAC,CAAE,CAAC;QACxC,CAAE,CAAC;QAEH,MAAM4F,UAAU,GAAG,IAAI,CAAC5F,MAAM,CAAC,CAAC,CAACvO,IAAI,CAAE,mBAAoB,CAAC;;QAE5D;QACA,IAAKmU,UAAU,CAACjT,MAAM,EAAG;UACxB,MAAMnB,IAAI,GAAG,IAAI;UAEjBoU,UAAU,CAAClU,IAAI,CAAE,YAAW;YAC3B,MAAMmU,SAAS,GAAG9V,CAAC,CAAE,IAAK,CAAC;YAC3B8V,SAAS,CAACC,YAAY,CAAEtU,IAAI,CAACR,MAAM,CAAC,CAAE,CAAC,CAACqR,IAAI,CAAC,CAAC;YAC9CpS,GAAG,CAACoF,QAAQ,CAAE,SAAS,EAAEwQ,SAAU,CAAC;UACrC,CAAE,CAAC;QACJ;;QAEA;QACA,IAAI,CAACrF,UAAU,CAAC,CAAC,CAACzN,GAAG,CAAE,IAAI,CAAC6M,IAAK,CAAC;QAClC,IAAI,CAACwB,sBAAsB,CAAC,CAAC;MAC9B,CACD,CAAC,CAAC2E,IAAI,CACL,UAAUC,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAG;QAC1C,MAAMC,KAAK,GAAGlW,GAAG,CAACmW,WAAW,CAAEJ,KAAM,CAAC;QACtC,IAAI9D,OAAO,GAAGjS,GAAG,CAACmC,EAAE,CAAE,oBAAqB,CAAC;QAE5C,IAAK,EAAE,KAAK+T,KAAK,EAAG;UACnBjE,OAAO,GAAG,GAAGA,OAAO,KAAKiE,KAAK,EAAE;QACjC;QAEA,IAAI,CAACvQ,UAAU,CAAE;UAChBF,IAAI,EAAEwM,OAAO;UACb/R,IAAI,EAAE;QACP,CAAE,CAAC;MACJ,CACD,CAAC;IACF;EAED,CAAE,CAAC;EAEHF,GAAG,CAACwJ,iBAAiB,CAAEzJ,KAAM,CAAC;;EAE9B;EACAC,GAAG,CAAC2J,6BAA6B,CAAE,UAAU,EAAE,UAAW,CAAC;EAC3D3J,GAAG,CAAC2J,6BAA6B,CAAE,YAAY,EAAE,UAAW,CAAC;EAC7D3J,GAAG,CAAC2J,6BAA6B,CAAE,UAAU,EAAE,UAAW,CAAC;EAC3D3J,GAAG,CAAC2J,6BAA6B,CAAE,aAAa,EAAE,UAAW,CAAC;;EAE9D;EACA,IAAI1F,UAAU,GAAG,IAAIjE,GAAG,CAAC4J,KAAK,CAAE;IAC/B5I,IAAI,EAAE,oBAAoB;IAE1B6I,GAAG,EAAE,SAAAA,CAAWA,GAAG,EAAE9E,OAAO,EAAG;MAC9B;MACA,IAAIjD,KAAK,GAAG,IAAI,CAACe,GAAG,CAAEgH,GAAG,GAAG9E,OAAQ,CAAC,IAAI,CAAC;;MAE1C;MACAjD,KAAK,EAAE;MACP,IAAI,CAACgI,GAAG,CAAED,GAAG,GAAG9E,OAAO,EAAEjD,KAAK,EAAE,IAAK,CAAC;;MAEtC;MACA,IAAKA,KAAK,GAAG,CAAC,EAAG;QAChB+H,GAAG,IAAI,GAAG,GAAG/H,KAAK;MACnB;;MAEA;MACA,OAAO+H,GAAG;IACX,CAAC;IAED3F,IAAI,EAAE,SAAAA,CAAW2F,GAAG,EAAG;MACtB;MACA,IAAIA,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAO,CAAC;MACjC,IAAIjI,IAAI,GAAG5B,GAAG,CAAC+J,aAAa,CAAE,IAAI,CAAC/I,IAAK,CAAC;;MAEzC;MACA,IAAKY,IAAI,IAAIA,IAAI,CAAEiI,GAAG,CAAE,EAAG;QAC1B,OAAOjI,IAAI,CAAEiI,GAAG,CAAE;MACnB,CAAC,MAAM;QACN,OAAO,KAAK;MACb;IACD,CAAC;IAEDR,IAAI,EAAE,SAAAA,CAAWQ,GAAG,EAAEzC,KAAK,EAAG;MAC7B;MACA,IAAIyC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAO,CAAC;MACjC,IAAIjI,IAAI,GAAG5B,GAAG,CAAC+J,aAAa,CAAE,IAAI,CAAC/I,IAAK,CAAC,IAAI,CAAC,CAAC;;MAE/C;MACA,IAAKoG,KAAK,KAAK,IAAI,EAAG;QACrB,OAAOxF,IAAI,CAAEiI,GAAG,CAAE;;QAElB;MACD,CAAC,MAAM;QACNjI,IAAI,CAAEiI,GAAG,CAAE,GAAGzC,KAAK;MACpB;;MAEA;MACA,IAAKtH,CAAC,CAACkK,aAAa,CAAEpI,IAAK,CAAC,EAAG;QAC9BA,IAAI,GAAG,IAAI;MACZ;;MAEA;MACA5B,GAAG,CAACiK,aAAa,CAAE,IAAI,CAACjJ,IAAI,EAAEY,IAAK,CAAC;IACrC;EACD,CAAE,CAAC;AACJ,CAAC,EAAIsI,MAAO,CAAC;;;;;;UCp8Bb;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;ACNkC;AACQ", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-field-flexible-content.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-field-gallery.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-field-repeater.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/acf-pro-input.js"], "sourcesContent": ["( function ( $ ) {\n\tvar Field = acf.Field.extend( {\n\t\ttype: 'flexible_content',\n\t\twait: '',\n\n\t\tevents: {\n\t\t\t'click [data-name=\"add-layout\"]': 'onClickAdd',\n\t\t\t'click [data-name=\"duplicate-layout\"]': 'onClickDuplicate',\n\t\t\t'click [data-name=\"remove-layout\"]': 'onClickRemove',\n\t\t\t'click [data-name=\"collapse-layout\"]': 'onClickCollapse',\n\t\t\tshowField: 'onShow',\n\t\t\tunloadField: 'onUnload',\n\t\t\tmouseover: 'onHover',\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first' );\n\t\t},\n\n\t\t$layoutsWrap: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .values' );\n\t\t},\n\n\t\t$layouts: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .values > .layout' );\n\t\t},\n\n\t\t$layout: function ( index ) {\n\t\t\treturn this.$(\n\t\t\t\t'.acf-flexible-content:first > .values > .layout:eq(' +\n\t\t\t\t\tindex +\n\t\t\t\t\t')'\n\t\t\t);\n\t\t},\n\n\t\t$clonesWrap: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .clones' );\n\t\t},\n\n\t\t$clones: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .clones  > .layout' );\n\t\t},\n\n\t\t$clone: function ( name ) {\n\t\t\treturn this.$(\n\t\t\t\t'.acf-flexible-content:first > .clones  > .layout[data-layout=\"' +\n\t\t\t\t\tname +\n\t\t\t\t\t'\"]'\n\t\t\t);\n\t\t},\n\n\t\t$actions: function () {\n\t\t\treturn this.$( '.acf-actions:last' );\n\t\t},\n\n\t\t$button: function () {\n\t\t\treturn this.$( '.acf-actions:last .button' );\n\t\t},\n\n\t\t$popup: function () {\n\t\t\treturn this.$( '.tmpl-popup:last' );\n\t\t},\n\n\t\tgetPopupHTML: function () {\n\t\t\tvar html = this.$popup().html();\n\t\t\tvar $html = $( html );\n\t\t\tvar self = this;\n\n\t\t\t// modify popup\n\t\t\t$html.find( '[data-layout]' ).each( function () {\n\t\t\t\tvar $a = $( this );\n\t\t\t\tvar min = $a.data( 'min' ) || 0;\n\t\t\t\tvar max = $a.data( 'max' ) || 0;\n\t\t\t\tvar name = $a.data( 'layout' ) || '';\n\t\t\t\tvar count = self.countLayouts( name );\n\n\t\t\t\t// max\n\t\t\t\tif ( max && count >= max ) {\n\t\t\t\t\t$a.addClass( 'disabled' );\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// min\n\t\t\t\tif ( min && count < min ) {\n\t\t\t\t\tvar required = min - count;\n\t\t\t\t\tvar title = acf.__(\n\t\t\t\t\t\t'{required} {label} {identifier} required (min {min})'\n\t\t\t\t\t);\n\t\t\t\t\tvar identifier = acf._n( 'layout', 'layouts', required );\n\n\t\t\t\t\t// translate\n\t\t\t\t\ttitle = title.replace( '{required}', required );\n\t\t\t\t\ttitle = title.replace( '{label}', name ); // 5.5.0\n\t\t\t\t\ttitle = title.replace( '{identifier}', identifier );\n\t\t\t\t\ttitle = title.replace( '{min}', min );\n\n\t\t\t\t\t// badge\n\t\t\t\t\t$a.append(\n\t\t\t\t\t\t'<span class=\"badge\" title=\"' +\n\t\t\t\t\t\t\ttitle +\n\t\t\t\t\t\t\t'\">' +\n\t\t\t\t\t\t\trequired +\n\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// update\n\t\t\thtml = $html.outerHTML();\n\n\t\t\treturn html;\n\t\t},\n\n\t\tgetValue: function () {\n\t\t\treturn this.$layouts().length;\n\t\t},\n\n\t\tallowRemove: function () {\n\t\t\tvar min = parseInt( this.get( 'min' ) );\n\t\t\treturn ! min || min < this.val();\n\t\t},\n\n\t\tallowAdd: function () {\n\t\t\tvar max = parseInt( this.get( 'max' ) );\n\t\t\treturn ! max || max > this.val();\n\t\t},\n\n\t\tisFull: function () {\n\t\t\tvar max = parseInt( this.get( 'max' ) );\n\t\t\treturn max && this.val() >= max;\n\t\t},\n\n\t\taddSortable: function ( self ) {\n\t\t\t// bail early if max 1 row\n\t\t\tif ( this.get( 'max' ) == 1 ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// add sortable\n\t\t\tthis.$layoutsWrap().sortable( {\n\t\t\t\titems: '> .layout',\n\t\t\t\thandle: '> .acf-fc-layout-handle',\n\t\t\t\tforceHelperSize: true,\n\t\t\t\tforcePlaceholderSize: true,\n\t\t\t\tscroll: true,\n\t\t\t\tstop: function ( event, ui ) {\n\t\t\t\t\tself.render();\n\t\t\t\t},\n\t\t\t\tupdate: function ( event, ui ) {\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\taddCollapsed: function () {\n\t\t\tvar indexes = preference.load( this.get( 'key' ) );\n\n\t\t\t// bail early if no collapsed\n\t\t\tif ( ! indexes ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// loop\n\t\t\tthis.$layouts().each( function ( i ) {\n\t\t\t\tif ( indexes.indexOf( i ) > -1 ) {\n\t\t\t\t\t$( this ).addClass( '-collapsed' );\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t\taddUnscopedEvents: function ( self ) {\n\t\t\t// invalidField\n\t\t\tthis.on( 'invalidField', '.layout', function ( e ) {\n\t\t\t\tself.onInvalidField( e, $( this ) );\n\t\t\t} );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// add unscoped events\n\t\t\tthis.addUnscopedEvents( this );\n\n\t\t\t// add collapsed\n\t\t\tthis.addCollapsed();\n\n\t\t\t// disable clone\n\t\t\tacf.disable( this.$clonesWrap(), this.cid );\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\t// update order number\n\t\t\tthis.$layouts().each( function ( i ) {\n\t\t\t\t$( this )\n\t\t\t\t\t.find( '.acf-fc-layout-order:first' )\n\t\t\t\t\t.html( i + 1 );\n\t\t\t} );\n\n\t\t\t// empty\n\t\t\tif ( this.val() == 0 ) {\n\t\t\t\tthis.$control().addClass( '-empty' );\n\t\t\t} else {\n\t\t\t\tthis.$control().removeClass( '-empty' );\n\t\t\t}\n\n\t\t\t// max\n\t\t\tif ( this.isFull() ) {\n\t\t\t\tthis.$button().addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\tthis.$button().removeClass( 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tonShow: function ( e, $el, context ) {\n\t\t\t// get sub fields\n\t\t\tvar fields = acf.getFields( {\n\t\t\t\tis: ':visible',\n\t\t\t\tparent: this.$el,\n\t\t\t} );\n\n\t\t\t// trigger action\n\t\t\t// - ignore context, no need to pass through 'conditional_logic'\n\t\t\t// - this is just for fields like google_map to render itself\n\t\t\tacf.doAction( 'show_fields', fields );\n\t\t},\n\n\t\tcountLayouts: function ( name ) {\n\t\t\treturn this.$layouts().filter( function () {\n\t\t\t\treturn $( this ).data( 'layout' ) === name;\n\t\t\t} ).length;\n\t\t},\n\n\t\tcountLayoutsByName: function ( currentLayout ) {\n\t\t\tconst layoutMax = currentLayout.data( 'max' );\n\t\t\tif ( ! layoutMax ) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tconst name = currentLayout.data( 'layout' ) || '';\n\t\t\tconst count = this.countLayouts( name );\n\n\t\t\tif( count >= layoutMax ) {\n\t\t\t\tlet text = acf.__(\n\t\t\t\t\t'This field has a limit of {max} {label} {identifier}'\n\t\t\t\t);\n\t\t\t\tconst identifier = acf._n( 'layout', 'layouts', layoutMax );\n\t\t\t\tconst layoutLabel = '\"' + currentLayout.data( 'label' ) + '\"';\n\t\t\t\ttext = text.replace( '{max}', layoutMax );\n\t\t\t\ttext = text.replace( '{label}', layoutLabel );\n\t\t\t\ttext = text.replace( '{identifier}', identifier );\n\n\t\t\t\tthis.showNotice( {\n\t\t\t\t\ttext: text,\n\t\t\t\t\ttype: 'warning',\n\t\t\t\t} );\n\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tvalidateAdd: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowAdd() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar max = this.get( 'max' );\n\t\t\tvar text = acf.__(\n\t\t\t\t'This field has a limit of {max} {label} {identifier}'\n\t\t\t);\n\t\t\tvar identifier = acf._n( 'layout', 'layouts', max );\n\n\t\t\ttext = text.replace( '{max}', max );\n\t\t\ttext = text.replace( '{label}', '' );\n\t\t\ttext = text.replace( '{identifier}', identifier );\n\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// validate\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// within layout\n\t\t\tvar $layout = null;\n\t\t\tif ( $el.hasClass( 'acf-icon' ) ) {\n\t\t\t\t$layout = $el.closest( '.layout' );\n\t\t\t\t$layout.addClass( '-hover' );\n\t\t\t}\n\n\t\t\t// new popup\n\t\t\tvar popup = new Popup( {\n\t\t\t\ttarget: $el,\n\t\t\t\ttargetConfirm: false,\n\t\t\t\ttext: this.getPopupHTML(),\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function ( e, $el ) {\n\t\t\t\t\t// check disabled\n\t\t\t\t\tif ( $el.hasClass( 'disabled' ) ) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// add\n\t\t\t\t\tthis.add( {\n\t\t\t\t\t\tlayout: $el.data( 'layout' ),\n\t\t\t\t\t\tbefore: $layout,\n\t\t\t\t\t} );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\tif ( $layout ) {\n\t\t\t\t\t\t$layout.removeClass( '-hover' );\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// add extra event\n\t\t\tpopup.on( 'click', '[data-layout]', 'onConfirm' );\n\t\t},\n\n\t\tadd: function ( args ) {\n\t\t\t// defaults\n\t\t\targs = acf.parseArgs( args, {\n\t\t\t\tlayout: '',\n\t\t\t\tbefore: false,\n\t\t\t} );\n\n\t\t\t// validate\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// add row\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: this.$clone( args.layout ),\n\t\t\t\tappend: this.proxy( function ( $el, $el2 ) {\n\t\t\t\t\t// append\n\t\t\t\t\tif ( args.before ) {\n\t\t\t\t\t\targs.before.before( $el2 );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$layoutsWrap().append( $el2 );\n\t\t\t\t\t}\n\n\t\t\t\t\t// enable\n\t\t\t\t\tacf.enable( $el2, this.cid );\n\n\t\t\t\t\t// render\n\t\t\t\t\tthis.render();\n\t\t\t\t} ),\n\t\t\t} );\n\n\t\t\t// trigger change for validation errors\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\treturn $el;\n\t\t},\n\n\t\tonClickDuplicate: function ( e, $el ) {\n\t\t\tvar $layout = $el.closest( '.layout' );\n\t\t\t// Validate each layout's max count.\n\t\t\tif ( ! this.countLayoutsByName( $layout.first() ) ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// Validate with warning.\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// get layout and duplicate it.\n\t\t\tthis.duplicateLayout( $layout );\n\t\t},\n\n\t\tduplicateLayout: function ( $layout ) {\n\t\t\t// Validate without warning.\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tvar fieldKey = this.get( 'key' );\n\n\t\t\t// Duplicate layout.\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: $layout,\n\n\t\t\t\t// Provide a custom renaming callback to avoid renaming parent row attributes.\n\t\t\t\trename: function ( name, value, search, replace ) {\n\t\t\t\t\t// Rename id attributes from \"field_1-search\" to \"field_1-replace\".\n\t\t\t\t\tif ( name === 'id' || name === 'for' ) {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '-' + search,\n\t\t\t\t\t\t\tfieldKey + '-' + replace\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// Rename name and for attributes from \"[field_1][search]\" to \"[field_1][replace]\".\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '][' + search,\n\t\t\t\t\t\t\tfieldKey + '][' + replace\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tbefore: function ( $el ) {\n\t\t\t\t\tacf.doAction( 'unmount', $el );\n\t\t\t\t},\n\t\t\t\tafter: function ( $el, $el2 ) {\n\t\t\t\t\tacf.doAction( 'remount', $el );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// trigger change for validation errors\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\t// Update order numbers.\n\t\t\tthis.render();\n\n\t\t\t// Draw focus to layout.\n\t\t\tacf.focusAttention( $el );\n\n\t\t\t// Return new layout.\n\t\t\treturn $el;\n\t\t},\n\n\t\tvalidateRemove: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowRemove() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar min = this.get( 'min' );\n\t\t\tvar text = acf.__(\n\t\t\t\t'This field requires at least {min} {label} {identifier}'\n\t\t\t);\n\t\t\tvar identifier = acf._n( 'layout', 'layouts', min );\n\n\t\t\t// replace\n\t\t\ttext = text.replace( '{min}', min );\n\t\t\ttext = text.replace( '{label}', '' );\n\t\t\ttext = text.replace( '{identifier}', identifier );\n\n\t\t\t// add notice\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\tvar $layout = $el.closest( '.layout' );\n\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.removeLayout( $layout );\n\t\t\t}\n\n\t\t\t// add class\n\t\t\t$layout.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.removeLayout( $layout );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\t$layout.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tremoveLayout: function ( $layout ) {\n\t\t\t// reference\n\t\t\tvar self = this;\n\n\t\t\tvar endHeight = this.getValue() == 1 ? 60 : 0;\n\n\t\t\t// remove\n\t\t\tacf.remove( {\n\t\t\t\ttarget: $layout,\n\t\t\t\tendHeight: endHeight,\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t// trigger change to allow attachment save\n\t\t\t\t\tself.$input().trigger( 'change' );\n\n\t\t\t\t\t// render\n\t\t\t\t\tself.render();\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonClickCollapse: function ( e, $el ) {\n\t\t\tvar $layout = $el.closest( '.layout' );\n\n\t\t\t// toggle\n\t\t\tif ( this.isLayoutClosed( $layout ) ) {\n\t\t\t\tthis.openLayout( $layout );\n\t\t\t} else {\n\t\t\t\tthis.closeLayout( $layout );\n\t\t\t}\n\t\t},\n\n\t\tisLayoutClosed: function ( $layout ) {\n\t\t\treturn $layout.hasClass( '-collapsed' );\n\t\t},\n\n\t\topenLayout: function ( $layout ) {\n\t\t\t$layout.removeClass( '-collapsed' );\n\t\t\tacf.doAction( 'show', $layout, 'collapse' );\n\t\t},\n\n\t\tcloseLayout: function ( $layout ) {\n\t\t\t$layout.addClass( '-collapsed' );\n\t\t\tacf.doAction( 'hide', $layout, 'collapse' );\n\n\t\t\t// render\n\t\t\t// - no change could happen if layout was already closed. Only render when closing\n\t\t\tthis.renderLayout( $layout );\n\t\t},\n\n\t\trenderLayout: function ( $layout ) {\n\t\t\tvar $input = $layout.children( 'input' );\n\t\t\tvar prefix = $input.attr( 'name' ).replace( '[acf_fc_layout]', '' );\n\n\t\t\t// ajax data\n\t\t\tvar ajaxData = {\n\t\t\t\taction: 'acf/fields/flexible_content/layout_title',\n\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\ti: $layout.index(),\n\t\t\t\tlayout: $layout.data( 'layout' ),\n\t\t\t\tvalue: acf.serialize( $layout, prefix ),\n\t\t\t};\n\n\t\t\t// ajax\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\tdataType: 'html',\n\t\t\t\ttype: 'post',\n\t\t\t\tsuccess: function ( html ) {\n\t\t\t\t\tif ( html ) {\n\t\t\t\t\t\t$layout\n\t\t\t\t\t\t\t.children( '.acf-fc-layout-handle' )\n\t\t\t\t\t\t\t.html( html );\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonUnload: function () {\n\t\t\tvar indexes = [];\n\n\t\t\t// loop\n\t\t\tthis.$layouts().each( function ( i ) {\n\t\t\t\tif ( $( this ).hasClass( '-collapsed' ) ) {\n\t\t\t\t\tindexes.push( i );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// allow null\n\t\t\tindexes = indexes.length ? indexes : null;\n\n\t\t\t// set\n\t\t\tpreference.save( this.get( 'key' ), indexes );\n\t\t},\n\n\t\tonInvalidField: function ( e, $layout ) {\n\t\t\t// open if is collapsed\n\t\t\tif ( this.isLayoutClosed( $layout ) ) {\n\t\t\t\tthis.openLayout( $layout );\n\t\t\t}\n\t\t},\n\n\t\tonHover: function () {\n\t\t\t// add sortable\n\t\t\tthis.addSortable( this );\n\n\t\t\t// remove event\n\t\t\tthis.off( 'mouseover' );\n\t\t},\n\t} );\n\n\tacf.registerFieldType( Field );\n\n\t/**\n\t *  Popup\n\t *\n\t *  description\n\t *\n\t *  @date\t7/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar Popup = acf.models.TooltipConfirm.extend( {\n\t\tevents: {\n\t\t\t'click [data-layout]': 'onConfirm',\n\t\t\t'click [data-event=\"cancel\"]': 'onCancel',\n\t\t},\n\n\t\trender: function () {\n\t\t\t// set HTML\n\t\t\tthis.html( this.get( 'text' ) );\n\n\t\t\t// add class\n\t\t\tthis.$el.addClass( 'acf-fc-popup' );\n\t\t},\n\t} );\n\n\t/**\n\t *  conditions\n\t *\n\t *  description\n\t *\n\t *  @date\t9/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\t// register existing conditions\n\tacf.registerConditionForFieldType( 'hasValue', 'flexible_content' );\n\tacf.registerConditionForFieldType( 'hasNoValue', 'flexible_content' );\n\tacf.registerConditionForFieldType( 'lessThan', 'flexible_content' );\n\tacf.registerConditionForFieldType( 'greaterThan', 'flexible_content' );\n\n\t// state\n\tvar preference = new acf.Model( {\n\t\tname: 'this.collapsedLayouts',\n\n\t\tkey: function ( key, context ) {\n\t\t\tvar count = this.get( key + context ) || 0;\n\n\t\t\t// update\n\t\t\tcount++;\n\t\t\tthis.set( key + context, count, true );\n\n\t\t\t// modify fieldKey\n\t\t\tif ( count > 1 ) {\n\t\t\t\tkey += '-' + count;\n\t\t\t}\n\n\t\t\treturn key;\n\t\t},\n\n\t\tload: function ( key ) {\n\t\t\tvar key = this.key( key, 'load' );\n\t\t\tvar data = acf.getPreference( this.name );\n\n\t\t\tif ( data && data[ key ] ) {\n\t\t\t\treturn data[ key ];\n\t\t\t} else {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\n\t\tsave: function ( key, value ) {\n\t\t\tvar key = this.key( key, 'save' );\n\t\t\tvar data = acf.getPreference( this.name ) || {};\n\n\t\t\t// delete\n\t\t\tif ( value === null ) {\n\t\t\t\tdelete data[ key ];\n\n\t\t\t\t// append\n\t\t\t} else {\n\t\t\t\tdata[ key ] = value;\n\t\t\t}\n\n\t\t\t// allow null\n\t\t\tif ( $.isEmptyObject( data ) ) {\n\t\t\t\tdata = null;\n\t\t\t}\n\n\t\t\t// save\n\t\t\tacf.setPreference( this.name, data );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $ ) {\n\tvar Field = acf.Field.extend( {\n\t\ttype: 'gallery',\n\n\t\tevents: {\n\t\t\t'click .acf-gallery-add': 'onClickAdd',\n\t\t\t'click .acf-gallery-edit': 'onClickEdit',\n\t\t\t'click .acf-gallery-remove': 'onClickRemove',\n\t\t\t'click .acf-gallery-attachment': 'onClickSelect',\n\t\t\t'click .acf-gallery-close': 'onClickClose',\n\t\t\t'change .acf-gallery-sort': 'onChangeSort',\n\t\t\t'click .acf-gallery-update': 'onUpdate',\n\t\t\tmouseover: 'onHover',\n\t\t\tshowField: 'render',\n\t\t},\n\n\t\tactions: {\n\t\t\tvalidation_begin: 'onValidationBegin',\n\t\t\tvalidation_failure: 'onValidationFailure',\n\t\t\tresize: 'onResize',\n\t\t},\n\n\t\tonValidationBegin: function () {\n\t\t\tacf.disable( this.$sideData(), this.cid );\n\t\t},\n\n\t\tonValidationFailure: function () {\n\t\t\tacf.enable( this.$sideData(), this.cid );\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.acf-gallery' );\n\t\t},\n\n\t\t$collection: function () {\n\t\t\treturn this.$( '.acf-gallery-attachments' );\n\t\t},\n\n\t\t$attachments: function () {\n\t\t\treturn this.$( '.acf-gallery-attachment' );\n\t\t},\n\n\t\t$attachment: function ( id ) {\n\t\t\treturn this.$( '.acf-gallery-attachment[data-id=\"' + id + '\"]' );\n\t\t},\n\n\t\t$active: function () {\n\t\t\treturn this.$( '.acf-gallery-attachment.active' );\n\t\t},\n\n\t\t$main: function () {\n\t\t\treturn this.$( '.acf-gallery-main' );\n\t\t},\n\n\t\t$side: function () {\n\t\t\treturn this.$( '.acf-gallery-side' );\n\t\t},\n\n\t\t$sideData: function () {\n\t\t\treturn this.$( '.acf-gallery-side-data' );\n\t\t},\n\n\t\tisFull: function () {\n\t\t\tvar max = parseInt( this.get( 'max' ) );\n\t\t\tvar count = this.$attachments().length;\n\t\t\treturn max && count >= max;\n\t\t},\n\n\t\tgetValue: function () {\n\t\t\t// vars\n\t\t\tvar val = [];\n\n\t\t\t// loop\n\t\t\tthis.$attachments().each( function () {\n\t\t\t\tval.push( $( this ).data( 'id' ) );\n\t\t\t} );\n\n\t\t\t// return\n\t\t\treturn val.length ? val : false;\n\t\t},\n\n\t\taddUnscopedEvents: function ( self ) {\n\t\t\t// invalidField\n\t\t\tthis.on( 'change', '.acf-gallery-side', function ( e ) {\n\t\t\t\tself.onUpdate( e, $( this ) );\n\t\t\t} );\n\t\t},\n\n\t\taddSortable: function ( self ) {\n\t\t\t// add sortable\n\t\t\tthis.$collection().sortable( {\n\t\t\t\titems: '.acf-gallery-attachment',\n\t\t\t\tforceHelperSize: true,\n\t\t\t\tforcePlaceholderSize: true,\n\t\t\t\tscroll: true,\n\t\t\t\tstart: function ( event, ui ) {\n\t\t\t\t\tui.placeholder.html( ui.item.html() );\n\t\t\t\t\tui.placeholder.removeAttr( 'style' );\n\t\t\t\t},\n\t\t\t\tupdate: function ( event, ui ) {\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// resizable\n\t\t\tthis.$control().resizable( {\n\t\t\t\thandles: 's',\n\t\t\t\tminHeight: 200,\n\t\t\t\tstop: function ( event, ui ) {\n\t\t\t\t\tacf.update_user_setting( 'gallery_height', ui.size.height );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// add unscoped events\n\t\t\tthis.addUnscopedEvents( this );\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar $sort = this.$( '.acf-gallery-sort' );\n\t\t\tvar $add = this.$( '.acf-gallery-add' );\n\t\t\tvar count = this.$attachments().length;\n\n\t\t\t// disable add\n\t\t\tif ( this.isFull() ) {\n\t\t\t\t$add.addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$add.removeClass( 'disabled' );\n\t\t\t}\n\n\t\t\t// disable select\n\t\t\tif ( ! count ) {\n\t\t\t\t$sort.addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$sort.removeClass( 'disabled' );\n\t\t\t}\n\n\t\t\t// resize\n\t\t\tthis.resize();\n\t\t},\n\n\t\tresize: function () {\n\t\t\t// vars\n\t\t\tvar width = this.$control().width();\n\t\t\tvar target = 150;\n\t\t\tvar columns = Math.round( width / target );\n\n\t\t\t// max columns = 8\n\t\t\tcolumns = Math.min( columns, 8 );\n\n\t\t\t// update data\n\t\t\tthis.$control().attr( 'data-columns', columns );\n\t\t},\n\n\t\tonResize: function () {\n\t\t\tthis.resize();\n\t\t},\n\n\t\topenSidebar: function () {\n\t\t\t// add class\n\t\t\tthis.$control().addClass( '-open' );\n\n\t\t\t// hide bulk actions\n\t\t\t// should be done with CSS\n\t\t\t//this.$main().find('.acf-gallery-sort').hide();\n\n\t\t\t// vars\n\t\t\tvar width = this.$control().width() / 3;\n\t\t\twidth = parseInt( width );\n\t\t\twidth = Math.max( width, 350 );\n\n\t\t\t// animate\n\t\t\tthis.$( '.acf-gallery-side-inner' ).css( { width: width - 1 } );\n\t\t\tthis.$side().animate( { width: width - 1 }, 250 );\n\t\t\tthis.$main().animate( { right: width }, 250 );\n\t\t},\n\n\t\tcloseSidebar: function () {\n\t\t\t// remove class\n\t\t\tthis.$control().removeClass( '-open' );\n\n\t\t\t// clear selection\n\t\t\tthis.$active().removeClass( 'active' );\n\n\t\t\t// disable sidebar\n\t\t\tacf.disable( this.$side() );\n\n\t\t\t// animate\n\t\t\tvar $sideData = this.$( '.acf-gallery-side-data' );\n\t\t\tthis.$main().animate( { right: 0 }, 250 );\n\t\t\tthis.$side().animate( { width: 0 }, 250, function () {\n\t\t\t\t$sideData.html( '' );\n\t\t\t} );\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// validate\n\t\t\tif ( this.isFull() ) {\n\t\t\t\tthis.showNotice( {\n\t\t\t\t\ttext: acf.__( 'Maximum selection reached' ),\n\t\t\t\t\ttype: 'warning',\n\t\t\t\t} );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// new frame\n\t\t\tvar frame = acf.newMediaPopup( {\n\t\t\t\tmode: 'select',\n\t\t\t\ttitle: acf.__( 'Add Image to Gallery' ),\n\t\t\t\tfield: this.get( 'key' ),\n\t\t\t\tmultiple: 'add',\n\t\t\t\tlibrary: this.get( 'library' ),\n\t\t\t\tallowedTypes: this.get( 'mime_types' ),\n\t\t\t\tselected: this.val(),\n\t\t\t\tselect: $.proxy( function ( attachment, i ) {\n\t\t\t\t\tthis.appendAttachment( attachment, i );\n\t\t\t\t}, this ),\n\t\t\t} );\n\t\t},\n\n\t\tappendAttachment: function ( attachment, i ) {\n\t\t\t// vars\n\t\t\tattachment = this.validateAttachment( attachment );\n\n\t\t\t// bail early if is full\n\t\t\tif ( this.isFull() ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// bail early if already exists\n\t\t\tif ( this.$attachment( attachment.id ).length ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// html\n\t\t\tvar html = [\n\t\t\t\t'<div class=\"acf-gallery-attachment\" data-id=\"' +\n\t\t\t\t\tattachment.id +\n\t\t\t\t\t'\">',\n\t\t\t\t'<input type=\"hidden\" value=\"' +\n\t\t\t\t\tattachment.id +\n\t\t\t\t\t'\" name=\"' +\n\t\t\t\t\tthis.getInputName() +\n\t\t\t\t\t'[]\">',\n\t\t\t\t'<div class=\"margin\" title=\"\">',\n\t\t\t\t'<div class=\"thumbnail\">',\n\t\t\t\t'<img src=\"\" alt=\"\">',\n\t\t\t\t'</div>',\n\t\t\t\t'<div class=\"filename\"></div>',\n\t\t\t\t'</div>',\n\t\t\t\t'<div class=\"actions\">',\n\t\t\t\t'<a href=\"#\" class=\"acf-icon -cancel dark acf-gallery-remove\" data-id=\"' +\n\t\t\t\t\tattachment.id +\n\t\t\t\t\t'\"></a>',\n\t\t\t\t'</div>',\n\t\t\t\t'</div>',\n\t\t\t].join( '' );\n\t\t\tvar $html = $( html );\n\n\t\t\t// append\n\t\t\tthis.$collection().append( $html );\n\n\t\t\t// move to beginning\n\t\t\tif ( this.get( 'insert' ) === 'prepend' ) {\n\t\t\t\tvar $before = this.$attachments().eq( i );\n\t\t\t\tif ( $before.length ) {\n\t\t\t\t\t$before.before( $html );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// render attachment\n\t\t\tthis.renderAttachment( attachment );\n\n\t\t\t// render\n\t\t\tthis.render();\n\n\t\t\t// trigger change\n\t\t\tthis.$input().trigger( 'change' );\n\t\t},\n\n\t\tvalidateAttachment: function ( attachment ) {\n\t\t\t// defaults\n\t\t\tattachment = acf.parseArgs( attachment, {\n\t\t\t\tid: '',\n\t\t\t\turl: '',\n\t\t\t\talt: '',\n\t\t\t\ttitle: '',\n\t\t\t\tfilename: '',\n\t\t\t\ttype: 'image',\n\t\t\t} );\n\n\t\t\t// WP attachment\n\t\t\tif ( attachment.attributes ) {\n\t\t\t\tattachment = attachment.attributes;\n\n\t\t\t\t// preview size\n\t\t\t\tvar url = acf.isget(\n\t\t\t\t\tattachment,\n\t\t\t\t\t'sizes',\n\t\t\t\t\tthis.get( 'preview_size' ),\n\t\t\t\t\t'url'\n\t\t\t\t);\n\t\t\t\tif ( url !== null ) {\n\t\t\t\t\tattachment.url = url;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn attachment;\n\t\t},\n\n\t\trenderAttachment: function ( attachment ) {\n\t\t\t// vars\n\t\t\tattachment = this.validateAttachment( attachment );\n\n\t\t\t// vars\n\t\t\tvar $el = this.$attachment( attachment.id );\n\n\t\t\t// Image type.\n\t\t\tif ( attachment.type == 'image' ) {\n\t\t\t\t// Remove filename.\n\t\t\t\t$el.find( '.filename' ).remove();\n\n\t\t\t\t// Other file type.\n\t\t\t} else {\n\t\t\t\t// Check for attachment featured image.\n\t\t\t\tvar image = acf.isget( attachment, 'image', 'src' );\n\t\t\t\tif ( image !== null ) {\n\t\t\t\t\tattachment.url = image;\n\t\t\t\t}\n\n\t\t\t\t// Update filename text.\n\t\t\t\t$el.find( '.filename' ).text( attachment.filename );\n\t\t\t}\n\n\t\t\t// Default to mimetype icon.\n\t\t\tif ( ! attachment.url ) {\n\t\t\t\tattachment.url = acf.get( 'mimeTypeIcon' );\n\t\t\t\t$el.addClass( '-icon' );\n\t\t\t}\n\n\t\t\t// update els\n\t\t\t$el.find( 'img' ).attr( {\n\t\t\t\tsrc: attachment.url,\n\t\t\t\talt: attachment.alt,\n\t\t\t\ttitle: attachment.title,\n\t\t\t} );\n\n\t\t\t// update val\n\t\t\tacf.val( $el.find( 'input' ), attachment.id );\n\t\t},\n\n\t\teditAttachment: function ( id ) {\n\t\t\t// new frame\n\t\t\tvar frame = acf.newMediaPopup( {\n\t\t\t\tmode: 'edit',\n\t\t\t\ttitle: acf.__( 'Edit Image' ),\n\t\t\t\tbutton: acf.__( 'Update Image' ),\n\t\t\t\tattachment: id,\n\t\t\t\tfield: this.get( 'key' ),\n\t\t\t\tselect: $.proxy( function ( attachment, i ) {\n\t\t\t\t\tthis.renderAttachment( attachment );\n\t\t\t\t\t// todo - render sidebar\n\t\t\t\t}, this ),\n\t\t\t} );\n\t\t},\n\n\t\tonClickEdit: function ( e, $el ) {\n\t\t\tvar id = $el.data( 'id' );\n\t\t\tif ( id ) {\n\t\t\t\tthis.editAttachment( id );\n\t\t\t}\n\t\t},\n\n\t\tremoveAttachment: function ( id ) {\n\t\t\t// close sidebar (if open)\n\t\t\tthis.closeSidebar();\n\n\t\t\t// remove attachment\n\t\t\tthis.$attachment( id ).remove();\n\n\t\t\t// render\n\t\t\tthis.render();\n\n\t\t\t// trigger change\n\t\t\tthis.$input().trigger( 'change' );\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\t// prevent event from triggering click on attachment\n\t\t\te.preventDefault();\n\t\t\te.stopPropagation();\n\n\t\t\t//remove\n\t\t\tvar id = $el.data( 'id' );\n\t\t\tif ( id ) {\n\t\t\t\tthis.removeAttachment( id );\n\t\t\t}\n\t\t},\n\n\t\tselectAttachment: function ( id ) {\n\t\t\t// vars\n\t\t\tvar $el = this.$attachment( id );\n\n\t\t\t// bail early if already active\n\t\t\tif ( $el.hasClass( 'active' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// step 1\n\t\t\tvar step1 = this.proxy( function () {\n\t\t\t\t// save any changes in sidebar\n\t\t\t\tthis.$side().find( ':focus' ).trigger( 'blur' );\n\n\t\t\t\t// clear selection\n\t\t\t\tthis.$active().removeClass( 'active' );\n\n\t\t\t\t// add selection\n\t\t\t\t$el.addClass( 'active' );\n\n\t\t\t\t// open sidebar\n\t\t\t\tthis.openSidebar();\n\n\t\t\t\t// call step 2\n\t\t\t\tstep2();\n\t\t\t} );\n\n\t\t\t// step 2\n\t\t\tvar step2 = this.proxy( function () {\n\t\t\t\tconst ajaxData = {\n\t\t\t\t\taction: 'acf/fields/gallery/get_attachment',\n\t\t\t\t\tnonce: this.get( 'nonce' ),\n\t\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\t\tid: id,\n\t\t\t\t};\n\n\t\t\t\t// abort prev ajax call\n\t\t\t\tif ( this.has( 'xhr' ) ) {\n\t\t\t\t\tthis.get( 'xhr' ).abort();\n\t\t\t\t}\n\n\t\t\t\t// loading\n\t\t\t\tacf.showLoading( this.$sideData() );\n\n\t\t\t\t// get HTML\n\t\t\t\tvar xhr = $.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tcache: false,\n\t\t\t\t\tsuccess: step3,\n\t\t\t\t} );\n\n\t\t\t\t// update\n\t\t\t\tthis.set( 'xhr', xhr );\n\t\t\t} );\n\n\t\t\t// step 3\n\t\t\tvar step3 = this.proxy( function ( html ) {\n\t\t\t\t// bail early if no html\n\t\t\t\tif ( ! html ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// vars\n\t\t\t\tvar $side = this.$sideData();\n\n\t\t\t\t// render\n\t\t\t\t$side.html( html );\n\n\t\t\t\t// remove acf form data\n\t\t\t\t$side.find( '.compat-field-acf-form-data' ).remove();\n\n\t\t\t\t// merge tables\n\t\t\t\t$side\n\t\t\t\t\t.find( '> table.form-table > tbody' )\n\t\t\t\t\t.append(\n\t\t\t\t\t\t$side.find( '> .compat-attachment-fields > tbody > tr' )\n\t\t\t\t\t);\n\n\t\t\t\t// setup fields\n\t\t\t\tacf.doAction( 'append', $side );\n\t\t\t} );\n\n\t\t\t// run step 1\n\t\t\tstep1();\n\t\t},\n\n\t\tonClickSelect: function ( e, $el ) {\n\t\t\tvar id = $el.data( 'id' );\n\t\t\tif ( id ) {\n\t\t\t\tthis.selectAttachment( id );\n\t\t\t}\n\t\t},\n\n\t\tonClickClose: function ( e, $el ) {\n\t\t\tthis.closeSidebar();\n\t\t},\n\n\t\tonChangeSort: function ( e, $el ) {\n\t\t\t// Bail early if is disabled.\n\t\t\tif ( $el.hasClass( 'disabled' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Get sort val.\n\t\t\tvar val = $el.val();\n\t\t\tif ( ! val ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// find ids\n\t\t\tvar ids = [];\n\t\t\tthis.$attachments().each( function () {\n\t\t\t\tids.push( $( this ).data( 'id' ) );\n\t\t\t} );\n\n\t\t\t// step 1\n\t\t\tvar step1 = this.proxy( function () {\n\t\t\t\tconst ajaxData = {\n\t\t\t\t\taction: 'acf/fields/gallery/get_sort_order',\n\t\t\t\t\tnonce: this.get( 'nonce' ),\n\t\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\t\tids: ids,\n\t\t\t\t\tsort: val,\n\t\t\t\t};\n\n\t\t\t\t// get results\n\t\t\t\tvar xhr = $.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdataType: 'json',\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tcache: false,\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\tsuccess: step2,\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\t// step 2\n\t\t\tvar step2 = this.proxy( function ( json ) {\n\t\t\t\t// validate\n\t\t\t\tif ( ! acf.isAjaxSuccess( json ) ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// reverse order\n\t\t\t\tjson.data.reverse();\n\n\t\t\t\t// loop\n\t\t\t\tjson.data.map( function ( id ) {\n\t\t\t\t\tthis.$collection().prepend( this.$attachment( id ) );\n\t\t\t\t}, this );\n\t\t\t} );\n\n\t\t\t// call step 1\n\t\t\tstep1();\n\t\t},\n\n\t\tonUpdate: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $submit = this.$( '.acf-gallery-update' );\n\n\t\t\t// validate\n\t\t\tif ( $submit.hasClass( 'disabled' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// serialize data\n\t\t\tconst ajaxData = acf.serialize( this.$sideData() );\n\n\t\t\t// loading\n\t\t\t$submit.addClass( 'disabled' );\n\t\t\t$submit.before( '<i class=\"acf-loading\"></i> ' );\n\n\t\t\t// Append AJAX action and nonce.\n\t\t\tajaxData.action = 'acf/fields/gallery/update_attachment';\n\t\t\tajaxData.nonce = this.get( 'nonce' );\n\t\t\tajaxData.field_key = this.get( 'key' );\n\n\t\t\t// ajax\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'json',\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t$submit.removeClass( 'disabled' );\n\t\t\t\t\t$submit.prev( '.acf-loading' ).remove();\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonHover: function () {\n\t\t\t// add sortable\n\t\t\tthis.addSortable( this );\n\n\t\t\t// remove event\n\t\t\tthis.off( 'mouseover' );\n\t\t},\n\t} );\n\n\tacf.registerFieldType( Field );\n\n\t// register existing conditions\n\tacf.registerConditionForFieldType( 'hasValue', 'gallery' );\n\tacf.registerConditionForFieldType( 'hasNoValue', 'gallery' );\n\tacf.registerConditionForFieldType( 'selectionLessThan', 'gallery' );\n\tacf.registerConditionForFieldType( 'selectionGreaterThan', 'gallery' );\n} )( jQuery );\n", "( function ( $ ) {\n\tvar Field = acf.Field.extend( {\n\t\ttype: 'repeater',\n\t\twait: '',\n\t\tpage: 1,\n\t\tnextRowNum: 0,\n\n\t\tevents: {\n\t\t\t'click a[data-event=\"add-row\"]': 'onClickAdd',\n\t\t\t'click a[data-event=\"duplicate-row\"]': 'onClickDuplicate',\n\t\t\t'click a[data-event=\"remove-row\"]': 'onClickRemove',\n\t\t\t'click a[data-event=\"collapse-row\"]': 'onClickCollapse',\n\t\t\t'click a[data-event=\"first-page\"]:not(.disabled)' : 'onClickFirstPage',\n\t\t\t'click a[data-event=\"last-page\"]:not(.disabled)' : 'onClickLastPage',\n\t\t\t'click a[data-event=\"prev-page\"]:not(.disabled)': 'onClickPrevPage',\n\t\t\t'click a[data-event=\"next-page\"]:not(.disabled)': 'onClickNextPage',\n\t\t\t'change .current-page': 'onChangeCurrentPage',\n\t\t\t'click .acf-order-input-wrap': 'onClickRowOrder',\n\t\t\t'blur .acf-order-input': 'onBlurRowOrder',\n\t\t\t'change .acf-order-input': 'onChangeRowOrder',\n\t\t\t'changed:total_rows': 'onChangeTotalRows',\n\t\t\tshowField: 'onShow',\n\t\t\tunloadField: 'onUnload',\n\t\t\tmouseover: 'onHover',\n\t\t\tchange: 'onChangeField',\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.acf-repeater:first' );\n\t\t},\n\n\t\t$table: function () {\n\t\t\treturn this.$( 'table:first' );\n\t\t},\n\n\t\t$tbody: function () {\n\t\t\treturn this.$( 'tbody:first' );\n\t\t},\n\n\t\t$rows: function () {\n\t\t\treturn this.$( 'tbody:first > tr' ).not( '.acf-clone, .acf-deleted' );\n\t\t},\n\n\t\t$row: function ( index ) {\n\t\t\treturn this.$( 'tbody:first > tr:eq(' + index + ')' );\n\t\t},\n\n\t\t$clone: function () {\n\t\t\treturn this.$( 'tbody:first > tr.acf-clone' );\n\t\t},\n\n\t\t$actions: function () {\n\t\t\treturn this.$( '.acf-actions:last' );\n\t\t},\n\n\t\t$button: function () {\n\t\t\treturn this.$( '.acf-actions:last .button' );\n\t\t},\n\n\t\t$firstPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .first-page' );\n\t\t},\n\n\t\t$prevPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .prev-page' );\n\t\t},\n\n\t\t$nextPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .next-page' );\n\t\t},\n\n\t\t$lastPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .last-page' );\n\t\t},\n\n\t\t$pageInput: function() {\n\t\t\treturn this.$( '.current-page:last' );\n\t\t},\n\n\t\ttotalPages: function() {\n\t\t\tconst totalPages = this.$( '.acf-total-pages:last' ).text();\n\t\t\treturn parseInt( totalPages );\n\t\t},\n\n\t\tgetValue: function () {\n\t\t\treturn this.$rows().length;\n\t\t},\n\n\t\tallowRemove: function () {\n\t\t\tlet numRows = this.val();\n\t\t\tlet minRows = parseInt( this.get( 'min' ) );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tnumRows = this.get( 'total_rows' );\n\t\t\t}\n\n\t\t\treturn ! minRows || minRows < numRows;\n\t\t},\n\n\t\tallowAdd: function () {\n\t\t\tlet numRows = this.val();\n\t\t\tlet maxRows = parseInt( this.get( 'max' ) );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tnumRows = this.get( 'total_rows' );\n\t\t\t}\n\n\t\t\treturn ! maxRows || maxRows > numRows;\n\t\t},\n\n\t\taddSortable: function ( self ) {\n\t\t\t// bail early if max 1 row\n\t\t\tif ( this.get( 'max' ) == 1 ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Bail early if using pagination.\n\t\t\tif ( this.get( 'pagination') ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// add sortable\n\t\t\tthis.$tbody().sortable( {\n\t\t\t\titems: '> tr',\n\t\t\t\thandle: '> td.order',\n\t\t\t\tforceHelperSize: true,\n\t\t\t\tforcePlaceholderSize: true,\n\t\t\t\tscroll: true,\n\t\t\t\tstop: function ( event, ui ) {\n\t\t\t\t\tself.render();\n\t\t\t\t},\n\t\t\t\tupdate: function ( event, ui ) {\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\taddCollapsed: function () {\n\t\t\t// vars\n\t\t\tvar indexes = preference.load( this.get( 'key' ) );\n\n\t\t\t// bail early if no collapsed\n\t\t\tif ( ! indexes ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// loop\n\t\t\tthis.$rows().each( function ( i ) {\n\t\t\t\tif ( indexes.indexOf( i ) > -1 ) {\n\t\t\t\t\tif ( $( this ).find( '.-collapsed-target' ).length ) {\n\t\t\t\t\t\t$( this ).addClass( '-collapsed' );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t\taddUnscopedEvents: function ( self ) {\n\t\t\t// invalidField\n\t\t\tthis.on( 'invalidField', '.acf-row', function ( e ) {\n\t\t\t\tvar $row = $( this );\n\t\t\t\tif ( self.isCollapsed( $row ) ) {\n\t\t\t\t\tself.expand( $row );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// Listen for changes to fields, so we can persist them in the DOM.\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.on( 'change', 'input, select, textarea', function ( e ) {\n\t\t\t\t\tconst $changed = $( e.currentTarget );\n\t\t\t\t\tif ( ! $changed.hasClass( 'acf-order-input' ) && ! $changed.hasClass( 'acf-row-status' ) ) {\n\t\t\t\t\t\tself.onChangeField( e, $( this ) );\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\tthis.listenForSavedMetaBoxes();\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// add unscoped events\n\t\t\tthis.addUnscopedEvents( this );\n\n\t\t\t// add collapsed\n\t\t\tthis.addCollapsed();\n\n\t\t\t// disable clone\n\t\t\tacf.disable( this.$clone(), this.cid );\n\n\t\t\t// Set up the next row number.\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.nextRowNum = this.get( 'total_rows' );\n\t\t\t}\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function ( update_order_numbers = true ) {\n\t\t\t// Update order number.\n\t\t\tif ( update_order_numbers ) {\n\t\t\t\tthis.$rows().each( function ( i ) {\n\t\t\t\t\t$( this )\n\t\t\t\t\t\t.find( '> .order > span' )\n\t\t\t\t\t\t.html( i + 1 );\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// Extract vars.\n\t\t\tvar $control = this.$control();\n\t\t\tvar $button = this.$button();\n\n\t\t\t// empty\n\t\t\tif ( this.val() == 0 ) {\n\t\t\t\t$control.addClass( '-empty' );\n\t\t\t} else {\n\t\t\t\t$control.removeClass( '-empty' );\n\t\t\t}\n\n\t\t\t// Reached max rows.\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\t$control.addClass( '-max' );\n\t\t\t\t$button.addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$control.removeClass( '-max' );\n\t\t\t\t$button.removeClass( 'disabled' );\n\t\t\t}\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.maybeDisablePagination();\n\t\t\t}\n\n\t\t\t// Reached min rows (not used).\n\t\t\t//if( !this.allowRemove() ) {\n\t\t\t//\t$control.addClass('-min');\n\t\t\t//} else {\n\t\t\t//\t$control.removeClass('-min');\n\t\t\t//}\n\t\t},\n\n\t\tlistenForSavedMetaBoxes: function() {\n\t\t\tif ( ! acf.isGutenbergPostEditor() || ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet checkedMetaBoxes = true;\n\t\t\twp.data.subscribe( () => {\n\t\t\t\tif ( wp.data.select( 'core/edit-post' ).isSavingMetaBoxes() ) {\n\t\t\t\t\tcheckedMetaBoxes = false;\n\t\t\t\t} else {\n\t\t\t\t\tif ( ! checkedMetaBoxes ) {\n\t\t\t\t\t\tcheckedMetaBoxes = true;\n\t\t\t\t\t\tthis.set( 'total_rows', 0, true );\n\t\t\t\t\t\tthis.ajaxLoadPage( true );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t\tincrementTotalRows: function() {\n\t\t\tlet totalRows = this.get( 'total_rows' );\n\t\t\tthis.set( 'total_rows', ++totalRows, true );\n\t\t},\n\n\t\tdecrementTotalRows: function() {\n\t\t\tlet totalRows = this.get( 'total_rows' );\n\t\t\tthis.set( 'total_rows', --totalRows, true );\n\t\t},\n\n\t\tvalidateAdd: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowAdd() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar max = this.get( 'max' );\n\t\t\tvar text = acf.__( 'Maximum rows reached ({max} rows)' );\n\n\t\t\t// replace\n\t\t\ttext = text.replace( '{max}', max );\n\n\t\t\t// add notice\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\t// return\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// validate\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// add above row\n\t\t\tif ( $el.hasClass( 'acf-icon' ) ) {\n\t\t\t\tthis.add( {\n\t\t\t\t\tbefore: $el.closest( '.acf-row' ),\n\t\t\t\t} );\n\n\t\t\t\t// default\n\t\t\t} else {\n\t\t\t\tthis.add();\n\t\t\t}\n\t\t},\n\n\t\tadd: function ( args ) {\n\t\t\t// validate\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// defaults\n\t\t\targs = acf.parseArgs( args, {\n\t\t\t\tbefore: false,\n\t\t\t} );\n\n\t\t\t// add row\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: this.$clone(),\n\t\t\t\tappend: this.proxy( function ( $el, $el2 ) {\n\t\t\t\t\t// append\n\t\t\t\t\tif ( args.before ) {\n\t\t\t\t\t\targs.before.before( $el2 );\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$el.before( $el2 );\n\t\t\t\t\t}\n\n\t\t\t\t\t// remove clone class\n\t\t\t\t\t$el2.removeClass( 'acf-clone' );\n\n\t\t\t\t\t// enable\n\t\t\t\t\tacf.enable( $el2, this.cid );\n\t\t\t\t} ),\n\t\t\t} );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.incrementTotalRows();\n\n\t\t\t\tif ( false !== args.before ) {\n\t\t\t\t\t// If the row was inserted above an existing row, try to keep that order.\n\t\t\t\t\tconst prevRowNum = parseInt( args.before.find( '.acf-row-number' ).first().text() ) || 0;\n\t\t\t\t\tlet newRowNum = prevRowNum;\n\n\t\t\t\t\tif ( newRowNum && ! args.before.hasClass( 'acf-inserted' ) && ! args.before.hasClass( 'acf-added' ) ) {\n\t\t\t\t\t\t--newRowNum;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( args.before.hasClass( 'acf-divider' ) ) {\n\t\t\t\t\t\targs.before.removeClass( 'acf-divider' );\n\t\t\t\t\t\t$el.addClass( 'acf-divider' );\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.updateRowStatus( $el, 'inserted' );\n\t\t\t\t\tthis.updateRowStatus( $el, 'reordered', newRowNum );\n\n\t\t\t\t\t// Hide the row numbers to avoid confusion with existing rows.\n\t\t\t\t\t$el.find( '.acf-row-number' ).first().hide().text( newRowNum );\n\t\t\t\t\tif ( ! $el.find( '.acf-order-input-wrap' ).hasClass( 'disabled' ) ) {\n\t\t\t\t\t\tlet message =  acf.__( 'Order will be assigned upon save' );\n\t\t\t\t\t\t$el.find( '.acf-order-input-wrap' ).addClass( 'disabled' );\n\t\t\t\t\t\t$el.find( '.acf-row-number' ).first().after( '<span title=\"' + message + '\">-</span>' );\n\t\t\t\t\t}\n\t\t\t\t\t$el.find( '.acf-order-input' ).first().hide();\n\t\t\t\t\t$el.attr( 'data-inserted', newRowNum );\n\t\t\t\t} else {\n\t\t\t\t\tthis.nextRowNum++;\n\n\t\t\t\t\t$el.find( '.acf-order-input' ).first().val( this.nextRowNum );\n\t\t\t\t\t$el.find( '.acf-row-number' ).first().text( this.nextRowNum );\n\t\t\t\t\tthis.updateRowStatus( $el, 'added' );\n\n\t\t\t\t\tif ( ! this.$tbody().find( '.acf-divider' ).length ) {\n\t\t\t\t\t\t$el.addClass( 'acf-divider' );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t$el.find( '.acf-input:first' )\n\t\t\t\t\t.find( 'input:not([type=hidden]), select, textarea' )\n\t\t\t\t\t.first()\n\t\t\t\t\t.trigger( 'focus' );\n\t\t\t}\n\n\t\t\t// Render and trigger change for validation errors.\n\t\t\tthis.render();\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\treturn $el;\n\t\t},\n\n\t\tonClickDuplicate: function ( e, $el ) {\n\t\t\t// Validate with warning.\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// get layout and duplicate it.\n\t\t\tvar $row = $el.closest( '.acf-row' );\n\t\t\tthis.duplicateRow( $row );\n\t\t},\n\n\t\tduplicateRow: function ( $row ) {\n\t\t\t// Validate without warning.\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// Vars.\n\t\t\tvar fieldKey = this.get( 'key' );\n\n\t\t\t// Duplicate row.\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: $row,\n\n\t\t\t\t// Provide a custom renaming callback to avoid renaming parent row attributes.\n\t\t\t\trename: function ( name, value, search, replace ) {\n\t\t\t\t\t// Rename id attributes from \"field_1-search\" to \"field_1-replace\".\n\t\t\t\t\tif ( name === 'id' || name === 'for' ) {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '-' + search,\n\t\t\t\t\t\t\tfieldKey + '-' + replace\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// Rename name and for attributes from \"[field_1][search]\" to \"[field_1][replace]\".\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '][' + search,\n\t\t\t\t\t\t\tfieldKey + '][' + replace\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tbefore: function ( $el ) {\n\t\t\t\t\tacf.doAction( 'unmount', $el );\n\t\t\t\t},\n\t\t\t\tafter: function ( $el, $el2 ) {\n\t\t\t\t\tacf.doAction( 'remount', $el );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.incrementTotalRows();\n\n\t\t\t\t// If the row was inserted above an existing row, try to keep that order.\n\t\t\t\tconst prevRowNum = parseInt( $row.find( '.acf-row-number' ).first().text() ) || 0;\n\n\t\t\t\tthis.updateRowStatus( $el, 'inserted' );\n\t\t\t\tthis.updateRowStatus( $el, 'reordered', prevRowNum );\n\n\t\t\t\t// Hide the row numbers to avoid confusion with existing rows.\n\t\t\t\t$el.find( '.acf-row-number' ).first().hide();\n\t\t\t\tif ( ! $el.find( '.acf-order-input-wrap' ).hasClass( 'disabled' ) ) {\n\t\t\t\t\tlet message =  acf.__( 'Order will be assigned upon save' );\n\t\t\t\t\t$el.find( '.acf-order-input-wrap' ).addClass( 'disabled' );\n\t\t\t\t\t$el.find( '.acf-row-number' ).first().after( '<span title=\"' + message + '\">-</span>' );\n\t\t\t\t}\n\t\t\t\t$el.find( '.acf-order-input' ).first().hide();\n\t\t\t\t$el.attr( 'data-inserted', prevRowNum );\n\t\t\t\t$el.removeClass( 'acf-divider' );\n\t\t\t}\n\n\t\t\t// trigger change for validation errors\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\t// Update order numbers.\n\t\t\tthis.render();\n\n\t\t\t// Focus on new row.\n\t\t\tacf.focusAttention( $el );\n\n\t\t\t// Return new layout.\n\t\t\treturn $el;\n\t\t},\n\n\t\tvalidateRemove: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowRemove() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar min = this.get( 'min' );\n\t\t\tvar text = acf.__( 'Minimum rows not reached ({min} rows)' );\n\n\t\t\t// replace\n\t\t\ttext = text.replace( '{min}', min );\n\n\t\t\t// add notice\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\t// return\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\tvar $row = $el.closest( '.acf-row' );\n\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.remove( $row );\n\t\t\t}\n\n\t\t\t// add class\n\t\t\t$row.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.remove( $row );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\t$row.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonClickRowOrder: function( e, $el ) {\n\t\t\tif ( ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( $el.hasClass( 'disabled' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t$el.find( '.acf-row-number' ).hide();\n\t\t\t$el.find( '.acf-order-input' ).show().trigger( 'select' );\n\t\t},\n\n\t\tonBlurRowOrder: function( e, $el ) {\n\t\t\tthis.onChangeRowOrder( e, $el, false );\n\t\t},\n\n\t\tonChangeRowOrder: function( e, $el, update = true ) {\n\t\t\tif ( ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst $row = $el.closest( '.acf-row' );\n\t\t\tconst $orderSpan = $row.find( '.acf-row-number' ).first();\n\t\t\tlet hrOrder = $el.val();\n\n\t\t\t$row.find( '.acf-order-input' ).first().hide();\n\n\t\t\tif ( ! acf.isNumeric( hrOrder ) || parseFloat( hrOrder ) < 0 ) {\n\t\t\t\t$orderSpan.show();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\thrOrder = Math.round( hrOrder );\n\n\t\t\tconst newOrder = hrOrder - 1;\n\n\t\t\t$el.val( hrOrder );\n\t\t\t$orderSpan.text( hrOrder ).show();\n\n\t\t\tif ( update ) {\n\t\t\t\tthis.updateRowStatus( $row, 'reordered', newOrder );\n\t\t\t}\n\t\t},\n\n\t\tonChangeTotalRows: function() {\n\t\t\tconst perPage = parseInt( this.get( 'per_page' ) ) || 20;\n\t\t\tconst totalRows = parseInt( this.get( 'total_rows' ) ) || 0;\n\t\t\tconst totalPages = Math.ceil( totalRows / perPage );\n\n\t\t\t// Update the total pages in pagination.\n\t\t\tthis.$( '.acf-total-pages:last' ).text( totalPages );\n\t\t\tthis.nextRowNum = totalRows;\n\n\t\t\t// If the current page no longer exists, load the last page.\n\t\t\tif ( this.page > totalPages ) {\n\t\t\t\tthis.page = totalPages;\n\t\t\t\tthis.ajaxLoadPage();\n\t\t\t}\n\t\t},\n\n\t\tremove: function ( $row ) {\n\t\t\tconst self = this;\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.decrementTotalRows();\n\n\t\t\t\t// If using pagination and the row had already been saved, just hide the row instead of deleting it.\n\t\t\t\tif ( $row.data( 'id' ).includes( 'row-' ) ){\n\t\t\t\t\tthis.updateRowStatus( $row, 'deleted' );\n\n\t\t\t\t\t$row.hide();\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t\tself.render( false );\n\t\t\t\t\treturn;\n\t\t\t\t} else if ( $row.hasClass( 'acf-divider' ) ) {\n\t\t\t\t\t$row.next( '.acf-added' ).addClass( 'acf-divider' );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If not using pagination, delete the actual row.\n\t\t\tacf.remove( {\n\t\t\t\ttarget: $row,\n\t\t\t\tendHeight: 0,\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t// trigger change to allow attachment save\n\t\t\t\t\tself.$input().trigger( 'change' );\n\n\t\t\t\t\t// render\n\t\t\t\t\tself.render();\n\n\t\t\t\t\t// sync collapsed order\n\t\t\t\t\t//self.sync();\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tisCollapsed: function ( $row ) {\n\t\t\treturn $row.hasClass( '-collapsed' );\n\t\t},\n\n\t\tcollapse: function ( $row ) {\n\t\t\t$row.addClass( '-collapsed' );\n\t\t\tacf.doAction( 'hide', $row, 'collapse' );\n\t\t},\n\n\t\texpand: function ( $row ) {\n\t\t\t$row.removeClass( '-collapsed' );\n\t\t\tacf.doAction( 'show', $row, 'collapse' );\n\t\t},\n\n\t\tonClickCollapse: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $row = $el.closest( '.acf-row' );\n\t\t\tvar isCollpased = this.isCollapsed( $row );\n\n\t\t\t// shift\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\t$row = this.$rows();\n\t\t\t}\n\n\t\t\t// toggle\n\t\t\tif ( isCollpased ) {\n\t\t\t\tthis.expand( $row );\n\t\t\t} else {\n\t\t\t\tthis.collapse( $row );\n\t\t\t}\n\t\t},\n\n\t\tonShow: function ( e, $el, context ) {\n\t\t\t// get sub fields\n\t\t\tvar fields = acf.getFields( {\n\t\t\t\tis: ':visible',\n\t\t\t\tparent: this.$el,\n\t\t\t} );\n\n\t\t\t// trigger action\n\t\t\t// - ignore context, no need to pass through 'conditional_logic'\n\t\t\t// - this is just for fields like google_map to render itself\n\t\t\tacf.doAction( 'show_fields', fields );\n\t\t},\n\n\t\tonUnload: function () {\n\t\t\t// vars\n\t\t\tvar indexes = [];\n\n\t\t\t// loop\n\t\t\tthis.$rows().each( function ( i ) {\n\t\t\t\tif ( $( this ).hasClass( '-collapsed' ) ) {\n\t\t\t\t\tindexes.push( i );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// allow null\n\t\t\tindexes = indexes.length ? indexes : null;\n\n\t\t\t// set\n\t\t\tpreference.save( this.get( 'key' ), indexes );\n\t\t},\n\n\t\tonHover: function () {\n\t\t\t// add sortable\n\t\t\tthis.addSortable( this );\n\n\t\t\t// remove event\n\t\t\tthis.off( 'mouseover' );\n\t\t},\n\n\t\tonChangeField: function( e, $el ) {\n\t\t\tconst $target = $( e.delegateTarget );\n\t\t\tlet $row = $el.closest( '.acf-row' );\n\n\t\t\tif ( $row.closest( '.acf-field-repeater' ).data( 'key' ) !== $target.data( 'key' ) ) {\n\t\t\t\t$row = $row.parent().closest( '.acf-row' );\n\t\t\t}\n\n\t\t\tthis.updateRowStatus( $row, 'changed' );\n\t\t},\n\n\t\tupdateRowStatus: function( $row, status, data = true ) {\n\t\t\tif ( ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst parent_key = $row.parents( '.acf-field-repeater' ).data( 'key' );\n\n\t\t\tif ( this.parent() && parent_key !== this.get( 'key' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst row_id = $row.data( 'id' );\n\t\t\tconst input_name = this.$el.find( '.acf-repeater-hidden-input:first' ).attr( 'name' );\n\t\t\tconst status_name = `${input_name}[${row_id}][acf_${status}]`;\n\t\t\tconst status_input = `<input type=\"hidden\" class=\"acf-row-status\" name=\"${status_name}\" value=\"${data}\" />`;\n\n\t\t\tif ( ! $row.hasClass( 'acf-' + status ) ) {\n\t\t\t\t$row.addClass( 'acf-' + status );\n\t\t\t}\n\n\t\t\t// TODO: Update so that this doesn't get messed up with repeater subfields.\n\t\t\tconst $existing_status = $row.find( `input[name='${status_name}']` );\n\t\t\tif ( ! $existing_status.length ) {\n\t\t\t\t$row.find( 'td' ).first().append( status_input );\n\t\t\t} else {\n\t\t\t\t$existing_status.val( data );\n\t\t\t}\n\t\t},\n\n\t\tonClickFirstPage: function() {\n\t\t\tthis.validatePage( 1 );\n\t\t},\n\n\t\tonClickPrevPage: function() {\n\t\t\tthis.validatePage( this.page - 1 );\n\t\t},\n\n\t\tonClickNextPage: function( e ) {\n\t\t\tthis.validatePage( this.page + 1 );\n\t\t},\n\n\t\tonClickLastPage: function() {\n\t\t\tthis.validatePage( this.totalPages() );\n\t\t},\n\n\t\tonChangeCurrentPage: function() {\n\t\t\tthis.validatePage( this.$pageInput().val() );\n\t\t},\n\n\t\tmaybeDisablePagination: function() {\n\t\t\tthis.$actions().find( '.acf-nav' ).removeClass( 'disabled' );\n\n\t\t\tif ( this.page <= 1 ) {\n\t\t\t\tthis.$firstPageButton().addClass( 'disabled' );\n\t\t\t\tthis.$prevPageButton().addClass( 'disabled' );\n\t\t\t}\n\n\t\t\tif ( this.page >= this.totalPages() ) {\n\t\t\t\tthis.$nextPageButton().addClass( 'disabled' );\n\t\t\t\tthis.$lastPageButton().addClass( 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tvalidatePage: function( nextPage ) {\n\t\t\tconst self = this;\n\n\t\t\t// Validate the current page.\n\t\t\tacf.validateForm( {\n\t\t\t\tform: this.$control(),\n\t\t\t\tevent: '',\n\t\t\t\treset: true,\n\t\t\t\tsuccess: function( $form ) {\n\t\t\t\t\tself.page = nextPage;\n\n\t\t\t\t\t// Set up some sane defaults.\n\t\t\t\t\tif ( self.page <= 1 ) {\n\t\t\t\t\t\tself.page = 1;\n\t\t\t\t\t}\n\t\t\t\t\tif ( self.page >= self.totalPages() ) {\n\t\t\t\t\t\tself.page = self.totalPages();\n\t\t\t\t\t}\n\n\t\t\t\t\tself.ajaxLoadPage();\n\t\t\t\t},\n\t\t\t\tfailure: function( $form ) {\n\t\t\t\t\tself.$pageInput().val( self.page );\n\t\t\t\t\treturn false;\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tajaxLoadPage: function( clearChanged = false ) {\n\t\t\tconst ajaxData = acf.prepareForAjax( {\n\t\t\t\taction: 'acf/ajax/query_repeater',\n\t\t\t\tpaged: this.page,\n\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\tfield_name: this.get( 'orig_name' ),\n\t\t\t\trows_per_page: parseInt( this.get( 'per_page' ) ),\n\t\t\t\trefresh: clearChanged,\n\t\t\t\tnonce: this.get( 'nonce' )\n\t\t\t} );\n\n\t\t\t$.ajax(\n\t\t\t\t{\n\t\t\t\t\turl: ajaxurl,\n\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\tdataType: 'json',\n\t\t\t\t\tdata: ajaxData,\n\t\t\t\t\tcontext: this,\n\t\t\t\t}\n\t\t\t).done(\n\t\t\t\tfunction( response ) {\n\t\t\t\t\tconst { rows } = response.data;\n\t\t\t\t\tconst $existingRows = this.$tbody().find( '> tr' );\n\n\t\t\t\t\t$existingRows.not( '.acf-clone' ).hide();\n\n\t\t\t\t\tif ( clearChanged ) {\n\t\t\t\t\t\t// Remove any existing rows since we are refreshing from the server.\n\t\t\t\t\t\t$existingRows.not( '.acf-clone' ).remove();\n\n\t\t\t\t\t\t// Trigger a change in total rows, so we can update pagination.\n\t\t\t\t\t\tthis.set( 'total_rows', response.data.total_rows, false );\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$existingRows.not( '.acf-changed, .acf-deleted, .acf-reordered, .acf-added, .acf-inserted, .acf-clone' ).remove();\n\t\t\t\t\t}\n\n\t\t\t\t\tObject.keys( rows ).forEach( index => {\n\t\t\t\t\t\tlet $row         = false;\n\t\t\t\t\t\tlet $unsavedRow  = this.$tbody().find( '> *[data-id=row-' + index + ']' );\n\t\t\t\t\t\tlet $insertedRow = this.$tbody().find( '> *[data-inserted=' + index + ']' );\n\n\t\t\t\t\t\t// Unsaved new rows that are inserted into this specific position.\n\t\t\t\t\t\tif ( $insertedRow.length ) {\n\t\t\t\t\t\t\t$insertedRow.appendTo( this.$tbody() ).show();\n\t\t\t\t\t\t\tacf.doAction( 'remount', $insertedRow );\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Skip unsaved deleted rows; we don't want to show them again.\n\t\t\t\t\t\tif ( $unsavedRow.hasClass( 'acf-deleted' ) ) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Unsaved edited rows should be moved to correct position.\n\t\t\t\t\t\tif ( $unsavedRow.length ) {\n\t\t\t\t\t\t\tacf.doAction( 'unmount', $unsavedRow );\n\t\t\t\t\t\t\t$unsavedRow.appendTo( this.$tbody() ).show();\n\t\t\t\t\t\t\tacf.doAction( 'remount', $unsavedRow );\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Rows from the server (that haven't been changed or deleted) should be appended and shown.\n\t\t\t\t\t\t$row = $( rows[ index ] );\n\t\t\t\t\t\tthis.$tbody().append( $row ).show();\n\t\t\t\t\t\tacf.doAction( 'remount', $row );\n\n\t\t\t\t\t\t// Move clone field back to the right spot.\n\t\t\t\t\t\tthis.$clone().appendTo( this.$tbody() );\n\t\t\t\t\t} );\n\n\t\t\t\t\tconst $addedRows = this.$tbody().find( '.acf-added:hidden' );\n\n\t\t\t\t\t// If there are any new rows that are still hidden, append them to the bottom.\n\t\t\t\t\tif ( $addedRows.length ) {\n\t\t\t\t\t\tconst self = this;\n\n\t\t\t\t\t\t$addedRows.each( function() {\n\t\t\t\t\t\t\tconst $addedRow = $( this );\n\t\t\t\t\t\t\t$addedRow.insertBefore( self.$clone() ).show();\n\t\t\t\t\t\t\tacf.doAction( 'remount', $addedRow );\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\n\t\t\t\t\t// Update the page input.\n\t\t\t\t\tthis.$pageInput().val( this.page );\n\t\t\t\t\tthis.maybeDisablePagination();\n\t\t\t\t}\n\t\t\t).fail(\n\t\t\t\tfunction( jqXHR, textStatus, errorThrown ) {\n\t\t\t\t\tconst error = acf.getXhrError( jqXHR );\n\t\t\t\t\tlet message = acf.__( 'Error loading page' );\n\n\t\t\t\t\tif ( '' !== error ) {\n\t\t\t\t\t\tmessage = `${message}: ${error}`;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.showNotice( {\n\t\t\t\t\t\ttext: message,\n\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t);\n\t\t},\n\n\t} );\n\n\tacf.registerFieldType( Field );\n\n\t// register existing conditions\n\tacf.registerConditionForFieldType( 'hasValue', 'repeater' );\n\tacf.registerConditionForFieldType( 'hasNoValue', 'repeater' );\n\tacf.registerConditionForFieldType( 'lessThan', 'repeater' );\n\tacf.registerConditionForFieldType( 'greaterThan', 'repeater' );\n\n\t// state\n\tvar preference = new acf.Model( {\n\t\tname: 'this.collapsedRows',\n\n\t\tkey: function ( key, context ) {\n\t\t\t// vars\n\t\t\tvar count = this.get( key + context ) || 0;\n\n\t\t\t// update\n\t\t\tcount++;\n\t\t\tthis.set( key + context, count, true );\n\n\t\t\t// modify fieldKey\n\t\t\tif ( count > 1 ) {\n\t\t\t\tkey += '-' + count;\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn key;\n\t\t},\n\n\t\tload: function ( key ) {\n\t\t\t// vars\n\t\t\tvar key = this.key( key, 'load' );\n\t\t\tvar data = acf.getPreference( this.name );\n\n\t\t\t// return\n\t\t\tif ( data && data[ key ] ) {\n\t\t\t\treturn data[ key ];\n\t\t\t} else {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\n\t\tsave: function ( key, value ) {\n\t\t\t// vars\n\t\t\tvar key = this.key( key, 'save' );\n\t\t\tvar data = acf.getPreference( this.name ) || {};\n\n\t\t\t// delete\n\t\t\tif ( value === null ) {\n\t\t\t\tdelete data[ key ];\n\n\t\t\t\t// append\n\t\t\t} else {\n\t\t\t\tdata[ key ] = value;\n\t\t\t}\n\n\t\t\t// allow null\n\t\t\tif ( $.isEmptyObject( data ) ) {\n\t\t\t\tdata = null;\n\t\t\t}\n\n\t\t\t// save\n\t\t\tacf.setPreference( this.name, data );\n\t\t},\n\t} );\n} )( jQuery );\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_acf-field-repeater.js';\nimport './_acf-field-flexible-content.js';\nimport './_acf-field-gallery.js';\n"], "names": ["$", "Field", "acf", "extend", "type", "wait", "events", "showField", "unloadField", "mouseover", "$control", "$layoutsWrap", "$layouts", "$layout", "index", "$clonesWrap", "$clones", "$clone", "name", "$actions", "$button", "$popup", "getPopupHTML", "html", "$html", "self", "find", "each", "$a", "min", "data", "max", "count", "countLayouts", "addClass", "required", "title", "__", "identifier", "_n", "replace", "append", "outerHTML", "getValue", "length", "allowRemove", "parseInt", "get", "val", "allowAdd", "isFull", "addSortable", "sortable", "items", "handle", "forceHelperSize", "forcePlaceholderSize", "scroll", "stop", "event", "ui", "render", "update", "$input", "trigger", "addCollapsed", "indexes", "preference", "load", "i", "indexOf", "addUnscopedEvents", "on", "e", "onInvalidField", "initialize", "disable", "cid", "removeClass", "onShow", "$el", "context", "fields", "getFields", "is", "parent", "doAction", "filter", "countLayoutsByName", "currentLayout", "layoutMax", "text", "layoutLabel", "showNotice", "validateAdd", "onClickAdd", "hasClass", "closest", "popup", "Popup", "target", "targetConfirm", "confirm", "add", "layout", "before", "cancel", "args", "parseArgs", "duplicate", "proxy", "$el2", "enable", "onClickDuplicate", "first", "duplicateLayout", "<PERSON><PERSON><PERSON>", "rename", "value", "search", "after", "focusAttention", "validate<PERSON><PERSON>ove", "onClickRemove", "shift<PERSON>ey", "removeLayout", "tooltip", "newTooltip", "confirmRemove", "endHeight", "remove", "complete", "onClickCollapse", "isLayoutClosed", "openLayout", "closeLayout", "renderLayout", "children", "prefix", "attr", "ajaxData", "action", "field_key", "serialize", "ajax", "url", "prepareForAjax", "dataType", "success", "onUnload", "push", "save", "onHover", "off", "registerFieldType", "models", "TooltipConfirm", "registerConditionForFieldType", "Model", "key", "set", "getPreference", "isEmptyObject", "setPreference", "j<PERSON><PERSON><PERSON>", "actions", "validation_begin", "validation_failure", "resize", "onValidationBegin", "$sideData", "onValidationFailure", "$collection", "$attachments", "$attachment", "id", "$active", "$main", "$side", "onUpdate", "start", "placeholder", "item", "removeAttr", "resizable", "handles", "minHeight", "update_user_setting", "size", "height", "$sort", "$add", "width", "columns", "Math", "round", "onResize", "openSidebar", "css", "animate", "right", "closeSidebar", "frame", "newMediaPopup", "mode", "field", "multiple", "library", "allowedTypes", "selected", "select", "attachment", "appendAttachment", "validateAttachment", "getInputName", "join", "$before", "eq", "renderAttachment", "alt", "filename", "attributes", "isget", "image", "src", "editAttachment", "button", "onClickEdit", "removeAttachment", "preventDefault", "stopPropagation", "selectAttachment", "step1", "step2", "nonce", "has", "abort", "showLoading", "xhr", "cache", "step3", "onClickSelect", "onClickClose", "onChangeSort", "ids", "sort", "json", "isAjaxSuccess", "reverse", "map", "prepend", "$submit", "prev", "page", "nextRowNum", "change", "$table", "$tbody", "$rows", "not", "$row", "$firstPageButton", "$prevPageButton", "$nextPageButton", "$lastPageButton", "$pageInput", "totalPages", "numRows", "minRows", "maxRows", "isCollapsed", "expand", "$changed", "currentTarget", "onChangeField", "listenForSavedMetaBoxes", "update_order_numbers", "maybeDisablePagination", "isGutenbergPostEditor", "checkedMetaBoxes", "wp", "subscribe", "isSavingMetaBoxes", "ajaxLoadPage", "incrementTotalRows", "totalRows", "decrementTotalRows", "prevRowNum", "newRowNum", "updateRowStatus", "hide", "message", "duplicateRow", "onClickRowOrder", "show", "onBlurRowOrder", "onChangeRowOrder", "$orderSpan", "hrOrder", "isNumeric", "parseFloat", "newOrder", "onChangeTotalRows", "perPage", "ceil", "includes", "next", "collapse", "isCollpased", "$target", "<PERSON><PERSON><PERSON><PERSON>", "status", "parent_key", "parents", "row_id", "input_name", "status_name", "status_input", "$existing_status", "onClickFirstPage", "validatePage", "onClickPrevPage", "onClickNextPage", "onClickLastPage", "onChangeCurrentPage", "nextPage", "validateForm", "form", "reset", "$form", "failure", "clearChanged", "paged", "field_name", "rows_per_page", "refresh", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "method", "done", "response", "rows", "$existingRows", "total_rows", "Object", "keys", "for<PERSON>ach", "$unsavedRow", "$insertedRow", "appendTo", "$addedRows", "$addedRow", "insertBefore", "fail", "jqXHR", "textStatus", "errorThrown", "error", "getXhrError"], "sourceRoot": ""}