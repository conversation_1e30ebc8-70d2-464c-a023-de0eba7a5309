# SAP Contract Pricing API Documentation

## Overview

The SAP Contract Pricing API allows external systems to update customer-specific contract pricing data in WordPress/WooCommerce. This endpoint processes batch updates of contract pricing information and stores them in custom database tables for customer-specific pricing.

## Endpoint Details

- **URL**: `/wp-json/wc/v3/sap-contract-pricing`
- **Method**: `POST`
- **Authentication**: WordPress REST API (Basic Auth or Application Passwords)
- **Content-Type**: `application/json`

## Database Tables

The API uses three existing custom database tables:

- `wp_contract_pricing` - USD/US contract prices
- `wp_contract_pricing_eur` - EUR/EU contract prices
- `wp_contract_pricing_gbp` - GBP/GB contract prices

### Table Structure

Each table has the following structure:
- `customer_id` int(10) unsigned
- `product_sku` varchar(255) NULL
- `new_price` float(10,3)

## Request Format

### JSON Structure

```json
{
  "contractPricing": [
    {
      "materialNumber": "TWSLPL040068",
      "customer_id": "123456",
      "price": {
        "US": {
          "contract_price": 1813.500,
          "price_code": "6",
          "valid_to": "31/12/2024"
        },
        "EU": {
          "contract_price": 111.222,
          "price_code": "8",
          "valid_to": "15/06/2024"
        }
      }
    },
    {
      "materialNumber": "TWSLPL040073",
      "customer_id": "123456",
      "price": {
        "EU": {
          "contract_price": 222.333,
          "price_code": "8",
          "valid_to": "15/06/2024"
        },
        "GB": {
          "contract_price": 333.444,
          "price_code": "8",
          "valid_to": "31/12/2024"
        }
      }
    }
  ]
}
```

### Field Mapping

| SAP Field | Database Field | Description |
|-----------|----------------|-------------|
| `materialNumber` | `product_sku` | Product SKU identifier |
| `customer_id` | `customer_id` | Customer identifier |
| `contract_price` | `new_price` | Contract price amount |
| `price_code` | `price_code` | Price code (optional) |
| `valid_to` | `valid_to` | Expiration date (optional) |

### Supported Currencies

- **US/USD** → `wp_contract_pricing` table
- **EU/EUR** → `wp_contract_pricing_eur` table  
- **GB/GBP** → `wp_contract_pricing_gbp` table

## Response Format

### Success Response

```json
{
  "success": true,
  "total_materials_requested": 2,
  "total_materials_processed": 2,
  "total_currencies_updated": 3,
  "processed_materials": [
    {
      "material_number": "TWSLPL040068",
      "customer_id": "123456",
      "product_id": 123,
      "total_currencies_updated": 2,
      "updated_currencies": [
        {
          "currency": "US",
          "price": 1813.5,
          "price_code": "6",
          "valid_to": "31/12/2024",
          "operation": "updated",
          "record_id": 45,
          "table_name": "wp_contract_pricing"
        }
      ]
    }
  ],
  "errors": [],
  "request_id": "sap_contract_pricing_...",
  "timestamp": "2024-01-01 12:00:00"
}
```

### Error Response

```json
{
  "success": false,
  "total_materials_requested": 1,
  "total_materials_processed": 0,
  "total_currencies_updated": 0,
  "processed_materials": [],
  "errors": [
    "Item 0: Product not found for materialNumber: INVALID_SKU"
  ],
  "request_id": "sap_contract_pricing_...",
  "timestamp": "2024-01-01 12:00:00"
}
```

## Authentication

### Basic Authentication

```bash
curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-contract-pricing' \
  -H 'Content-Type: application/json' \
  -u 'username:password' \
  -d '{"contractPricing": [...]}'
```

### Application Password

```bash
curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-contract-pricing' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Basic [base64_encoded_username:app_password]' \
  -d '{"contractPricing": [...]}'
```

## Testing

### Test Endpoint

Check if the API is active:

```bash
GET /wp-json/wc/v3/sap-contract-pricing-test
```

Response:
```json
{
  "status": "working",
  "message": "SAP Contract Pricing API is active",
  "timestamp": "2024-01-01 12:00:00",
  "auth_required": "yes"
}
```

### Test Files

- `test-sap-contract-pricing.php` - Web-based test interface
- `test-sap-contract-pricing.ps1` - PowerShell test script

### PowerShell Test Example

```powershell
.\test-sap-contract-pricing.ps1 -SiteUrl 'https://yoursite.com' -Username 'your_username' -Password 'your_password'
```

## Logging

All API calls are logged to `/wp-content/logs/APIlogs.log` with detailed information:

- Request details (method, URL, timestamp, user agent, client IP)
- Authentication information
- Request data (sanitized)
- Processing results for each material and currency
- Response data
- Execution time

## Error Handling

### Common Errors

| Error | Description | Solution |
|-------|-------------|----------|
| `missing_material_number` | materialNumber field is missing | Include materialNumber in request |
| `missing_customer_id` | customer_id field is missing | Include customer_id in request |
| `missing_price_data` | price object is missing or invalid | Include valid price object |
| `product_not_found` | Product with given SKU not found | Verify product exists with correct SKU |
| `unsupported_currency` | Currency not supported | Use US/USD, EU/EUR, or GB/GBP |
| `missing_contract_price` | contract_price field missing | Include contract_price in price data |

### HTTP Status Codes

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication failed)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (product not found)
- `500` - Internal Server Error

## Required Permissions

Users must have one of the following capabilities:
- `manage_options` (Administrator)
- `manage_woocommerce` (WooCommerce admin)
- `edit_shop_orders` (WooCommerce orders)
- `edit_users` (User management)
- `edit_products` (Product management)

Or one of these roles:
- `administrator`
- `shop_manager`
- `editor`

## Data Processing

1. **Validation**: Validates JSON structure and required fields
2. **Product Lookup**: Finds products by SKU (materialNumber)
3. **Currency Processing**: Updates each currency in appropriate table
4. **Database Operations**: Insert new records or update existing ones
5. **Verification**: Confirms updates were successful
6. **Logging**: Records all operations for audit trail

## Integration Notes

- Supports batch processing of multiple materials
- Each material can have pricing for multiple currencies
- Existing records are updated, new records are inserted
- Customer-specific pricing allows different prices per customer
- All operations are logged for monitoring and debugging
