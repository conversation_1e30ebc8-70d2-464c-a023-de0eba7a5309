(()=>{var e={138:()=>{jQuery,acf.jsxNameReplacements={"accent-height":"accentHeight",accentheight:"accentHeight","accept-charset":"acceptCharset",acceptcharset:"acceptCharset",accesskey:"accessKey","alignment-baseline":"alignmentBaseline",alignmentbaseline:"alignmentBaseline",allowedblocks:"allowedBlocks",allowfullscreen:"allowFullScreen",allowreorder:"allowReorder","arabic-form":"arabicForm",arabicform:"arabicForm",attributename:"attributeName",attributetype:"attributeType",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autoreverse:"autoReverse",autosave:"autoSave",basefrequency:"baseFrequency","baseline-shift":"baselineShift",baselineshift:"baselineShift",baseprofile:"baseProfile",calcmode:"calcMode","cap-height":"capHeight",capheight:"capHeight",cellpadding:"cellPadding",cellspacing:"cellSpacing",charset:"charSet",class:"className",classid:"classID",classname:"className","clip-path":"clipPath","clip-rule":"clipRule",clippath:"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","color-interpolation":"colorInterpolation","color-interpolation-filters":"colorInterpolationFilters","color-profile":"colorProfile","color-rendering":"colorRendering",colorinterpolation:"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters",colorprofile:"colorProfile",colorrendering:"colorRendering",colspan:"colSpan",contenteditable:"contentEditable",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",contextmenu:"contextMenu",controlslist:"controlsList",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",datetime:"dateTime",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",diffuseconstant:"diffuseConstant",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback","dominant-baseline":"dominantBaseline",dominantbaseline:"dominantBaseline",edgemode:"edgeMode","enable-background":"enableBackground",enablebackground:"enableBackground",enctype:"encType",enterkeyhint:"enterKeyHint",externalresourcesrequired:"externalResourcesRequired","fill-opacity":"fillOpacity","fill-rule":"fillRule",fillopacity:"fillOpacity",fillrule:"fillRule",filterres:"filterRes",filterunits:"filterUnits","flood-color":"floodColor","flood-opacity":"floodOpacity",floodcolor:"floodColor",floodopacity:"floodOpacity","font-family":"fontFamily","font-size":"fontSize","font-size-adjust":"fontSizeAdjust","font-stretch":"fontStretch","font-style":"fontStyle","font-variant":"fontVariant","font-weight":"fontWeight",fontfamily:"fontFamily",fontsize:"fontSize",fontsizeadjust:"fontSizeAdjust",fontstretch:"fontStretch",fontstyle:"fontStyle",fontvariant:"fontVariant",fontweight:"fontWeight",for:"htmlFor",foreignobject:"foreignObject",formaction:"formAction",formenctype:"formEncType",formmethod:"formMethod",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder","glyph-name":"glyphName","glyph-orientation-horizontal":"glyphOrientationHorizontal","glyph-orientation-vertical":"glyphOrientationVertical",glyphname:"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits","horiz-adv-x":"horizAdvX","horiz-origin-x":"horizOriginX",horizadvx:"horizAdvX",horizoriginx:"horizOriginX",hreflang:"hrefLang",htmlfor:"htmlFor","http-equiv":"httpEquiv",httpequiv:"httpEquiv","image-rendering":"imageRendering",imagerendering:"imageRendering",innerhtml:"innerHTML",inputmode:"inputMode",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keyparams:"keyParams",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",keytype:"keyType",lengthadjust:"lengthAdjust","letter-spacing":"letterSpacing",letterspacing:"letterSpacing","lighting-color":"lightingColor",lightingcolor:"lightingColor",limitingconeangle:"limitingConeAngle",marginheight:"marginHeight",marginwidth:"marginWidth","marker-end":"markerEnd","marker-mid":"markerMid","marker-start":"markerStart",markerend:"markerEnd",markerheight:"markerHeight",markermid:"markerMid",markerstart:"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",maxlength:"maxLength",mediagroup:"mediaGroup",minlength:"minLength",nomodule:"noModule",novalidate:"noValidate",numoctaves:"numOctaves","overline-position":"overlinePosition","overline-thickness":"overlineThickness",overlineposition:"overlinePosition",overlinethickness:"overlineThickness","paint-order":"paintOrder",paintorder:"paintOrder","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",playsinline:"playsInline","pointer-events":"pointerEvents",pointerevents:"pointerEvents",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",refx:"refX",refy:"refY","rendering-intent":"renderingIntent",renderingintent:"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",rowspan:"rowSpan","shape-rendering":"shapeRendering",shaperendering:"shapeRendering",specularconstant:"specularConstant",specularexponent:"specularExponent",spellcheck:"spellCheck",spreadmethod:"spreadMethod",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles","stop-color":"stopColor","stop-opacity":"stopOpacity",stopcolor:"stopColor",stopopacity:"stopOpacity","strikethrough-position":"strikethroughPosition","strikethrough-thickness":"strikethroughThickness",strikethroughposition:"strikethroughPosition",strikethroughthickness:"strikethroughThickness","stroke-dasharray":"strokeDasharray","stroke-dashoffset":"strokeDashoffset","stroke-linecap":"strokeLinecap","stroke-linejoin":"strokeLinejoin","stroke-miterlimit":"strokeMiterlimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth",strokedasharray:"strokeDasharray",strokedashoffset:"strokeDashoffset",strokelinecap:"strokeLinecap",strokelinejoin:"strokeLinejoin",strokemiterlimit:"strokeMiterlimit",strokeopacity:"strokeOpacity",strokewidth:"strokeWidth",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tabindex:"tabIndex",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",templatelock:"templateLock","text-anchor":"textAnchor","text-decoration":"textDecoration","text-rendering":"textRendering",textanchor:"textAnchor",textdecoration:"textDecoration",textlength:"textLength",textrendering:"textRendering","underline-position":"underlinePosition","underline-thickness":"underlineThickness",underlineposition:"underlinePosition",underlinethickness:"underlineThickness","unicode-bidi":"unicodeBidi","unicode-range":"unicodeRange",unicodebidi:"unicodeBidi",unicoderange:"unicodeRange","units-per-em":"unitsPerEm",unitsperem:"unitsPerEm",usemap:"useMap","v-alphabetic":"vAlphabetic","v-hanging":"vHanging","v-ideographic":"vIdeographic","v-mathematical":"vMathematical",valphabetic:"vAlphabetic","vector-effect":"vectorEffect",vectoreffect:"vectorEffect","vert-adv-y":"vertAdvY","vert-origin-x":"vertOriginX","vert-origin-y":"vertOriginY",vertadvy:"vertAdvY",vertoriginx:"vertOriginX",vertoriginy:"vertOriginY",vhanging:"vHanging",videographic:"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",vmathematical:"vMathematical","word-spacing":"wordSpacing",wordspacing:"wordSpacing","writing-mode":"writingMode",writingmode:"writingMode","x-height":"xHeight",xchannelselector:"xChannelSelector",xheight:"xHeight","xlink:actuate":"xlinkActuate","xlink:arcrole":"xlinkArcrole","xlink:href":"xlinkHref","xlink:role":"xlinkRole","xlink:show":"xlinkShow","xlink:title":"xlinkTitle","xlink:type":"xlinkType",xlinkactuate:"xlinkActuate",xlinkarcrole:"xlinkArcrole",xlinkhref:"xlinkHref",xlinkrole:"xlinkRole",xlinkshow:"xlinkShow",xlinktitle:"xlinkTitle",xlinktype:"xlinkType","xml:base":"xmlBase","xml:lang":"xmlLang","xml:space":"xmlSpace",xmlbase:"xmlBase",xmllang:"xmlLang","xmlns:xlink":"xmlnsXlink",xmlnsxlink:"xmlnsXlink",xmlspace:"xmlSpace",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"}},151:e=>{var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t},bytesToString:function(e){for(var t=[],r=0;r<e.length;r++)t.push(String.fromCharCode(e[r]));return t.join("")}}};e.exports=t},939:e=>{var t,r;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&r.rotl(e,8)|4278255360&r.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=r.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],r=0,n=0;r<e.length;r++,n+=8)t[n>>>5]|=e[r]<<24-n%32;return t},wordsToBytes:function(e){for(var t=[],r=0;r<32*e.length;r+=8)t.push(e[r>>>5]>>>24-r%32&255);return t},bytesToHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},bytesToBase64:function(e){for(var r=[],n=0;n<e.length;n+=3)for(var o=e[n]<<16|e[n+1]<<8|e[n+2],i=0;i<4;i++)8*n+6*i<=8*e.length?r.push(t.charAt(o>>>6*(3-i)&63)):r.push("=");return r.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var r=[],n=0,o=0;n<e.length;o=++n%4)0!=o&&r.push((t.indexOf(e.charAt(n-1))&Math.pow(2,-2*o+8)-1)<<2*o|t.indexOf(e.charAt(n))>>>6-2*o);return r}},e.exports=r},206:e=>{function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},503:(e,t,r)=>{var n,o,i,s,a;n=r(939),o=r(151).utf8,i=r(206),s=r(151).bin,(a=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?s.stringToBytes(e):o.stringToBytes(e):i(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var r=n.bytesToWords(e),c=8*e.length,l=1732584193,u=-271733879,p=-1732584194,d=271733878,f=0;f<r.length;f++)r[f]=16711935&(r[f]<<8|r[f]>>>24)|4278255360&(r[f]<<24|r[f]>>>8);r[c>>>5]|=128<<c%32,r[14+(c+64>>>9<<4)]=c;var h=a._ff,m=a._gg,g=a._hh,y=a._ii;for(f=0;f<r.length;f+=16){var b=l,k=u,v=p,x=d;l=h(l,u,p,d,r[f+0],7,-680876936),d=h(d,l,u,p,r[f+1],12,-389564586),p=h(p,d,l,u,r[f+2],17,606105819),u=h(u,p,d,l,r[f+3],22,-1044525330),l=h(l,u,p,d,r[f+4],7,-176418897),d=h(d,l,u,p,r[f+5],12,1200080426),p=h(p,d,l,u,r[f+6],17,-1473231341),u=h(u,p,d,l,r[f+7],22,-45705983),l=h(l,u,p,d,r[f+8],7,1770035416),d=h(d,l,u,p,r[f+9],12,-1958414417),p=h(p,d,l,u,r[f+10],17,-42063),u=h(u,p,d,l,r[f+11],22,-1990404162),l=h(l,u,p,d,r[f+12],7,1804603682),d=h(d,l,u,p,r[f+13],12,-40341101),p=h(p,d,l,u,r[f+14],17,-1502002290),l=m(l,u=h(u,p,d,l,r[f+15],22,1236535329),p,d,r[f+1],5,-165796510),d=m(d,l,u,p,r[f+6],9,-1069501632),p=m(p,d,l,u,r[f+11],14,643717713),u=m(u,p,d,l,r[f+0],20,-373897302),l=m(l,u,p,d,r[f+5],5,-701558691),d=m(d,l,u,p,r[f+10],9,38016083),p=m(p,d,l,u,r[f+15],14,-660478335),u=m(u,p,d,l,r[f+4],20,-405537848),l=m(l,u,p,d,r[f+9],5,568446438),d=m(d,l,u,p,r[f+14],9,-1019803690),p=m(p,d,l,u,r[f+3],14,-187363961),u=m(u,p,d,l,r[f+8],20,1163531501),l=m(l,u,p,d,r[f+13],5,-1444681467),d=m(d,l,u,p,r[f+2],9,-51403784),p=m(p,d,l,u,r[f+7],14,1735328473),l=g(l,u=m(u,p,d,l,r[f+12],20,-1926607734),p,d,r[f+5],4,-378558),d=g(d,l,u,p,r[f+8],11,-2022574463),p=g(p,d,l,u,r[f+11],16,1839030562),u=g(u,p,d,l,r[f+14],23,-35309556),l=g(l,u,p,d,r[f+1],4,-1530992060),d=g(d,l,u,p,r[f+4],11,1272893353),p=g(p,d,l,u,r[f+7],16,-155497632),u=g(u,p,d,l,r[f+10],23,-1094730640),l=g(l,u,p,d,r[f+13],4,681279174),d=g(d,l,u,p,r[f+0],11,-358537222),p=g(p,d,l,u,r[f+3],16,-722521979),u=g(u,p,d,l,r[f+6],23,76029189),l=g(l,u,p,d,r[f+9],4,-640364487),d=g(d,l,u,p,r[f+12],11,-421815835),p=g(p,d,l,u,r[f+15],16,530742520),l=y(l,u=g(u,p,d,l,r[f+2],23,-995338651),p,d,r[f+0],6,-198630844),d=y(d,l,u,p,r[f+7],10,1126891415),p=y(p,d,l,u,r[f+14],15,-1416354905),u=y(u,p,d,l,r[f+5],21,-57434055),l=y(l,u,p,d,r[f+12],6,1700485571),d=y(d,l,u,p,r[f+3],10,-1894986606),p=y(p,d,l,u,r[f+10],15,-1051523),u=y(u,p,d,l,r[f+1],21,-2054922799),l=y(l,u,p,d,r[f+8],6,1873313359),d=y(d,l,u,p,r[f+15],10,-30611744),p=y(p,d,l,u,r[f+6],15,-1560198380),u=y(u,p,d,l,r[f+13],21,1309151649),l=y(l,u,p,d,r[f+4],6,-145523070),d=y(d,l,u,p,r[f+11],10,-1120210379),p=y(p,d,l,u,r[f+2],15,718787259),u=y(u,p,d,l,r[f+9],21,-343485551),l=l+b>>>0,u=u+k>>>0,p=p+v>>>0,d=d+x>>>0}return n.endian([l,u,p,d])})._ff=function(e,t,r,n,o,i,s){var a=e+(t&r|~t&n)+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._gg=function(e,t,r,n,o,i,s){var a=e+(t&n|r&~n)+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._hh=function(e,t,r,n,o,i,s){var a=e+(t^r^n)+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._ii=function(e,t,r,n,o,i,s){var a=e+(r^(t|~n))+(o>>>0)+s;return(a<<i|a>>>32-i)+t},a._blocksize=16,a._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var r=n.wordsToBytes(a(e,t));return t&&t.asBytes?r:t&&t.asString?s.bytesToString(r):n.bytesToHex(r)}},20:(e,t,r)=>{"use strict";var n=r(540),o=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),s=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,r){var n,c={},l=null,u=null;for(n in void 0!==r&&(l=""+r),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,n)&&!a.hasOwnProperty(n)&&(c[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===c[n]&&(c[n]=t[n]);return{$$typeof:o,type:e,key:l,ref:u,props:c,_owner:s.current}}t.jsx=c,t.jsxs=c},287:(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),f=Symbol.iterator,h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,r){this.props=e,this.context=t,this.refs=g,this.updater=r||h}function b(){}function k(e,t,r){this.props=e,this.context=t,this.refs=g,this.updater=r||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=y.prototype;var v=k.prototype=new b;v.constructor=k,m(v,y.prototype),v.isPureReactComponent=!0;var x=Array.isArray,w=Object.prototype.hasOwnProperty,_={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function j(e,t,n){var o,i={},s=null,a=null;if(null!=t)for(o in void 0!==t.ref&&(a=t.ref),void 0!==t.key&&(s=""+t.key),t)w.call(t,o)&&!S.hasOwnProperty(o)&&(i[o]=t[o]);var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){for(var l=Array(c),u=0;u<c;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(o in c=e.defaultProps)void 0===i[o]&&(i[o]=c[o]);return{$$typeof:r,type:e,key:s,ref:a,props:i,_owner:_.current}}function A(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var I=/\/+/g;function T(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function C(e,t,o,i,s){var a=typeof e;"undefined"!==a&&"boolean"!==a||(e=null);var c=!1;if(null===e)c=!0;else switch(a){case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case r:case n:c=!0}}if(c)return s=s(c=e),e=""===i?"."+T(c,0):i,x(s)?(o="",null!=e&&(o=e.replace(I,"$&/")+"/"),C(s,t,o,"",(function(e){return e}))):null!=s&&(A(s)&&(s=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,o+(!s.key||c&&c.key===s.key?"":(""+s.key).replace(I,"$&/")+"/")+e)),t.push(s)),1;if(c=0,i=""===i?".":i+":",x(e))for(var l=0;l<e.length;l++){var u=i+T(a=e[l],l);c+=C(a,t,o,u,s)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=f&&e[f]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),l=0;!(a=e.next()).done;)c+=C(a=a.value,t,o,u=i+T(a,l++),s);else if("object"===a)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return c}function E(e,t,r){if(null==e)return e;var n=[],o=0;return C(e,n,"","",(function(e){return t.call(r,e,o++)})),n}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var B={current:null},P={transition:null},R={ReactCurrentDispatcher:B,ReactCurrentBatchConfig:P,ReactCurrentOwner:_};function $(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:E,forEach:function(e,t,r){E(e,(function(){t.apply(this,arguments)}),r)},count:function(e){var t=0;return E(e,(function(){t++})),t},toArray:function(e){return E(e,(function(e){return e}))||[]},only:function(e){if(!A(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=o,t.Profiler=s,t.PureComponent=k,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R,t.act=$,t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),i=e.key,s=e.ref,a=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,a=_.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(l in t)w.call(t,l)&&!S.hasOwnProperty(l)&&(o[l]=void 0===t[l]&&void 0!==c?c[l]:t[l])}var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){c=Array(l);for(var u=0;u<l;u++)c[u]=arguments[u+2];o.children=c}return{$$typeof:r,type:e.type,key:i,ref:s,props:o,_owner:a}},t.createContext=function(e){return(e={$$typeof:c,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},t.createElement=j,t.createFactory=function(e){var t=j.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:l,render:e}},t.isValidElement=A,t.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=P.transition;P.transition={};try{e()}finally{P.transition=t}},t.unstable_act=$,t.useCallback=function(e,t){return B.current.useCallback(e,t)},t.useContext=function(e){return B.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return B.current.useDeferredValue(e)},t.useEffect=function(e,t){return B.current.useEffect(e,t)},t.useId=function(){return B.current.useId()},t.useImperativeHandle=function(e,t,r){return B.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return B.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return B.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return B.current.useMemo(e,t)},t.useReducer=function(e,t,r){return B.current.useReducer(e,t,r)},t.useRef=function(e){return B.current.useRef(e)},t.useState=function(e){return B.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return B.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return B.current.useTransition()},t.version="18.3.1"},540:(e,t,r)=>{"use strict";e.exports=r(287)},848:(e,t,r)=>{"use strict";e.exports=r(20)}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,r,n){return(r=function(t){var r=function(t){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==e(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}r(138);var n=r(848);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?o(Object(n),!0).forEach((function(r){t(e,r,n[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const s=r(503);((e,t)=>{const{BlockControls:r,InspectorControls:o,InnerBlocks:a,useBlockProps:c,AlignmentToolbar:l,BlockVerticalAlignmentToolbar:u}=wp.blockEditor,{ToolbarGroup:p,ToolbarButton:d,Placeholder:f,Spinner:h}=wp.components,{Fragment:m}=wp.element,{Component:g}=React,{useSelect:y}=wp.data,{createHigherOrderComponent:b}=wp.compose,k=wp.blockEditor.__experimentalBlockAlignmentMatrixToolbar||wp.blockEditor.BlockAlignmentMatrixToolbar,v=wp.blockEditor.__experimentalBlockAlignmentMatrixControl||wp.blockEditor.BlockAlignmentMatrixControl,x=wp.blockEditor.__experimentalBlockFullHeightAligmentControl||wp.blockEditor.__experimentalBlockFullHeightAlignmentControl||wp.blockEditor.BlockFullHeightAlignmentControl,w=wp.blockEditor.__experimentalUseInnerBlocksProps||wp.blockEditor.useInnerBlocksProps,_={};function S(e){return _[e]||!1}function j(e){return S(e).acf_block_version||1}function A(e){return S(e).validate}function I(e){const t=wp.data.select("core/block-editor").getBlockParents(e);return wp.data.select("core/block-editor").getBlocksByClientId(t).filter((e=>"core/query"===e.name)).length}function T(){return"string"==typeof pagenow&&"site-editor"===pagenow}function C(){const e=B("core/edit-post");return!!e&&!!e.isEditingTemplate&&e.isEditingTemplate()}function E(){return e("iframe[name=editor-canvas]").length&&!function(){const e=B("core/edit-post");return!e||(e.__experimentalGetPreviewDeviceType?"Desktop"===e.__experimentalGetPreviewDeviceType():!e.getPreviewDeviceType||"Desktop"===e.getPreviewDeviceType())}()}function O(e){const o=e.post_types||[];if(o.length){o.push("wp_block");const e=acf.get("postType");if(!o.includes(e))return!1}if("string"==typeof e.icon&&"<svg"===e.icon.substr(0,4)){const t=e.icon;e.icon=(0,n.jsx)(q,{children:t})}e.icon||delete e.icon,wp.blocks.getCategories().filter((({slug:t})=>t===e.category)).pop()||(e.category="common"),e=acf.parseArgs(e,{title:"",name:"",category:"",api_version:2,acf_block_version:1});for(const t in e.attributes)"default"in e.attributes[t]&&0===e.attributes[t].default.length&&delete e.attributes[t].default;e.supports.anchor&&(e.attributes.anchor={type:"string"});let s=U,a=L;var c;(e.supports.alignText||e.supports.align_text)&&(e.attributes=Z(e.attributes,"align_text","string"),s=function(e,t){const o=Q;return t.alignText=o(t.alignText),class extends g{render(){const{attributes:t,setAttributes:s}=this.props,{alignText:a}=t;return(0,n.jsxs)(m,{children:[(0,n.jsx)(r,{group:"block",children:(0,n.jsx)(l,{value:o(a),onChange:function(e){s({alignText:o(e)})}})}),(0,n.jsx)(e,i({},this.props))]})}}}(s,e)),(e.supports.alignContent||e.supports.align_content)&&(e.attributes=Z(e.attributes,"align_content","string"),s=function(e,o){let s,a,c=o.supports.align_content||o.supports.alignContent;return"matrix"===c?(s=v||k,a=K):(s=u,a=G),s===t?(console.warn(`The "${c}" alignment component was not found.`),e):(o.alignContent=a(o.alignContent),class extends g{render(){const{attributes:t,setAttributes:o}=this.props,{alignContent:c}=t;return(0,n.jsxs)(m,{children:[(0,n.jsx)(r,{group:"block",children:(0,n.jsx)(s,{label:acf.__("Change content alignment"),value:a(c),onChange:function(e){o({alignContent:a(e)})}})}),(0,n.jsx)(e,i({},this.props))]})}})}(s,e)),(e.supports.fullHeight||e.supports.full_height)&&(e.attributes=Z(e.attributes,"full_height","boolean"),c=s,e.blockType,s=x?class extends g{render(){const{attributes:e,setAttributes:t}=this.props,{fullHeight:o}=e;return(0,n.jsxs)(m,{children:[(0,n.jsx)(r,{group:"block",children:(0,n.jsx)(x,{isActive:o,onToggle:function(e){t({fullHeight:e})}})}),(0,n.jsx)(c,i({},this.props))]})}}:c),e.edit=e=>(wp.element.useEffect((()=>()=>{wp.data.dispatch("core/editor")&&wp.data.dispatch("core/editor").unlockPostSaving("acf/block/"+e.clientId)}),[]),(0,n.jsx)(s,i({},e))),e.save=()=>(0,n.jsx)(a,{}),_[e.name]=e;const p=wp.blocks.registerBlockType(e.name,e);return p.attributes.anchor&&(p.attributes.anchor={type:"string"}),p}function B(e){return"core/block-editor"===e?wp.data.select("core/block-editor")||wp.data.select("core/editor"):wp.data.select(e)}acf.blockInstances={};const P={},R={};function $(t){const{attributes:r={},context:n={},query:o={},clientId:a=null,delay:c=0}=t,l=s(JSON.stringify(i(i(i({},r),n),o))),u=P[l]||{query:{},timeout:!1,promise:e.Deferred(),started:!1};return u.query=i(i({},u.query),o),u.started||(clearTimeout(u.timeout),u.timeout=setTimeout((()=>{u.started=!0,R[l]?(P[l]=null,u.promise.resolve.apply(R[l][0],R[l][1])):e.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",cache:!1,data:acf.prepareForAjax({action:"acf/ajax/fetch-block",block:JSON.stringify(r),clientId:a,context:JSON.stringify(n),query:u.query})}).always((()=>{P[l]=null})).done((function(){R[l]=[this,arguments],u.promise.resolve.apply(this,arguments)})).fail((function(){u.promise.reject.apply(this,arguments)}))}),c),P[l]=u),u.promise}function D(e,t){return JSON.stringify(e)===JSON.stringify(t)}function F(e,t,r=0){const o=function(e,t){switch(e){case"innerblocks":return t<2?a:"ACFInnerBlocks";case"script":return W;case"#comment":return null;default:e=N(e)}return e}(e.nodeName.toLowerCase(),t);if(!o)return null;const s={};if(1===r&&"ACFInnerBlocks"!==o&&(s.ref=React.createRef()),acf.arrayArgs(e.attributes).map(H).forEach((({name:e,value:t})=>{s[e]=t})),"ACFInnerBlocks"===o)return(0,n.jsx)(M,i({},s));const c=[o,s];return acf.arrayArgs(e.childNodes).forEach((e=>{if(e instanceof Text){const t=e.textContent;t&&c.push(t)}else c.push(F(e,t,r+1))})),React.createElement.apply(this,c)}function N(e){return acf.isget(acf,"jsxNameReplacements",e)||e}function M(e){const{className:t="acf-innerblocks-container"}=e,r=w({className:t},e);return(0,n.jsx)("div",i(i({},r),{},{children:r.children}))}function H(e){let t=e.name,r=e.value,n=acf.applyFilters("acf_blocks_parse_node_attr",!1,e);if(n)return n;switch(t){case"class":t="className";break;case"style":const e={};r.split(";").forEach((t=>{const r=t.indexOf(":");if(r>0){let n=t.substr(0,r).trim();const o=t.substr(r+1).trim();"-"!==n.charAt(0)&&(n=acf.strCamelCase(n)),e[n]=o}})),r=e;break;default:if(0===t.indexOf("data-"))break;t=N(t);const n=r.charAt(0);"["!==n&&"{"!==n||(r=JSON.parse(r)),"true"!==r&&"false"!==r||(r="true"===r)}return{name:t,value:r}}acf.parseJSX=(t,r)=>(t=(t="<div>"+t+"</div>").replace(/<InnerBlocks([^>]+)?\/>/,"<InnerBlocks$1></InnerBlocks>"),F(e(t)[0],r,0).props.children);const V=b((e=>class extends g{constructor(e){super(e);const{name:r,attributes:n}=this.props,o=S(r);if(!o)return;Object.keys(n).forEach((e=>{""===n[e]&&delete n[e]}));const i={full_height:"fullHeight",align_content:"alignContent",align_text:"alignText"};Object.keys(i).forEach((e=>{n[e]!==t?n[i[e]]=n[e]:n[i[e]]===t&&o[e]!==t&&(n[i[e]]=o[e]),delete o[e],delete n[e]}));for(let e in o.attributes)n[e]===t&&o[e]!==t&&(n[e]=o[e])}render(){return(0,n.jsx)(e,i({},this.props))}}),"withDefaultAttributes");function L(){return(0,n.jsx)(a.Content,{})}wp.hooks.addFilter("editor.BlockListBlock","acf/with-default-attributes",V);class U extends g{constructor(e){super(e),this.setup()}setup(){const{name:e,attributes:t,clientId:r}=this.props,n=S(e);function o(e){e.includes(t.mode)||(t.mode=e[0])}if(I(r)||T()||E()||C())o(["preview"]);else switch(n.mode){case"edit":o(["edit","preview"]);break;case"preview":o(["preview","edit"]);break;default:o(["auto"])}}render(){const{name:e,attributes:t,setAttributes:s,clientId:a}=this.props,c=S(e),l=I(a)||T()||E()||C();let{mode:u}=t;l&&(u="preview");let f=c.supports.mode;("auto"===u||l)&&(f=!1);const h="preview"===u?acf.__("Switch to Edit"):acf.__("Switch to Preview"),g="preview"===u?"edit":"welcome-view-site";return(0,n.jsxs)(m,{children:[(0,n.jsx)(r,{children:f&&(0,n.jsx)(p,{children:(0,n.jsx)(d,{className:"components-icon-button components-toolbar__control",label:h,icon:g,onClick:function(){s({mode:"preview"===u?"edit":"preview"})}})})}),(0,n.jsx)(o,{children:"preview"===u&&(0,n.jsx)("div",{className:"acf-block-component acf-block-panel",children:(0,n.jsx)(J,i({},this.props))})}),(0,n.jsx)(z,i({},this.props))]})}}function z(e){const{attributes:t,isSelected:r,name:o,clientId:s}=e,{mode:a}=t,l=y((e=>{const t=e("core/block-editor").getBlockRootClientId(s);return e("core/block-editor").getBlockIndex(s,t)}));let u=!0,p="acf-block-component acf-block-body";return("auto"===a&&!r||"preview"===a)&&(p+=" acf-block-preview",u=!1),s in acf.blockInstances||(acf.blockInstances[s]={validation_errors:!1,mode:a}),acf.blockInstances[s].mode=a,r||(A(o)&&acf.blockInstances[s].validation_errors&&(p+=" acf-block-has-validation-error"),acf.blockInstances[s].has_been_deselected=!0),j(o)>1?(0,n.jsx)("div",i(i({},c({className:p})),{},{children:u?(0,n.jsx)(J,i(i({},e),{},{index:l})):(0,n.jsx)(Y,i(i({},e),{},{index:l}))})):(0,n.jsx)("div",i(i({},c()),{},{children:(0,n.jsx)("div",{className:"acf-block-component acf-block-body",children:u?(0,n.jsx)(J,i(i({},e),{},{index:l})):(0,n.jsx)(Y,i(i({},e),{},{index:l}))})}))}class q extends g{render(){return(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:this.props.children}})}}class W extends g{render(){return(0,n.jsx)("div",{ref:e=>this.el=e})}setHTML(t){e(this.el).html(`<script>${t}<\/script>`)}componentDidUpdate(){this.setHTML(this.props.children)}componentDidMount(){this.setHTML(this.props.children)}}class X extends g{constructor(e){super(e),this.setRef=this.setRef.bind(this),this.id="",this.el=!1,this.subscribed=!0,this.renderMethod="jQuery",this.passedValidation=!1,this.setup(e),this.loadState()}setup(e){const t=this.constructor.name,r=e.clientId;r in acf.blockInstances||(acf.blockInstances[r]={validation_errors:!1,mode:e.mode}),t in acf.blockInstances[r]||(acf.blockInstances[r][t]={})}fetch(){}maybePreload(e,t,r){if(acf.debug("Preload check",e,t,r),!I(this.props.clientId)){const n=acf.get("preloadedBlocks");if(n&&n[e])return r&&!n[e].form||!r&&n[e].form?(acf.debug("Preload failed: state not preloaded."),!1):(n[e].html=n[e].html.replaceAll(e,t),n[e].validation&&n[e].validation.errors&&(n[e].validation.errors=n[e].validation.errors.map((r=>(r.input=r.input.replaceAll(e,t),r)))),acf.debug("Preload successful",n[e]),n[e])}return acf.debug("Preload failed: not preloaded."),!1}loadState(){const e=acf.blockInstances[this.props.clientId]||{};this.state=e[this.constructor.name]||{}}setState(e){acf.blockInstances[this.props.clientId][this.constructor.name]=i(i({},this.state),e),this.subscribed&&super.setState(e),acf.debug("SetState",Object.assign({},this),this.props.clientId,this.constructor.name,Object.assign({},acf.blockInstances[this.props.clientId][this.constructor.name]))}setHtml(t){if((t=t?t.trim():"")===this.state.html)return;const r={html:t};if("jsx"===this.renderMethod){if(r.jsx=acf.parseJSX(t,j(this.props.name)),r.jsx||(console.warn("Your ACF block template contains no valid HTML elements. Appending a empty div to prevent React JS errors."),r.html+="<div></div>",r.jsx=acf.parseJSX(r.html,j(this.props.name))),Array.isArray(r.jsx)){let e=r.jsx.find((e=>React.isValidElement(e)));r.ref=e.ref}else r.ref=r.jsx.ref;r.$el=e(this.el)}else r.$el=e(t);this.setState(r)}setRef(e){this.el=e}render(){return this.state.jsx?j(this.props.name)>1?(this.setRef(this.state.jsx),this.state.jsx):(0,n.jsx)("div",{ref:this.setRef,children:this.state.jsx}):(0,n.jsx)("div",{ref:this.setRef,children:(0,n.jsx)(f,{children:(0,n.jsx)(h,{})})})}shouldComponentUpdate({index:e},{html:t}){return e!==this.props.index&&this.componentWillMove(),t!==this.state.html}display(t){if("jQuery"===this.renderMethod){const t=this.state.$el,r=t.parent(),n=e(this.el);n.html(t),r.length&&r[0]!==n[0]&&r.html(t.clone())}switch(this.getValidationErrors()&&this.isNotNewlyAdded()?this.lockBlockForSaving():this.unlockBlockForSaving(),t){case"append":this.componentDidAppend();break;case"remount":this.componentDidRemount()}}validate(){}componentDidMount(){this.state.html===t?this.fetch():this.display("remount")}componentDidUpdate(e,t){this.display("append")}componentDidAppend(){acf.doAction("append",this.state.$el)}componentWillUnmount(){acf.doAction("unmount",this.state.$el),this.subscribed=!1}componentDidRemount(){this.subscribed=!0,setTimeout((()=>{acf.doAction("remount",this.state.$el)}))}componentWillMove(){acf.doAction("unmount",this.state.$el),setTimeout((()=>{acf.doAction("remount",this.state.$el)}))}isNotNewlyAdded(){return acf.blockInstances[this.props.clientId].has_been_deselected||!1}hasShownValidation(){return acf.blockInstances[this.props.clientId].shown_validation||!1}setShownValidation(){acf.blockInstances[this.props.clientId].shown_validation=!0}setValidationErrors(e){acf.blockInstances[this.props.clientId].validation_errors=e}getValidationErrors(){return acf.blockInstances[this.props.clientId].validation_errors}getMode(){return acf.blockInstances[this.props.clientId].mode}lockBlockForSaving(){wp.data.dispatch("core/editor")&&wp.data.dispatch("core/editor").lockPostSaving("acf/block/"+this.props.clientId)}unlockBlockForSaving(){wp.data.dispatch("core/editor")&&wp.data.dispatch("core/editor").unlockPostSaving("acf/block/"+this.props.clientId)}displayValidation(e){if(!A(this.props.name))return void acf.debug("Block does not support validation");if(!e||e.hasClass("acf-empty-block-fields"))return void acf.debug("There is no edit form available to validate.");const t=this.getValidationErrors();acf.debug("Starting handle validation",Object.assign({},this),Object.assign({},e),t),this.setShownValidation();let r=acf.getBlockFormValidator(e);r.clearErrors(),acf.doAction("blocks/validation/pre_apply",t),t?(r.addErrors(t),r.showErrors("after"),this.lockBlockForSaving()):(r.has("notice")&&(r.get("notice").update({type:"success",text:acf.__("Validation successful"),timeout:1e3}),r.set("notice",null)),this.unlockBlockForSaving()),acf.doAction("blocks/validation/post_apply",t)}}class J extends X{setup(e){this.id=`BlockForm-${e.clientId}`,super.setup(e)}fetch(e=!1,t=!1){const{context:r,clientId:n,name:o}=this.props;let{attributes:i}=this.props,s={form:!0};e&&(s={validate:!0},i.data=t);const a=ee(i,r);acf.debug("BlockForm fetch",i,s);const c=this.maybePreload(a,n,!0);if(c)return this.setHtml(acf.applyFilters("blocks/form/render",c.html,!0)),void(c.validation&&this.setValidationErrors(c.validation.errors));A(o)||(s.validate=!1),$({attributes:i,context:r,clientId:n,query:s}).done((({data:e})=>{acf.debug("fetch block form promise"),e?(e.form&&this.setHtml(acf.applyFilters("blocks/form/render",e.form.replaceAll(e.clientId,n),!1)),e.validation&&this.setValidationErrors(e.validation.errors),this.isNotNewlyAdded()&&(acf.debug("Block has already shown it's invalid. The form needs to show validation errors"),this.validate())):this.setHtml(`<div class="acf-block-fields acf-fields acf-empty-block-fields">${acf.__("Error loading block form")}</div>`)}))}validate(e=!0){e&&this.loadState(),acf.debug("BlockForm calling validate with state",Object.assign({},this)),super.displayValidation(this.state.$el)}shouldComponentUpdate(e,t){return A(this.props.name)&&this.state.$el&&this.isNotNewlyAdded()&&!this.hasShownValidation()&&this.validate(!1),super.shouldComponentUpdate(e,t)}componentWillUnmount(){super.componentWillUnmount(),acf.debug("BlockForm Component did unmount")}componentDidRemount(){super.componentDidRemount(),acf.debug("BlockForm component did remount");const{$el:e}=this.state;A(this.props.name)&&this.isNotNewlyAdded()&&(acf.debug("Block has already shown it's invalid. The form needs to show validation errors"),this.validate()),!0!==e.data("acf-events-added")&&this.componentDidAppend()}componentDidAppend(){super.componentDidAppend(),acf.debug("BlockForm component did append");const{attributes:e,setAttributes:t,clientId:r,name:n}=this.props,o=this,{$el:i}=this.state;function s(s=!1){const a=acf.serialize(i,`acf-block_${r}`);s?e.data=a:t({data:a}),A(n)&&!s&&"edit"===o.getMode()&&(acf.debug("No block preview currently available. Need to trigger a validation only fetch."),o.fetch(!0,a))}let a=!1;i.on("change keyup",(()=>{clearTimeout(a),a=setTimeout(s,300)})),i.data("acf-events-added",!0),e.data||s(!0)}}class Y extends X{setup(e){const t=S(e.name),r=acf.isget(this.props,"context","postId");this.id=`BlockPreview-${e.clientId}`,super.setup(e),r&&(this.id=`BlockPreview-${e.clientId}-${r}`),t.supports.jsx&&(this.renderMethod="jsx")}fetch(e={}){const{attributes:t=this.props.attributes,clientId:r=this.props.clientId,context:n=this.props.context,delay:o=0}=e,{name:i}=this.props;this.setState({prevAttributes:t,prevContext:n});const s=ee(t,n);let a=this.maybePreload(s,r,!1);if(a)return 1==j(i)&&(a.html='<div class="acf-block-preview">'+a.html+"</div>"),this.setHtml(acf.applyFilters("blocks/preview/render",a.html,!0)),void(a.validation&&this.setValidationErrors(a.validation.errors));let c={preview:!0};A(i)||(c.validate=!1),$({attributes:t,context:n,clientId:r,query:c,delay:o}).done((({data:e})=>{if(!e)return void this.setHtml(`<div class="acf-block-fields acf-fields acf-empty-block-fields">${acf.__("Error previewing block")}</div>`);let t=e.preview.replaceAll(e.clientId,r);1==j(i)&&(t='<div class="acf-block-preview">'+t+"</div>"),acf.debug("fetch block render promise"),this.setHtml(acf.applyFilters("blocks/preview/render",t,!1)),e.validation&&this.setValidationErrors(e.validation.errors),this.isNotNewlyAdded()&&this.validate()}))}validate(){const e=(acf.blockInstances[this.props.clientId]||{}).BlockForm||!1;e&&super.displayValidation(e.$el)}componentDidAppend(){super.componentDidAppend(),this.renderBlockPreviewEvent()}shouldComponentUpdate(e,t){const r=e.attributes,n=this.props.attributes;if(!D(r,n)||!D(e.context,this.props.context)){let t=0;r.className!==n.className&&(t=300),r.anchor!==n.anchor&&(t=300),acf.debug("Triggering fetch from block preview shouldComponentUpdate"),this.fetch({attributes:r,context:e.context,delay:t})}return super.shouldComponentUpdate(e,t)}renderBlockPreviewEvent(){const{attributes:t,name:r}=this.props,{$el:n,ref:o}=this.state;var i;const s=t.name.replace("acf/","");i=o&&o.current?e(o.current).parent():1==j(r)?n:n.parents(".acf-block-preview"),acf.doAction("render_block_preview",i,t),acf.doAction(`render_block_preview/type=${s}`,i,t)}componentDidRemount(){super.componentDidRemount(),acf.debug("Checking if fetch is required in BlockPreview componentDidRemount",Object.assign({},this.state.prevAttributes),Object.assign({},this.props.attributes),Object.assign({},this.state.prevContext),Object.assign({},this.props.context)),D(this.state.prevAttributes,this.props.attributes)&&D(this.state.prevContext,this.props.context)||(acf.debug("Triggering block preview fetch from componentDidRemount"),this.fetch()),this.renderBlockPreviewEvent()}}function G(e){return["top","center","bottom"].includes(e)?e:"top"}function Q(e){const t=acf.get("rtl")?"right":"left";return["left","center","right"].includes(e)?e:t}function K(e){if(e){const[t,r]=e.split(" ");return`${G(t)} ${Q(r)}`}return"center center"}function Z(e,t,r){return e[t]={type:r},e}function ee(e,t){return e._acf_context=te(t),s(JSON.stringify(te(e)))}function te(e){return Object.keys(e).sort().reduce(((t,r)=>(t[r]=e[r],t)),{})}acf.addAction("prepare",(function(){wp.blockEditor||(wp.blockEditor=wp.editor);const e=acf.get("blockTypes");e&&e.map(O)}))})(jQuery)})()})();