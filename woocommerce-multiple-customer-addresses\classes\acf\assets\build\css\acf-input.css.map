{"version": 3, "file": "acf-input.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;;;;8FAAA;AAMA;AAOA;AAQA;AAgBA;;;;8FAAA;ACrCA;;;;8FAAA;ACAA;;;;+FAAA;AAMC;EACC;AHmBF;;AGfA;;;;+FAAA;AAOC;EACC,cF0CS;AD1BX;;AGXA;;;;+FAAA;AAMA;;EACC;EACA;AHcD;;AGXA;;EACC;EACA;AHeD;;AGZA;;EACC;EACA;AHgBD;;AGIA;;;;+FAAA;AAQC;EACC;AHJF;AGOC;EACC;AHLF;AGQC;EACC;AHNF;AGSC;EACC;AHPF;AGUC;EACC;AHRF;AGWC;EACC;AHTF;AGYC;;;EACC;AHRF;AGWC;EACC;AHTF;;AGcA;;;;+FAAA;AAKA;EAEC,cF5DU;ADgDX;;AGeA;;;;+FAAA;AAOC;EACC;AHdF;AGiBC;EACC;AHfF;;AGoBA;;;;+FAAA;AASA;;;;+FAAA;AAMC;EACC;EACA;AHtBF;AGyBC;EACC;EACA;AHvBF;;AIlIA;;;;8FAAA;AAMA;;;EAGC;EACA;EACA;EACA;AJoID;;AIjIA;EACC;EAIA;AJiID;AI9HC;EACC;EACA;EACA;AJgIF;AI5HC;EACC;EACA;AJ8HF;AI5HE;EACC;EACA;EACA;EACA;AJ8HH;AI3HE;EACC;AJ6HH;AIxHC;EACC;AJ0HF;AItHC;EACC;EAEC;EAGD,cHTS;AD8HX;AIjHC;EACC;EACA;EACA;EACA;AJmHF;AIhHE;EACC;EACA;EACA;AJkHH;AI9GE;EACC;EACA;EACA;AJgHH;AI5GE;EACC;EACA;EACA;AJ8GH;AIzGU;;EAER;AJ2GF;;AItGA;EACC;EACA;EAwBA;;;;;;;GAAA;AJyFD;AI9GC;EACC;AJgHF;AI5GC;EACC;AJ8GF;AI5GE;EACC;EACA;AJ8GH;AIzGU;;EAER;AJ2GF;;AI5FA;EACC;EACA;AJ+FD;AI5FC;EACC;EACA;AJ8FF;;AI1FA;EACC;AJ6FD;;AI1FA;;;;8FAAA;AAMA;EACC;AJ4FD;AEnPC;EACC;EACA;EACA;AFqPF;AI3FC;EACC;EACA;AJ6FF;AIzFC;EACC;EACA;EACA;EAEC;EACA;EACA,yBHlIQ;AD4NX;AItFE;EACC;EACA;AJwFH;AInFU;EACR;AJqFF;;AIjFA;;;;8FAAA;AAMA;EACC;EACA;EACA;AJmFD;AIhFC;EACC;AJkFF;AI9EC;EACC;AJgFF;AI5EC;EACC;AJ8EF;;AI1EA;;;;8FAAA;AAMA;EACC;AJ4ED;AExSC;EACC;EACA;EACA;AF0SF;AI3EC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJ6EF;AIzEC;EACC;EACA;EACA;EACA;AJ2EF;AIvEC;EACC;EACA;EACA;EACA;AJyEF;AIrEC;EACC;EACA;EACA;EACA;AJuEF;AIjEE;EACC;EACA;EACA;AJmEH;AI/DE;EACC;AJiEH;AI7DE;EACC;AJ+DH;AIzDE;EACC;AJ2DH;AIzDE;EACC;EACA;AJ2DH;AIzDE;EACC;AJ2DH;AItDC;EAEC;IACC;EJuDD;EInDA;IACC;IACA;EJqDD;EIjDA;IACC;EJmDD;AACF;;AI/CA;AACA;EACC;EACA;AJkDD;AI/CC;EACC;AJiDF;AI7CC;EACC;AJ+CF;AI3CC;EACC;AJ6CF;;AIzCA;;;;8FAAA;AAQC;EACC,kBHlVG;EGmVH;EACA;EACA;AJyCF;AIrCC;EACC,kBH1VG;EG2VH;EACA;AJuCF;;AInCA;EACC;EACA;AJsCD;;AInCA;;;;8FAAA;AAMA;EACC;AJqCD;AIlCC;EACC;EACA;AJoCF;AIhCC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AJkCF;AIjCE;EACC;AJmCH;AI5BE;;EACC;AJ+BH;AIzBE;EACC;EACA;EACA;EACA;AJ2BH;AI1BG;EACC;AJ4BJ;AItBC;EACC;EACA;AJwBF;;AInBA;EACC;AJsBD;;AInBA;AACA;EACC;EACA;EACA;EAEA;EAOA;AJeD;AIrBC;;;EAGC;AJuBF;AInBC;EACC;EACA;EACA;AJqBF;AInBE;EACC;AJqBH;;AIhBA;AACA;EACC;EAKA;AJeD;AInBC;EACC;AJqBF;AIjBC;EAPD;IAQE;EJoBA;EInBA;IAEC;EJoBD;AACF;;AIhBA;;;;+EAAA;AAOC;;;;;;;;;;;;;;;EAeC;EACA;EACA;EACA;EACA;EACA;AJiBF;AEjeC;;;;;;;;;;;;;;;EEodE;AJ8BH;AI3BC;EACC;AJ6BF;;AIxBA;EACC;AJ2BD;;AIxBA;;;;+EAAA;AAKA;;;EAGC;AJ2BD;;AIxBA;;EAEC;EACA;EACA;EACA;EACA;EACA;AJ2BD;AE9gBC;;EEufC;EACA,qBH9fkB;EG+flB;AJ2BF;;AIvBA;EACC;EACA;EACA;AJ0BD;;AIvBA;EACC;EACA;EACA;AJ0BD;;AIvBA;EACC;EACA;AJ0BD;AIzBC;EACC;AJ2BF;AIzBC;EACC;AJ2BF;AIzBC;EACC;AJ2BF;;AIvBA;AACA;EACC;EACA;EACA;EAEA;AJyBD;;AItBA;EACC;EACA;EACA;EACA;AJyBD;;AItBA;EACC;AJyBD;;AItBA;EACC;AJyBD;;AItBA;EACC;AJyBD;;AItBA;;;;+EAAA;AAOC;EACC,qBHvkBgB;AD8lBlB;AEllBC;EE6jBE,qBH5kBc;ADomBjB;AIrBC;EACC;EACA;AJuBF;;AInBA;;;;+EAAA;AAOC;EACC;EACA;EACA;EACA;EACA;AJoBF;AIjBC;EACC;AJmBF;AIhBC;EACC;AJkBF;;AIdA;;;;+EAAA;AAMA;EACC;EA6DA;EAOA;AJlDD;AIhBC;EACC;EACA;EACA;EACA;AJkBF;AIhBE;EACC;EACA;EACA;EACA;EACA;EAEA;EAYA;AJMH;AIjBG;EACC;EACA;EACA;EACA;AJmBJ;AIjBI;EACC;AJmBL;AIdG;EACC;EACA;EACA;AJgBJ;AIZE;EACC;AJcH;AIXE;EACC;EACA;EACA;EACA;AJaH;AITC;EACC;AJWF;AITE;EACC;EACA;EACA;AJWH;AIRE;EACC;AJUH;AILC;EAEC;EACA;AJMF;AIFC;EACC;EACA;AJIF;;AIAA;AAEC;EACC;AJEF;AICC;EACC;AJCF;AIEC;EACC;EACA;AJAF;;AIIA;AACA;EACC;EAKA;AJLD;AICC;EACC;AJCF;AIGC;EAOC;AJPF;AICE;EACC;EACA;EACA;AJCH;AIIG;EACC;EACA;AJFJ;;AIQA;;;;+EAAA;AAOC;EACC;AJPF;AIYE;EACC;AJVH;AIeC;EACC,qBHzvBgB;AD4uBlB;AEhuBC;EEivBE;AJdH;AIsBE;EACC;AJpBH;AIqBG;EACC;AJnBJ;AIwBE;EACC;AJtBH;AI0BE;EACC;EACA;EACA;AJxBH;AI0BG;EACC;AJxBJ;AI6BE;EACC;EACA;EAGA;EACA;EACA;EACA;AJ7BH;AIgCG;EACC,mBHzwBO;EG0wBP,qBHzwBO;EG0wBP;EACA;AJ9BJ;AIgCI;EACC;AJ9BL;AImCG;EACC;EACA;EACA;AJjCJ;AIqCG;EACC,yBH5yBO;EG6yBP,qBH7yBO;EG8yBP;AJnCJ;AIwCE;EACC;EACA;AJtCH;AI2CC;EACC;AJzCF;AI0CE;EACC;AJxCH;;AI6CA;EACC;EACA;EACA;EACA,6CH3xBc;ADivBf;;AI6CA;EACC;AJ1CD;;AI6CA;EACC;EACA,cH30BU;ADiyBX;AI4CC;EACC,cHn0BS;ADyxBX;;AI8CA;EAEC;EACA;AJ5CD;;AIgDA;EACC;AJ7CD;;AIkDC;EACC;AJ/CF;AIkDE;EACC;EACA;AJhDH;;AIqDA;;;;+EAAA;AAOC;EACC;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;AJrDF;AIuDE;EACC;AJrDH;AI2DE;EACC;AJzDH;AI2DE;EACC;AJzDH;AI2DE;EACC;AJzDH;AI+DE;EACC;AJ7DH;;AIkEA;EACC;AJ/DD;;AIiEA;EACC;AJ9DD;;AIiEA;;;;+EAAA;AAMA;;EAEC;EACA;EACA;EACA;EACA;EAwCA;EAQA;AJ7GD;AI+DC;;EACC;EACA,kBH34BU;AD+0BZ;AI+DC;;EACC;EACA;EACA;EACA;EACA;EAkBA;AJ7EF;AI6DE;;EACC;AJ1DH;AI6DE;;;;EAEC;EACA;AJzDH;AI4DE;;EACC;EACA;EACA;AJzDH;AI6DE;;EACC;AJ1DH;AI6DE;;EACC;AJ1DH;AIgEE;;EACC;EACA;AJ7DH;AImEE;;;;EAEC;EACA;AJ/DH;;AIoEA;;;;+EAAA;AAMA;EACC;EA6BA;EAgCA;AJ7HD;AIkEC;EACC;EACA;EACA;EACA;EACA;EACA;AJhEF;AIkEE;EACC;EACA;EACA;EACA;AJhEH;AImEE;EACC;EACA;EACA;EACA;AJjEH;AIqEC;EACC;AJnEF;AIuEC;EACC;EACA;EACA;EACA;AJrEF;AIuEE;EACC;EACA;EACA;EACA;AJrEH;AIwEG;EACC;AJtEJ;AIuEI;EACC;AJrEL;AIwEG;EACC;AJtEJ;AIuEI;EACC;AJrEL;AIwEG;EACC;AJtEJ;AI4EC;EACC;EACA;EACA;AJ1EF;AI4EE;EACC;AJ1EH;AI6EG;EACC;AJ3EJ;AI6EG;EACC;AJ3EJ;AI6EG;EACC;AJ3EJ;AIkFE;EACC,qBHvlCc;ADugCjB;AIiFG;EACC;AJ/EJ;AIiFG;EACC;AJ/EJ;;AIsFC;EACC;EACA;EACA;EACA;EACA,kBH/iCU;EGgjCV,6CH3iCa;ADw9Bf;AIqFE;EACC;EACA;EACA;EACA;EACA;EACA,cHzlCQ;EG0lCR;AJnFH;AIqFG;EACC,cHllCO;AD+/BX;AIsFG;EACC,mBHvmCO;EGwmCP,cHvlCO;ADmgCX;AI2FG;EACC;EACA;EAEC;EACA;EAED;EAEC;EACA;EACA;EACA;EAED,yBHjnCO;EGknCP,qBHhnCO;EGinCP,cH9mCO;ADihCX;AI+FI;EACC;EACA;EACA;EAEC;EACA;EAED,cH1nCM;EG2nCN;EACA;EACA;AJ/FL;AIiGK;EACC,cH9nCK;AD+hCX;AIkGK;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJjGN;;AIyGA;;;;+EAAA;AAOC;EACC;AJxGF;;AI4GA;;;;+EAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EA+CA;EAWA;EAiBA;AJlLD;AIyGC;EACC;EACA;EACA;EAEA;EACA;EAEA;EACA;AJzGF;AI2GE;EACC;AJzGH;AI6GC;EACC;EACA;EACA;AJ3GF;AI8GC;EACC;AJ5GF;AI+GC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;AJ/GF;AImHC;EAEC;EACA;EACA;AJlHF;AImHE;EACC;AJjHH;AIsHC;EACC;EACA;EAQA;AJ3HF;AIqHE;EACC;EACA;EACA;AJnHH;AIuHE;EACC;AJrHH;AI0HC;EACC;AJxHF;AErqCC;EEkyCC,qBHjzCe;ADurCjB;AI2HE;EACC,qBHnzCc;AD0rCjB;AI4HE;EAEC;AJ3HH;AI4HG;EACC;AJ1HJ;AI8HE;EACC;AJ5HH;AI6HG;EACC;AJ3HJ;AI6HG;EACC;AJ3HJ;;AIiIA;AACA;EACC;EACA;EACA;AJ9HD;;AIiIA;EACC;AJ9HD;AIgIC;EACC;EACA;AJ9HF;;AIoIC;EACC;EACA;EACA;AJjIF;;AIqIA;AAEC;EACC;EACA;EACA;AJnIF;;AIuIA;;;;2EAAA;AAMA;EACC;EACA;EACA;AJrID;AIuIC;EACC;EACA;AJrIF;AIuIE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJrIH;AIwIE;EACC;EACA;EACA;EACA;AJtIH;AI0IE;EACC;AJxIH;AI4IC;EACC;AJ1IF;AI8IC;EACC;AJ5IF;AIiJE;EACC;AJ/IH;AIiJE;;EAEC;AJ/IH;AIqJE;EACC;AJnJH;AIqJE;EACC;AJnJH;AIqJE;EACC;AJnJH;AIyJE;EACC;AJvJH;AIyJE;;EAEC;AJvJH;AI2JE;EACC;AJzJH;AI6JE;EACC;AJ3JH;AIiKE;EACC;AJ/JH;AIiKE;EACC;AJ/JH;;AIoKA;AACA;EACC;EACA;AJjKD;;AIoKA;EACC;AJjKD;;AIoKA;EACC;AJjKD;;AImKA;EACC;EACA;AJhKD;;AImKA;EACC;AJhKD;;AImKA;;;;2EAAA;AAMA;EACC;EACA;EAuDA;EAkGA;AJxTD;AIkKC;EAEC;EACA;EAiCA;AJjMF;AE92CC;EACC;EACA;EACA;AFg3CF;AI6JE;EACC;EACA;EACA;EACA;EACA;EACA;AJ3JH;AI4JG;EACC;AJ1JJ;AI8JG;;EAEC;EACA;AJ5JJ;AI8JI;;;EAEC;EACA;AJ3JL;AI8JG;EACC;EACA;EACA;EACA;AJ5JJ;AIkKG;EACC;AJhKJ;AIoKG;EACC;AJlKJ;AIoKG;EACC;AJlKJ;AIwKC;EACC;EACA;EACA;EACA;AJtKF;AIwKE;;;EAGC;EACA;EACA;EACA;EACA;AJtKH;AIyKE;EACC;AJvKH;AI0KE;EACC;EA+BA;EAcA;AJnNH;AIwKG;EACC;EACA;AJtKJ;AIyKG;EACC;EACA;EACA;EACA;EACA;AJvKJ;AIyKI;EACC;EACA;EACA;EACA;AJvKL;AI0KI;EACC;AJxKL;AI0KK;EACC;EACA;AJxKN;AI8KG;EACC;EACA;AJ5KJ;AI8KI;EACC;AJ5KL;AI8KK;EACC;AJ5KN;AIkLG;EACC;AJhLJ;AIkLI;EACC;EACA;EACA;AJhLL;AIkLK;EACC;AJhLN;AIkLM;EACC;AJhLP;AIuLE;EACC;AJrLH;AIuLG;;;EAGC;AJrLJ;AI2LC;EAEC;EASA;EASA;AJ1MF;AE5+CC;EACC;EACA;EACA;AF8+CF;AIqLE;;EAEC;EACA;EACA;AJnLH;AIuLE;EACC;AJrLH;AIuLG;EACC;AJrLJ;AI2LG;EACC;EACA;EACA;EACA;EAEA;AJ1LJ;AI2LI;EACC;EACA;AJzLL;AI6LG;EACC;AJ3LJ;AI8LG;EACC;AJ5LJ;AI8LI;EACC;AJ5LL;;AImMA;AAGE;EACC;AJlMH;AIqME;EACC;AJnMH;;AIwMA;;;;2EAAA;AASE;EACC;EACA;EACA;EACA;EACA;EACA;AJzMH;AI4ME;EACC;EACA;EACA;AJ1MH;AI8MC;EACC;AJ5MF;AI+MC;EACC;EACA;AJ7MF;AIgNC;EACC;AJ9MF;AIiNC;EACC,qBHpvDe;EGqvDf;AJ/MF;;AIoNA;EACC;AJjND;;AIoNA;;;;+EAAA;AAMA;EACC;AJlND;;AIsNA;EACC;AJnND;;AIuNA;EACC;EACA;EACA;AJpND;;AIwNA;EACC;EACA;AJrND;AIuNC;EACC;AJrNF;AIuNE;EACC;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;AJvNH;AIyNG;EACC;AJvNJ;AI0NG;EACC;EACA;AJxNJ;AI2NG;EACC;AJzNJ;AI8NE;EACC;AJ5NH;AIgOE;EACC;EACA;EACA;EACA;EACA;EACA;AJ9NH;;AIoOA;EACC;AJjOD;AIoOC;EACC;EACA;EACA;EAGA;EACA;AJpOF;AEnmDC;EEw1DE,qBHh2DkB;AD8mDrB;;AIgQC;EACC;EAEA;EAKA;AJlQF;AI8PE;EAJD;IAKE;EJ3PD;AACF;AI8PE;EACC;EACA;EAEA;AJ7PH;AI8PG;EALD;IAME;EJ3PF;AACF;;AImQC;EACC;EACA;EACA;EACA;EACA;EACA;AJhQF;AImQE;EACC;EACA;AJjQH;AImQG;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJjQJ;AImQI;EACC;AJjQL;AIqQG;EACC;EACA;EACA;EACA;AJnQJ;AIwQE;EACC;EACA;AJtQH;AIwQG;EACC;EACA;AJtQJ;AI4QC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJ1QF;AIgRG;EACC;AJ9QJ;;AIoRA;AACA;EACC;EACA;EAEA;EAcA;AJ/RD;AIkRC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJhRF;AIoRC;EACC;AJlRF;AIoRE;EACC;EACA;EACA;EACA;AJlRH;AIuRC;EACC;EAEA;AJtRF;AIuRE;EACC;AJrRH;AIwRE;EACC;EACA;EACA;AJtRH;AIyRE;EACC;AJvRH;AIyRG;EACC;AJvRJ;AI0RG;EACC;AJxRJ;AI8RC;EACC;AJ5RF;;AIiSA;EACC;AJ9RD;AIiSC;EACC;EACA;EACA;EACA;AJ/RF;AIiSE;EACC;AJ/RH;AIiSG;EACC;AJ/RJ;AImSE;EACC;AJjSH;;AIsSA;AAGC;EACC;AJrSF;AIuSE;EACC;AJrSH;AI0SC;EACC;EACA;EACA,kBHrkEG;EGskEH,mBHtkEG;AD8xDL;AI0SE;EACC;EACA,qBHnkEc;AD2xDjB;AI0SG;EACC;EACA,qBHvkEa;AD+xDjB;AI0SI;EACC;AJxSL;AI4SG;EACC;AJ1SJ;AIiTE;EACC;EACA;AJ/SH;AIkTE;EACC;AJhTH;AIkTG;EACC;EACA;EACA;AJhTJ;AImTG;EACC;AJjTJ;;AI2TE;;EACC;AJvTH;AIyTE;;;EAEC;AJtTH;;AI2TA;EACC;AJxTD;;AI2TA;AACA;EACC;AJxTD;;AI2TA;EACC;EACA;AJxTD;;AI2TA;EACC;AJxTD;;AI2TA;AACA;EACC;AJxTD;;AI2TA;EACC;AJxTD;;AI2TA;EACC;AJxTD;;AI2TA;AACA;EAKC;EACA;AJ5TD;;AI+TA;AAEA;EACC;AJ7TD;;AIgUA;AACA;EACC;AJ7TD;;AIgUA;;;;8FAAA;AAMA;EACC;EACA;EACA;AJ9TD;AIgUC;EACC;EACA;EACA;AJ9TF;AIgUE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJ9TH;AIiUE;EACC;AJ/TH;AImUC;EACC;EACA;EACA;AJjUF;AImUE;EACC;EACA;AJjUH;AIoUE;EACC;EACA;EACA;EACA;AJlUH;AIqUE;EFtuED;EACA;EACA;EACA;EEquEE;EAEA;EACA;EACA;EACA;AJjUH;AIoUE;EACC;AJlUH;AIqUE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AJnUH;AIqUG;EACC;EACA;EACA;AJnUJ;AI0UE;EACC;AJxUH;AI2UE;EACC;AJzUH;AI4UE;EACC;AJ1UH;;AI+UA;;;;8FAAA;AAMA;EAEC;EAMA;EA8BA;EAKA;AJpXD;AEh+DC;EACC;EACA;EACA;AFk+DF;AIwUC;EACC;AJtUF;AI0UC;EACC;EACA;EAqBA;AJ5VF;AIyUE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;AJxUH;AIyUG;EACC;EACA;AJvUJ;AI4UE;EACC;AJ1UH;AI+UC;EACC;AJ7UF;AIkVE;EACC;AJhVH;;AIqVA;;;;8FAAA;AAMA;EACC;EA8CA;EAKA;AJpYD;AImVC;EACC;AJjVF;AIoVC;EACC;EACA;EACA;EACA;AJlVF;AIqVC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AJnVF;AIqVE;EACC;EACA;EACA;EACA;AJnVH;AIuVC;EACC;EACA;AJrVF;AIuVE;EACC;EACA;EACA;EACA;AJrVH;AIwVE;EACC;AJtVH;AI2VC;EACC;AJzVF;AI8VE;EACC;EACA;EACA;EACA;AJ5VH;AI+VE;EACC;EACA;AJ7VH;;AIkWA;;;;+EAAA;AAMA;EACC;AJhWD;AIkWC;EACC;EACA;AJhWF;;AIoWA;AACA;EACC;EACA;EACA;EACA;AJjWD;;AIoWA;EACC;EACA;EACA;EACA;AJjWD;;AIoWA;;;;+EAAA;AAMA;EAaC;AJ9WD;AIkWC;EACC;AJhWF;AIkWE;EACC;AJhWH;AIoWC;EACC;AJlWF;AIsWC;EACC;EACA;EACA;EACA;EACA;AJpWF;;AIwWA;;;;+EAAA;AAMA;EACC;EAkBA;EAOA;AJ7XD;AIsWC;EACC;EACA;EACA;EACA;AJpWF;AIuWC;EACC;EACA;AJrWF;AIuWE;EACC;AJrWH;AI2WE;EACC;AJzWH;AI+WE;EACC;EACA;AJ7WH;;AIkXA;;;;+EAAA;AAMA;EAiCC;AJhZD;AIgXC;;EAEC;EACA;EACA;EACA;AJ9WF;AIiXC;EACC;AJ/WF;AIkXC;EACC;EACA;EACA;EACA;EACA;AJhXF;AIkXE;EACC;AJhXH;AIoXC;EACC;EACA;EACA;EACA;EACA;AJlXF;AIuXE;EACC;EACA;AJrXH;AIwXE;EACC;AJtXH;AIwXE;EACC;AJtXH;;AI2XA;;;;+EAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA;AJzXD;AI4XC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AJ1XF;AI4XE;EACC;AJ1XH;AI6XE;EACC;EACA;EACA;EACA;AJ3XH;AI8XE;EACC;AJ5XH;AI+XE;EACC;AJ7XH;AIiYE;EACC;EACA;EACA;EACA;EACA;EACA;AJ/XH;AImYC;EACC;EACA;EACA;AJjYF;AIsYE;EACC;AJpYH;;AI0YA;EACC;EACA;EACA,qBHpnFkB;AD6uEnB;AIyYC;EACC;EACA;EACA;EACA;AJvYF;AI0YC;EACC;EACA;EACA;AJxYF;AI0YE;EACC;AJxYH;AI0YG;EACC;AJxYJ;;AI8YA;AAEC;EACC;AJ5YF;AI+YC;EACC;EACA;EACA;EACA;AJ7YF;AIgZC;EACC;AJ9YF;;AIkZA;AACA;EACC;EACA;AJ/YD;AIiZC;EACC;AJ/YF;;AImZA;AACA;EACC;AJhZD;AIkZC;EACC;EACA;AJhZF;AImZC;EACC;AJjZF;;AIqZA;AACA;EACC;EACA;AJlZD;;AIqZA;EACC;EACA;AJlZD;AIoZC;EACC;AJlZF;;AIsZA;AACA;EACC;AJnZD;AIqZC;EACC;AJnZF;;AIuZA;AACA;EACC,iBH5tFiB;EG6tFjB,kBH7tFiB;EG8tFjB;AJpZD;;AIuZA;AAIA;AACA;;;;;;;;;CAAA;AAWA;AACA;EACC;EACA;AJxZD;AI0ZC;EACC;AJxZF;AI2ZC;EACC;AJzZF;;AIgaC;EACC;AJ7ZF;AIiaC;EACC;AJ/ZF;AImaC;EACC;AJjaF;;AIqaA;;;;+EAAA;AAUG;;EAEC;AJvaJ;AI0aI;;EAEC;AJxaL;AI8aE;EACC;EACA;AJ5aH;AI8aG;EACC;EACA;EACA;EACA;EAGA;EACA;EACA;AJ9aJ;AIibI;EACC;AJ/aL;AIgbK;EACC;AJ9aN;AImbI;EACC;EACA;EACA;AJjbL;AImbK;EACC;AJjbN;AIobK;EACC;EACA;AJlbN;AImbM;EACC;EACA;AJjbP;AIobM;EACC;AJlbP;AIubM;EACC;AJrbP;AI8bG;EACC;EACA;EACA;EACA;AJ5bJ;AI8bG;EACC;AJ5bJ;AI8bG;EACC;EACA;EACA;AJ5bJ;AIgcG;EACC;EACA;AJ9bJ;AIicG;EACC;AJ/bJ;;AIqcA;;;;+EAAA;AAOC;;EACC;EAEC;EACA;AJpcH;AIucE;;EAEE;EACA;EACA;EACA;EAED,yBHp3FQ;EGq3FR;EAEA,cHn3FQ;AD46EX;;AI4cA;;;;+EAAA;AAMA;EACC;AJ1cD;;AI6cA;EACC;AJ1cD;;AI6cA;EACC;EACA;AJ1cD;;AI6cA;EACC;AJ1cD;;AKn/EA;;;;8FAAA;AAMA;EAEC;EAkCA;EAYA;ALw8ED;AKr/EC;EAEC;EAkBA;ALq+EF;AKt/EE;EACC;EACG;EAEA;ALu/EN;AKt/EG;EACC;ALw/EJ;AKr/EM;EACF;EACA;ALu/EJ;AKh/EE;EACC;EAEA;ALi/EH;AKh/EG;EACC;ALk/EJ;AK1+EC;EACC;EAEA;AL2+EF;AK1+EE;EACC;AL4+EH;AKr+EC;EACC;ALu+EF;;AKl+EA;;EAGC;EAgBA;ALq9ED;AKp+EC;;;;;;;;;;;;;;EAOI;AL6+EL;AK1+EC;;EACC;AL6+EF;AKx+EC;;;;;;;;;;;;;;;;EAQI;ALk/EL;;AK5+EC;EACC;AL++EF;AK5+EC;EACC;EAWF;;;;;;;;GAAA;AL4+EA;AKr/EE;EACC;ALu/EH;AKr/EG;EACC;EACA;ALu/EJ;AKx+EC;EACC;AL0+EF;;AKr+EA;;;;8FAAA;AAOA;EACC;ALs+ED;AKl+EE;EACC;ALo+EH;AKl+EG;EACC;EACA;ALo+EJ;;AK79EA;;EAEC;EACA;EACA;ALg+ED;;AKz9EC;EACC;AL49EF;AK19EE;EACC;AL49EH;AKz9EE;EACC;EACA;EACA;AL29EH;AKx9EE;EACC;AL09EH;;AKr9EA;EACC;ALw9ED;AKp9EE;EACC;ALs9EH;;AKh9EA;;;;8FAAA;AAMA;EACI;EACA;ALk9EJ;;AK98EA;;;;8FAAA;AAMA;EACC;EACA;ALg9ED;;AKz8EE;EACC;AL48EH;AK18EG;EACC;EACA;AL48EJ;;AKt8EA;;;;8FAAA;AAMA;EACC;EACG;ALw8EJ;AKr8EC;EACC;EACA;ALu8EF;AKr8EE;EAAO;ALw8ET;AKp8EC;EACC;EACA;ALs8EF;;AKl8EA;EACC;EACA;ALq8ED;AKn8EC;EACC;EACA;ALq8EF;AKn8EE;EACC;ALq8EH;AKp8EG;EACC;EACA;ALs8EJ;;AKh8EA;;;;+FAAA;AAQC;EACC;ALg8EF;AK77EC;;;;;EAKC;AL+7EF;AK57EC;EACC;AL87EF;AK57EE;EACC;AL87EH;AK57EG;EACC;EACA;AL87EJ;AK57EI;EACC;AL87EL;AKz7EE;EACC;AL27EH;;AMnvFA;;;;+FAAA;AAMA;AAGC;EACC;EACA;ANmvFF;AMjvFE;EACC;ANmvFH;AMhvFE;EACC;ANkvFH;AM/uFE;EACC;ANivFH;;AMzuFA;AACA;EACC;AN4uFD;AM1uFC;EACC;EACA;EACA;EACA;EACG;EACA;EACA;AN4uFL;AM1uFK;EACC;EACH;EACA;EACG;EACA;AN4uFN;AMxuFC;EACC;EACA;EACA;EACG;EACA;AN0uFL;AMvuFC;EACC;ANyuFF;;AMpuFA;AACA;EACC;EACG;EACA;EACA;EACA;ANuuFJ;AMruFI;EACF;EACG;EACA;EACA;EACA;EACA;ANuuFL;AMpuFC;EACC;EACG;EACA;EACA;EACA;ANsuFL;;AMjuFA;AAGC;EACC;ANkuFF;AM/tFC;EACC;EACA;EACA;ANiuFF;;AM3tFA;AACA;EAEC;EAOA;EAMA;EASA;EAUA;ANisFD;AMhuFC;;EAEC;ANkuFF;AM7tFC;EACC;AN+tFF;AM1tFC;EACC;EACA;EACA;EACA;AN4tFF;AMrtFE;EACC;ANutFH;AMhtFC;EAnCD;IAqCE;IAWA;ENwsFA;EMltFA;;IAEC;IACA;IACA;IACA;IACA;ENotFD;EM/sFA;;;IAGC;IACG;IACA;IACA;ENitFJ;AACF;;AMxsFA;;;;+FAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN0sFD;AMvsFC;EACC;EACA;EACA;ANysFF;AMvsFC;EACC;ANysFF;AMrsFC;EAAW;ANwsFZ;AMvsFC;EAAa;AN0sFd;AMvsFC;EAzBD;IA0BE;EN0sFA;AACF;;AMtsFA;AACA;EAEC;ANwsFD;AMtsFE;EAAW;ANysFb;AMxsFE;EAAa;AN2sFf;AMtsFC;;EACoC;ANysFrC;AMxsFC;EAAiB;AN2sFlB;AMpsFG;EACC;EACA;ANssFJ;AMpsFI;EACC;EACA;ANssFL;AMlsFG;EACC;ANosFJ;AM/rFE;;;EAGC;EACA;ANisFH;AM7rFE;;;;;EAKC;AN+rFH;AM1rFC;EAGC;IAAsC;EN2rFtC;EM1rFA;IAAe;EN6rFf;EM5rFA;IAAiB;IAAa;IAA4B;ENisF1D;EM1rFE;IACC;IACA;IACA;EN4rFH;EMzrFE;IACC;IACA;IACA;EN2rFH;AACF;AMprFC;EAOG;IACC;ENgrFH;AACF;;AMxqFA;;;;+FAAA;AAMA;EAEC;ANyqFD;AMvqFE;;EAEC;ANyqFH;;AMnqFA;;;;+FAAA;AAaA;;;;+FAAA;AAMA;EAEC;EACA;EACA;EACA;EACA;EAGA;EASA;EAWA;EAMA;EAOA;EA4DA;EASA;AN0jFD;AM/pFC;;;;EAII;ANiqFL;AM5pFC;;;;EAIC;EACA;EACA;AN8pFF;AMzpFC;EACI;AN2pFL;AMtpFC;EACI;EACA;ANwpFL;AMnpFC;EAEC;EAEA;EAmCA;EAcA;ANomFF;AMppFE;EAEC;EAEA;EAMA;EAQA;ANwoFH;AMrpFG;EACC;ANupFJ;AMlpFG;EACC;EACA;EACA;EACA;ANopFJ;AMhpFG;EACC;ANkpFJ;AM/oFG;EACC;ANipFJ;AM/oFI;EACC;ANipFL;AMvoFG;EACC;ANyoFJ;AMvoFI;EACC;ANyoFL;AMjoFE;EAA6B;ANooF/B;AM9nFC;EAvGD;IAwGE;IACA;IACA;IACA;ENioFA;AACF;AM7nFC;EAhHD;IAiHE;IACA;IACA;IACA;ENgoFA;AACF;AM9nFC;EACC;IACI;ENgoFJ;AACF;;AOtlGA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;APwlGD;AOvlGC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;APylGF;;AOplGA;EACC;APulGD;AOplGC;EACC;APslGF;AOrlGE;EACC;APulGH;;AOjlGA;EACC;APolGD;;AOhlGA;EACC;APmlGD;AOllGC;EACC;EACA;APolGF;;AOjlGA;EACC;EACA;APolGD;AOnlGC;EACC;APqlGF;;AQtoGC;EACC;ARyoGF;AQxoGE;EACC;AR0oGH;AQpoGE;EACC;ARsoGH;AQjoGC;EACC;ARmoGF;AQhoGG;EACC;ARkoGJ;AQjoGI;EACC;ARmoGL;AQ/nGI;;EAEC;ARioGL;AQ7nGI;EACC;EACA;AR+nGL;AQ5nGG;EACC;AR8nGJ;AQznGE;EACC;AR2nGH;AQznGE;EACC;AR2nGH;AQtnGC;EACC;ARwnGF;;AQnnGA;EACC;EACA;EACA;EACA;ARsnGD,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/acf-input.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_variables.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_mixins.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_typography.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_fields.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_forms.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_media.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_input.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_postbox.scss"], "sourcesContent": ["@charset \"UTF-8\";\n/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n/* colors */\n/* acf-field */\n/* responsive */\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page #wpcontent {\n  line-height: 140%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page a {\n  color: #0783BE;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-h1, .acf-admin-page h1,\n.acf-headerbar h1 {\n  font-size: 21px;\n  font-weight: 400;\n}\n\n.acf-h2, .acf-page-title, .acf-admin-page h2,\n.acf-headerbar h2 {\n  font-size: 18px;\n  font-weight: 400;\n}\n\n.acf-h3, .acf-admin-page h3,\n.acf-headerbar h3 {\n  font-size: 16px;\n  font-weight: 400;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page .p1 {\n  font-size: 15px;\n}\n.acf-admin-page .p2 {\n  font-size: 14px;\n}\n.acf-admin-page .p3 {\n  font-size: 13.5px;\n}\n.acf-admin-page .p4 {\n  font-size: 13px;\n}\n.acf-admin-page .p5 {\n  font-size: 12.5px;\n}\n.acf-admin-page .p6, .acf-admin-page .acf-field p.description, .acf-field .acf-admin-page p.description, .acf-admin-page .acf-small {\n  font-size: 12px;\n}\n.acf-admin-page .p7, .acf-admin-page .acf-field-setting-prefix_label p.description code, .acf-field-setting-prefix_label p.description .acf-admin-page code,\n.acf-admin-page .acf-field-setting-prefix_name p.description code,\n.acf-field-setting-prefix_name p.description .acf-admin-page code {\n  font-size: 11.5px;\n}\n.acf-admin-page .p8 {\n  font-size: 11px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n  color: #344054;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page .acf-settings-wrap h1 {\n  display: none !important;\n}\n.acf-admin-page #acf-admin-tools h1:not(.acf-field-group-pro-features-title, .acf-field-group-pro-features-title-sm) {\n  display: none !important;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page a:focus {\n  box-shadow: none;\n  outline: none;\n}\n.acf-admin-page a:focus-visible {\n  box-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgba(79, 148, 212, 0.8);\n  outline: 1px solid transparent;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-field\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-field,\n.acf-field .acf-label,\n.acf-field .acf-input {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  position: relative;\n}\n\n.acf-field {\n  margin: 15px 0;\n  clear: both;\n}\n.acf-field p.description {\n  display: block;\n  margin: 0;\n  padding: 0;\n}\n.acf-field .acf-label {\n  vertical-align: top;\n  margin: 0 0 10px;\n}\n.acf-field .acf-label label {\n  display: block;\n  font-weight: 500;\n  margin: 0 0 3px;\n  padding: 0;\n}\n.acf-field .acf-label:empty {\n  margin-bottom: 0;\n}\n.acf-field .acf-input {\n  vertical-align: top;\n}\n.acf-field p.description {\n  display: block;\n  margin-top: 6px;\n  color: #667085;\n}\n.acf-field .acf-notice {\n  margin: 0 0 15px;\n  background: #edf2ff;\n  color: #0c6ca0;\n  border-color: #2183b9;\n}\n.acf-field .acf-notice.-error {\n  background: #ffe6e6;\n  color: #cc2727;\n  border-color: #d12626;\n}\n.acf-field .acf-notice.-success {\n  background: #eefbe8;\n  color: #0e7b17;\n  border-color: #32a23b;\n}\n.acf-field .acf-notice.-warning {\n  background: #fff3e6;\n  color: #bd4b0e;\n  border-color: #d16226;\n}\ntd.acf-field,\ntr.acf-field {\n  margin: 0;\n}\n\n.acf-field[data-width] {\n  float: left;\n  clear: none;\n  /*\n  \t@media screen and (max-width: $sm) {\n  \t\tfloat: none;\n  \t\twidth: auto;\n  \t\tborder-left-width: 0;\n  \t\tborder-right-width: 0;\n  \t}\n  */\n}\n.acf-field[data-width] + .acf-field[data-width] {\n  border-left: 1px solid #eeeeee;\n}\nhtml[dir=rtl] .acf-field[data-width] {\n  float: right;\n}\nhtml[dir=rtl] .acf-field[data-width] + .acf-field[data-width] {\n  border-left: none;\n  border-right: 1px solid #eeeeee;\n}\ntd.acf-field[data-width],\ntr.acf-field[data-width] {\n  float: none;\n}\n\n.acf-field.-c0 {\n  clear: both;\n  border-left-width: 0 !important;\n}\nhtml[dir=rtl] .acf-field.-c0 {\n  border-left-width: 1px !important;\n  border-right-width: 0 !important;\n}\n\n.acf-field.-r0 {\n  border-top-width: 0 !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-fields\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-fields {\n  position: relative;\n}\n.acf-fields:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.acf-fields.-border {\n  border: #ccd0d4 solid 1px;\n  background: #fff;\n}\n.acf-fields > .acf-field {\n  position: relative;\n  margin: 0;\n  padding: 16px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.acf-fields > .acf-field:first-child {\n  border-top: none;\n  margin-top: 0;\n}\ntd.acf-fields {\n  padding: 0 !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-fields (clear)\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-fields.-clear > .acf-field {\n  border: none;\n  padding: 0;\n  margin: 15px 0;\n}\n.acf-fields.-clear > .acf-field[data-width] {\n  border: none !important;\n}\n.acf-fields.-clear > .acf-field > .acf-label {\n  padding: 0;\n}\n.acf-fields.-clear > .acf-field > .acf-input {\n  padding: 0;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-fields (left)\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-fields.-left > .acf-field {\n  padding: 15px 0;\n}\n.acf-fields.-left > .acf-field:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.acf-fields.-left > .acf-field:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  z-index: 0;\n  background: #f9f9f9;\n  border-color: #e1e1e1;\n  border-style: solid;\n  border-width: 0 1px 0 0;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  width: 20%;\n}\n.acf-fields.-left > .acf-field[data-width] {\n  float: none;\n  width: auto !important;\n  border-left-width: 0 !important;\n  border-right-width: 0 !important;\n}\n.acf-fields.-left > .acf-field > .acf-label {\n  float: left;\n  width: 20%;\n  margin: 0;\n  padding: 0 12px;\n}\n.acf-fields.-left > .acf-field > .acf-input {\n  float: left;\n  width: 80%;\n  margin: 0;\n  padding: 0 12px;\n}\nhtml[dir=rtl] .acf-fields.-left > .acf-field:before {\n  border-width: 0 0 0 1px;\n  left: auto;\n  right: 0;\n}\nhtml[dir=rtl] .acf-fields.-left > .acf-field > .acf-label {\n  float: right;\n}\nhtml[dir=rtl] .acf-fields.-left > .acf-field > .acf-input {\n  float: right;\n}\n#side-sortables .acf-fields.-left > .acf-field:before {\n  display: none;\n}\n#side-sortables .acf-fields.-left > .acf-field > .acf-label {\n  width: 100%;\n  margin-bottom: 10px;\n}\n#side-sortables .acf-fields.-left > .acf-field > .acf-input {\n  width: 100%;\n}\n@media screen and (max-width: 640px) {\n  .acf-fields.-left > .acf-field:before {\n    display: none;\n  }\n  .acf-fields.-left > .acf-field > .acf-label {\n    width: 100%;\n    margin-bottom: 10px;\n  }\n  .acf-fields.-left > .acf-field > .acf-input {\n    width: 100%;\n  }\n}\n\n/* clear + left */\n.acf-fields.-clear.-left > .acf-field {\n  padding: 0;\n  border: none;\n}\n.acf-fields.-clear.-left > .acf-field:before {\n  display: none;\n}\n.acf-fields.-clear.-left > .acf-field > .acf-label {\n  padding: 0;\n}\n.acf-fields.-clear.-left > .acf-field > .acf-input {\n  padding: 0;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-table\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-table tr.acf-field > td.acf-label {\n  padding: 15px 12px;\n  margin: 0;\n  background: #f9f9f9;\n  width: 20%;\n}\n.acf-table tr.acf-field > td.acf-input {\n  padding: 15px 12px;\n  margin: 0;\n  border-left-color: #e1e1e1;\n}\n\n.acf-sortable-tr-helper {\n  position: relative !important;\n  display: table-row !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-postbox\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-postbox {\n  position: relative;\n}\n.acf-postbox > .inside {\n  margin: 0 !important; /* override WP style - do not delete - you have tried this before */\n  padding: 0 !important; /* override WP style - do not delete - you have tried this before */\n}\n.acf-postbox .acf-hndle-cog {\n  color: #72777c;\n  font-size: 16px;\n  line-height: 36px;\n  height: 36px;\n  width: 1.62rem;\n  position: relative;\n  display: none;\n}\n.acf-postbox .acf-hndle-cog:hover {\n  color: #191e23;\n}\n.acf-postbox > .hndle:hover .acf-hndle-cog,\n.acf-postbox > .postbox-header:hover .acf-hndle-cog {\n  display: inline-block;\n}\n.acf-postbox > .hndle .acf-hndle-cog {\n  height: 20px;\n  line-height: 20px;\n  float: right;\n  width: auto;\n}\n.acf-postbox > .hndle .acf-hndle-cog:hover {\n  color: #777777;\n}\n.acf-postbox .acf-replace-with-fields {\n  padding: 15px;\n  text-align: center;\n}\n\n#post-body-content #acf_after_title-sortables {\n  margin: 20px 0 -20px;\n}\n\n/* seamless */\n.acf-postbox.seamless {\n  border: 0 none;\n  background: transparent;\n  box-shadow: none;\n  /* hide hndle */\n  /* inside */\n}\n.acf-postbox.seamless > .postbox-header,\n.acf-postbox.seamless > .hndle,\n.acf-postbox.seamless > .handlediv {\n  display: none !important;\n}\n.acf-postbox.seamless > .inside {\n  display: block !important; /* stop metabox from hiding when closed */\n  margin-left: -12px !important;\n  margin-right: -12px !important;\n}\n.acf-postbox.seamless > .inside > .acf-field {\n  border-color: transparent;\n}\n\n/* seamless (left) */\n.acf-postbox.seamless > .acf-fields.-left {\n  /* hide sidebar bg */\n  /* mobile */\n}\n.acf-postbox.seamless > .acf-fields.-left > .acf-field:before {\n  display: none;\n}\n@media screen and (max-width: 782px) {\n  .acf-postbox.seamless > .acf-fields.-left {\n    /* remove padding */\n  }\n  .acf-postbox.seamless > .acf-fields.-left > .acf-field > .acf-label, .acf-postbox.seamless > .acf-fields.-left > .acf-field > .acf-input {\n    padding: 0;\n  }\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Inputs\n*\n*-----------------------------------------------------------------------------*/\n.acf-field input[type=text],\n.acf-field input[type=password],\n.acf-field input[type=date],\n.acf-field input[type=datetime],\n.acf-field input[type=datetime-local],\n.acf-field input[type=email],\n.acf-field input[type=month],\n.acf-field input[type=number],\n.acf-field input[type=search],\n.acf-field input[type=tel],\n.acf-field input[type=time],\n.acf-field input[type=url],\n.acf-field input[type=week],\n.acf-field textarea,\n.acf-field select {\n  width: 100%;\n  padding: 4px 8px;\n  margin: 0;\n  box-sizing: border-box;\n  font-size: 14px;\n  line-height: 1.4;\n}\n.acf-admin-3-8 .acf-field input[type=text],\n.acf-admin-3-8 .acf-field input[type=password],\n.acf-admin-3-8 .acf-field input[type=date],\n.acf-admin-3-8 .acf-field input[type=datetime],\n.acf-admin-3-8 .acf-field input[type=datetime-local],\n.acf-admin-3-8 .acf-field input[type=email],\n.acf-admin-3-8 .acf-field input[type=month],\n.acf-admin-3-8 .acf-field input[type=number],\n.acf-admin-3-8 .acf-field input[type=search],\n.acf-admin-3-8 .acf-field input[type=tel],\n.acf-admin-3-8 .acf-field input[type=time],\n.acf-admin-3-8 .acf-field input[type=url],\n.acf-admin-3-8 .acf-field input[type=week],\n.acf-admin-3-8 .acf-field textarea,\n.acf-admin-3-8 .acf-field select {\n  padding: 3px 5px;\n}\n.acf-field textarea {\n  resize: vertical;\n}\n\nbody.acf-browser-firefox .acf-field select {\n  padding: 4px 5px;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Text\n*\n*-----------------------------------------------------------------------------*/\n.acf-input-prepend,\n.acf-input-append,\n.acf-input-wrap {\n  box-sizing: border-box;\n}\n\n.acf-input-prepend,\n.acf-input-append {\n  font-size: 13px;\n  line-height: 1.4;\n  padding: 4px 8px;\n  background: #f5f5f5;\n  border: #7e8993 solid 1px;\n  min-height: 30px;\n}\n.acf-admin-3-8 .acf-input-prepend,\n.acf-admin-3-8 .acf-input-append {\n  padding: 3px 5px;\n  border-color: #dddddd;\n  min-height: 28px;\n}\n\n.acf-input-prepend {\n  float: left;\n  border-right-width: 0;\n  border-radius: 3px 0 0 3px;\n}\n\n.acf-input-append {\n  float: right;\n  border-left-width: 0;\n  border-radius: 0 3px 3px 0;\n}\n\n.acf-input-wrap {\n  position: relative;\n  overflow: hidden;\n}\n.acf-input-wrap .acf-is-prepended {\n  border-radius: 0 6px 6px 0 !important;\n}\n.acf-input-wrap .acf-is-appended {\n  border-radius: 6px 0 0 6px !important;\n}\n.acf-input-wrap .acf-is-prepended.acf-is-appended {\n  border-radius: 0 !important;\n}\n\n/* rtl */\nhtml[dir=rtl] .acf-input-prepend {\n  border-left-width: 0;\n  border-right-width: 1px;\n  border-radius: 0 3px 3px 0;\n  float: right;\n}\n\nhtml[dir=rtl] .acf-input-append {\n  border-left-width: 1px;\n  border-right-width: 0;\n  border-radius: 3px 0 0 3px;\n  float: left;\n}\n\nhtml[dir=rtl] input.acf-is-prepended {\n  border-radius: 3px 0 0 3px !important;\n}\n\nhtml[dir=rtl] input.acf-is-appended {\n  border-radius: 0 3px 3px 0 !important;\n}\n\nhtml[dir=rtl] input.acf-is-prepended.acf-is-appended {\n  border-radius: 0 !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Color Picker\n*\n*-----------------------------------------------------------------------------*/\n.acf-color-picker .wp-color-result {\n  border-color: #7e8993;\n}\n.acf-admin-3-8 .acf-color-picker .wp-color-result {\n  border-color: #ccd0d4;\n}\n.acf-color-picker .wp-picker-active {\n  position: relative;\n  z-index: 1;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Url\n*\n*-----------------------------------------------------------------------------*/\n.acf-url i {\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  opacity: 0.5;\n  color: #7e8993;\n}\n.acf-url input[type=url] {\n  padding-left: 27px !important;\n}\n.acf-url.-valid i {\n  opacity: 1;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Select2 (v3)\n*\n*-----------------------------------------------------------------------------*/\n.select2-container.-acf {\n  z-index: 1001;\n  /* open */\n  /* single open */\n}\n.select2-container.-acf .select2-choices {\n  background: #fff;\n  border-color: #ddd;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.07) inset;\n  min-height: 31px;\n}\n.select2-container.-acf .select2-choices .select2-search-choice {\n  margin: 5px 0 5px 5px;\n  padding: 3px 5px 3px 18px;\n  border-color: #bbb;\n  background: #f9f9f9;\n  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.25) inset;\n  /* sortable item*/\n  /* sortable shadow */\n}\n.select2-container.-acf .select2-choices .select2-search-choice.ui-sortable-helper {\n  background: #5897fb;\n  border-color: rgb(63.0964912281, 135.4912280702, 250.4035087719);\n  color: #fff !important;\n  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);\n}\n.select2-container.-acf .select2-choices .select2-search-choice.ui-sortable-helper a {\n  visibility: hidden;\n}\n.select2-container.-acf .select2-choices .select2-search-choice.ui-sortable-placeholder {\n  background-color: #f7f7f7;\n  border-color: #f7f7f7;\n  visibility: visible !important;\n}\n.select2-container.-acf .select2-choices .select2-search-choice-focus {\n  border-color: #999;\n}\n.select2-container.-acf .select2-choices .select2-search-field input {\n  height: 31px;\n  line-height: 22px;\n  margin: 0;\n  padding: 5px 5px 5px 7px;\n}\n.select2-container.-acf .select2-choice {\n  border-color: #bbbbbb;\n}\n.select2-container.-acf .select2-choice .select2-arrow {\n  background: transparent;\n  border-left-color: #dfdfdf;\n  padding-left: 1px;\n}\n.select2-container.-acf .select2-choice .select2-result-description {\n  display: none;\n}\n.select2-container.-acf.select2-container-active .select2-choices, .select2-container.-acf.select2-dropdown-open .select2-choices {\n  border-color: #5b9dd9;\n  border-radius: 3px 3px 0 0;\n}\n.select2-container.-acf.select2-dropdown-open .select2-choice {\n  background: #fff;\n  border-color: #5b9dd9;\n}\n\n/* rtl */\nhtml[dir=rtl] .select2-container.-acf .select2-search-choice-close {\n  left: 24px;\n}\nhtml[dir=rtl] .select2-container.-acf .select2-choice > .select2-chosen {\n  margin-left: 42px;\n}\nhtml[dir=rtl] .select2-container.-acf .select2-choice .select2-arrow {\n  padding-left: 0;\n  padding-right: 1px;\n}\n\n/* description */\n.select2-drop {\n  /* search*/\n  /* result */\n}\n.select2-drop .select2-search {\n  padding: 4px 4px 0;\n}\n.select2-drop .select2-result {\n  /* hover*/\n}\n.select2-drop .select2-result .select2-result-description {\n  color: #999;\n  font-size: 12px;\n  margin-left: 5px;\n}\n.select2-drop .select2-result.select2-highlighted .select2-result-description {\n  color: #fff;\n  opacity: 0.75;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Select2 (v4)\n*\n*-----------------------------------------------------------------------------*/\n.select2-container.-acf li {\n  margin-bottom: 0;\n}\n.select2-container.-acf[data-select2-id^=select2-data] .select2-selection--multiple {\n  overflow: hidden;\n}\n.select2-container.-acf .select2-selection {\n  border-color: #7e8993;\n}\n.acf-admin-3-8 .select2-container.-acf .select2-selection {\n  border-color: #aaa;\n}\n.select2-container.-acf .select2-selection--multiple .select2-search--inline:first-child {\n  float: none;\n}\n.select2-container.-acf .select2-selection--multiple .select2-search--inline:first-child input {\n  width: 100% !important;\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__rendered {\n  padding-right: 0;\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__rendered[id^=select2-acf-field] {\n  display: inline;\n  padding: 0;\n  margin: 0;\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__rendered[id^=select2-acf-field] .select2-selection__choice {\n  margin-right: 0;\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__choice {\n  background-color: #f7f7f7;\n  border-color: #cccccc;\n  max-width: 100%;\n  overflow: hidden;\n  word-wrap: normal !important;\n  white-space: normal;\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__choice.ui-sortable-helper {\n  background: #0783BE;\n  border-color: #066998;\n  color: #fff !important;\n  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__choice.ui-sortable-helper span {\n  visibility: hidden;\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {\n  position: static;\n  border-right: none;\n  padding: 0;\n}\n.select2-container.-acf .select2-selection--multiple .select2-selection__choice.ui-sortable-placeholder {\n  background-color: #F2F4F7;\n  border-color: #F2F4F7;\n  visibility: visible !important;\n}\n.select2-container.-acf .select2-selection--multiple .select2-search__field {\n  box-shadow: none !important;\n  min-height: 0;\n}\n.acf-row .select2-container.-acf .select2-selection--single {\n  overflow: hidden;\n}\n.acf-row .select2-container.-acf .select2-selection--single .select2-selection__rendered {\n  white-space: normal;\n}\n\n.acf-admin-single-field-group .select2-dropdown {\n  border-color: #6BB5D8 !important;\n  margin-top: -5px;\n  overflow: hidden;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n\n.select2-dropdown.select2-dropdown--above {\n  margin-top: 0;\n}\n\n.acf-admin-single-field-group .select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #F9FAFB !important;\n  color: #667085;\n}\n.acf-admin-single-field-group .select2-container--default .select2-results__option[aria-selected=true]:hover {\n  color: #399CCB;\n}\n\n.acf-admin-single-field-group .select2-container--default .select2-results__option--highlighted[aria-selected] {\n  color: #fff !important;\n  background-color: #0783BE !important;\n}\n\n.select2-dropdown .select2-results__option {\n  margin-bottom: 0;\n}\n\n.select2-container .select2-dropdown {\n  z-index: 900000;\n}\n.select2-container .select2-dropdown .select2-search__field {\n  line-height: 1.4;\n  min-height: 0;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Link\n*\n*-----------------------------------------------------------------------------*/\n.acf-link .link-wrap {\n  display: none;\n  border: #ccd0d4 solid 1px;\n  border-radius: 3px;\n  padding: 5px;\n  line-height: 26px;\n  background: #fff;\n  word-wrap: break-word;\n  word-break: break-all;\n}\n.acf-link .link-wrap .link-title {\n  padding: 0 5px;\n}\n.acf-link.-value .button {\n  display: none;\n}\n.acf-link.-value .acf-icon.-link-ext {\n  display: none;\n}\n.acf-link.-value .link-wrap {\n  display: inline-block;\n}\n.acf-link.-external .acf-icon.-link-ext {\n  display: inline-block;\n}\n\n#wp-link-backdrop {\n  z-index: 900000 !important;\n}\n\n#wp-link-wrap {\n  z-index: 900001 !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Radio\n*\n*-----------------------------------------------------------------------------*/\nul.acf-radio-list,\nul.acf-checkbox-list {\n  background: transparent;\n  border: 1px solid transparent;\n  position: relative;\n  padding: 1px;\n  margin: 0;\n  /* hl */\n  /* rtl */\n}\nul.acf-radio-list:focus-within,\nul.acf-checkbox-list:focus-within {\n  border: 1px solid #A5D2E7;\n  border-radius: 6px;\n}\nul.acf-radio-list li,\nul.acf-checkbox-list li {\n  font-size: 13px;\n  line-height: 22px;\n  margin: 0;\n  position: relative;\n  word-wrap: break-word;\n  /* attachment sidebar fix*/\n}\nul.acf-radio-list li label,\nul.acf-checkbox-list li label {\n  display: inline;\n}\nul.acf-radio-list li input[type=checkbox],\nul.acf-radio-list li input[type=radio],\nul.acf-checkbox-list li input[type=checkbox],\nul.acf-checkbox-list li input[type=radio] {\n  margin: -1px 4px 0 0;\n  vertical-align: middle;\n}\nul.acf-radio-list li input[type=text],\nul.acf-checkbox-list li input[type=text] {\n  width: auto;\n  vertical-align: middle;\n  margin: 2px 0;\n}\nul.acf-radio-list li span,\nul.acf-checkbox-list li span {\n  float: none;\n}\nul.acf-radio-list li i,\nul.acf-checkbox-list li i {\n  vertical-align: middle;\n}\nul.acf-radio-list.acf-hl li,\nul.acf-checkbox-list.acf-hl li {\n  margin-right: 20px;\n  clear: none;\n}\nhtml[dir=rtl] ul.acf-radio-list input[type=checkbox],\nhtml[dir=rtl] ul.acf-radio-list input[type=radio],\nhtml[dir=rtl] ul.acf-checkbox-list input[type=checkbox],\nhtml[dir=rtl] ul.acf-checkbox-list input[type=radio] {\n  margin-left: 4px;\n  margin-right: 0;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Button Group\n*\n*-----------------------------------------------------------------------------*/\n.acf-button-group {\n  display: inline-block;\n  /* default (horizontal) */\n  /* vertical */\n}\n.acf-button-group label {\n  display: inline-block;\n  border: #7e8993 solid 1px;\n  position: relative;\n  z-index: 1;\n  padding: 5px 10px;\n  background: #fff;\n}\n.acf-button-group label:hover {\n  color: #016087;\n  background: #f3f5f6;\n  border-color: #0071a1;\n  z-index: 2;\n}\n.acf-button-group label.selected {\n  border-color: #007cba;\n  background: rgb(0, 141, 211.5);\n  color: #fff;\n  z-index: 2;\n}\n.acf-button-group input {\n  display: none !important;\n}\n.acf-button-group {\n  padding-left: 1px;\n  display: inline-flex;\n  flex-direction: row;\n  flex-wrap: nowrap;\n}\n.acf-button-group label {\n  margin: 0 0 0 -1px;\n  flex: 1;\n  text-align: center;\n  white-space: nowrap;\n}\n.acf-button-group label:first-child {\n  border-radius: 3px 0 0 3px;\n}\nhtml[dir=rtl] .acf-button-group label:first-child {\n  border-radius: 0 3px 3px 0;\n}\n.acf-button-group label:last-child {\n  border-radius: 0 3px 3px 0;\n}\nhtml[dir=rtl] .acf-button-group label:last-child {\n  border-radius: 3px 0 0 3px;\n}\n.acf-button-group label:only-child {\n  border-radius: 3px;\n}\n.acf-button-group.-vertical {\n  padding-left: 0;\n  padding-top: 1px;\n  flex-direction: column;\n}\n.acf-button-group.-vertical label {\n  margin: -1px 0 0 0;\n}\n.acf-button-group.-vertical label:first-child {\n  border-radius: 3px 3px 0 0;\n}\n.acf-button-group.-vertical label:last-child {\n  border-radius: 0 0 3px 3px;\n}\n.acf-button-group.-vertical label:only-child {\n  border-radius: 3px;\n}\n.acf-admin-3-8 .acf-button-group label {\n  border-color: #ccd0d4;\n}\n.acf-admin-3-8 .acf-button-group label:hover {\n  border-color: #0071a1;\n}\n.acf-admin-3-8 .acf-button-group label.selected {\n  border-color: #007cba;\n}\n\n.acf-admin-page .acf-button-group {\n  display: flex;\n  align-items: stretch;\n  align-content: center;\n  height: 40px;\n  border-radius: 6px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-admin-page .acf-button-group label {\n  display: inline-flex;\n  align-items: center;\n  align-content: center;\n  border: #D0D5DD solid 1px;\n  padding: 6px 16px;\n  color: #475467;\n  font-weight: 500;\n}\n.acf-admin-page .acf-button-group label:hover {\n  color: #0783BE;\n}\n.acf-admin-page .acf-button-group label.selected {\n  background: #F9FAFB;\n  color: #0783BE;\n}\n.acf-admin-page .select2-container.-acf .select2-selection--multiple .select2-selection__choice {\n  display: inline-flex;\n  align-items: center;\n  margin-top: 8px;\n  margin-left: 2px;\n  position: relative;\n  padding-top: 4px;\n  padding-right: auto;\n  padding-bottom: 4px;\n  padding-left: 8px;\n  background-color: #EBF5FA;\n  border-color: #A5D2E7;\n  color: #0783BE;\n}\n.acf-admin-page .select2-container.-acf .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {\n  order: 2;\n  width: 14px;\n  height: 14px;\n  margin-right: 0;\n  margin-left: 4px;\n  color: #6BB5D8;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.acf-admin-page .select2-container.-acf .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:hover {\n  color: #0783BE;\n}\n.acf-admin-page .select2-container.-acf .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:before {\n  content: \"\";\n  display: block;\n  width: 14px;\n  height: 14px;\n  top: 0;\n  left: 0;\n  background-color: currentColor;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n  mask-image: url(\"../../images/icons/icon-close.svg\");\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Checkbox\n*\n*-----------------------------------------------------------------------------*/\n.acf-checkbox-list .button {\n  margin: 10px 0 0;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  True / False\n*\n*-----------------------------------------------------------------------------*/\n.acf-switch {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  width: fit-content;\n  max-width: 100%;\n  border-radius: 5px;\n  cursor: pointer;\n  position: relative;\n  background: #f5f5f5;\n  height: 30px;\n  vertical-align: middle;\n  border: #7e8993 solid 1px;\n  -webkit-transition: background 0.25s ease;\n  -moz-transition: background 0.25s ease;\n  -o-transition: background 0.25s ease;\n  transition: background 0.25s ease;\n  /* hover */\n  /* active */\n  /* message */\n}\n.acf-switch span {\n  display: inline-block;\n  float: left;\n  text-align: center;\n  font-size: 13px;\n  line-height: 22px;\n  padding: 4px 10px;\n  min-width: 15px;\n}\n.acf-switch span i {\n  vertical-align: middle;\n}\n.acf-switch .acf-switch-on {\n  color: #fff;\n  text-shadow: #007cba 0 1px 0;\n  overflow: hidden;\n}\n.acf-switch .acf-switch-off {\n  overflow: hidden;\n}\n.acf-switch .acf-switch-slider {\n  position: absolute;\n  top: 2px;\n  left: 2px;\n  bottom: 2px;\n  right: 50%;\n  z-index: 1;\n  background: #fff;\n  border-radius: 3px;\n  border: #7e8993 solid 1px;\n  -webkit-transition: all 0.25s ease;\n  -moz-transition: all 0.25s ease;\n  -o-transition: all 0.25s ease;\n  transition: all 0.25s ease;\n  transition-property: left, right;\n}\n.acf-switch:hover, .acf-switch.-focus {\n  border-color: #0071a1;\n  background: #f3f5f6;\n  color: #016087;\n}\n.acf-switch:hover .acf-switch-slider, .acf-switch.-focus .acf-switch-slider {\n  border-color: #0071a1;\n}\n.acf-switch.-on {\n  background: #0d99d5;\n  border-color: #007cba;\n  /* hover */\n}\n.acf-switch.-on .acf-switch-slider {\n  left: 50%;\n  right: 2px;\n  border-color: #007cba;\n}\n.acf-switch.-on:hover {\n  border-color: #007cba;\n}\n.acf-switch + span {\n  margin-left: 6px;\n}\n.acf-admin-3-8 .acf-switch {\n  border-color: #ccd0d4;\n}\n.acf-admin-3-8 .acf-switch .acf-switch-slider {\n  border-color: #ccd0d4;\n}\n.acf-admin-3-8 .acf-switch:hover, .acf-admin-3-8 .acf-switch.-focus {\n  border-color: #0071a1;\n}\n.acf-admin-3-8 .acf-switch:hover .acf-switch-slider, .acf-admin-3-8 .acf-switch.-focus .acf-switch-slider {\n  border-color: #0071a1;\n}\n.acf-admin-3-8 .acf-switch.-on {\n  border-color: #007cba;\n}\n.acf-admin-3-8 .acf-switch.-on .acf-switch-slider {\n  border-color: #007cba;\n}\n.acf-admin-3-8 .acf-switch.-on:hover {\n  border-color: #007cba;\n}\n\n/* checkbox */\n.acf-switch-input {\n  opacity: 0;\n  position: absolute;\n  margin: 0;\n}\n\n.acf-admin-single-field-group .acf-true-false {\n  border: 1px solid transparent;\n}\n.acf-admin-single-field-group .acf-true-false:focus-within {\n  border: 1px solid #399CCB;\n  border-radius: 120px;\n}\n\n.acf-true-false:has(.acf-switch) label {\n  display: flex;\n  align-items: center;\n  justify-items: center;\n}\n\n/* in media modal */\n.compat-item .acf-true-false .message {\n  float: none;\n  padding: 0;\n  vertical-align: middle;\n}\n\n/*--------------------------------------------------------------------------\n*\n*\tGoogle Map\n*\n*-------------------------------------------------------------------------*/\n.acf-google-map {\n  position: relative;\n  border: #ccd0d4 solid 1px;\n  background: #fff;\n}\n.acf-google-map .title {\n  position: relative;\n  border-bottom: #ccd0d4 solid 1px;\n}\n.acf-google-map .title .search {\n  margin: 0;\n  font-size: 14px;\n  line-height: 30px;\n  height: 40px;\n  padding: 5px 10px;\n  border: 0 none;\n  box-shadow: none;\n  border-radius: 0;\n  font-family: inherit;\n  cursor: text;\n}\n.acf-google-map .title .acf-loading {\n  position: absolute;\n  top: 10px;\n  right: 11px;\n  display: none;\n}\n.acf-google-map .title .acf-icon:active {\n  display: inline-block !important;\n}\n.acf-google-map .canvas {\n  height: 400px;\n}\n.acf-google-map:hover .title .acf-actions {\n  display: block;\n}\n.acf-google-map .title .acf-icon.-location {\n  display: inline-block;\n}\n.acf-google-map .title .acf-icon.-cancel,\n.acf-google-map .title .acf-icon.-search {\n  display: none;\n}\n.acf-google-map.-value .title .search {\n  font-weight: bold;\n}\n.acf-google-map.-value .title .acf-icon.-location {\n  display: none;\n}\n.acf-google-map.-value .title .acf-icon.-cancel {\n  display: inline-block;\n}\n.acf-google-map.-searching .title .acf-icon.-location {\n  display: none;\n}\n.acf-google-map.-searching .title .acf-icon.-cancel,\n.acf-google-map.-searching .title .acf-icon.-search {\n  display: inline-block;\n}\n.acf-google-map.-searching .title .acf-actions {\n  display: block;\n}\n.acf-google-map.-searching .title .search {\n  font-weight: normal !important;\n}\n.acf-google-map.-loading .title a {\n  display: none !important;\n}\n.acf-google-map.-loading .title i {\n  display: inline-block;\n}\n\n/* autocomplete */\n.pac-container {\n  border-width: 1px 0;\n  box-shadow: none;\n}\n\n.pac-container:after {\n  display: none;\n}\n\n.pac-container .pac-item:first-child {\n  border-top: 0 none;\n}\n\n.pac-container .pac-item {\n  padding: 5px 10px;\n  cursor: pointer;\n}\n\nhtml[dir=rtl] .pac-container .pac-item {\n  text-align: right;\n}\n\n/*--------------------------------------------------------------------------\n*\n*\tRelationship\n*\n*-------------------------------------------------------------------------*/\n.acf-relationship {\n  background: #fff;\n  border: #ccd0d4 solid 1px;\n  /* list */\n  /* selection (bottom) */\n}\n.acf-relationship .filters {\n  border-bottom: #ccd0d4 solid 1px;\n  background: #fff;\n  /* widths */\n}\n.acf-relationship .filters:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.acf-relationship .filters .filter {\n  margin: 0;\n  padding: 0;\n  float: left;\n  width: 100%;\n  box-sizing: border-box;\n  padding: 7px 7px 7px 0;\n}\n.acf-relationship .filters .filter:first-child {\n  padding-left: 7px;\n}\n.acf-relationship .filters .filter input,\n.acf-relationship .filters .filter select {\n  margin: 0;\n  float: none; /* potential fix for media popup? */\n}\n.acf-relationship .filters .filter input:focus, .acf-relationship .filters .filter input:active,\n.acf-relationship .filters .filter select:focus,\n.acf-relationship .filters .filter select:active {\n  outline: none;\n  box-shadow: none;\n}\n.acf-relationship .filters .filter input {\n  border-color: transparent;\n  box-shadow: none;\n  padding-left: 3px;\n  padding-right: 3px;\n}\n.acf-relationship .filters.-f2 .filter {\n  width: 50%;\n}\n.acf-relationship .filters.-f3 .filter {\n  width: 25%;\n}\n.acf-relationship .filters.-f3 .filter.-search {\n  width: 50%;\n}\n.acf-relationship .list {\n  margin: 0;\n  padding: 5px;\n  height: 160px;\n  overflow: auto;\n}\n.acf-relationship .list .acf-rel-label,\n.acf-relationship .list .acf-rel-item,\n.acf-relationship .list p {\n  padding: 5px;\n  margin: 0;\n  display: block;\n  position: relative;\n  min-height: 18px;\n}\n.acf-relationship .list .acf-rel-label {\n  font-weight: bold;\n}\n.acf-relationship .list .acf-rel-item {\n  cursor: pointer;\n  /* hover */\n  /* disabled */\n}\n.acf-relationship .list .acf-rel-item b {\n  text-decoration: underline;\n  font-weight: normal;\n}\n.acf-relationship .list .acf-rel-item .thumbnail {\n  background: rgb(223.5, 223.5, 223.5);\n  width: 22px;\n  height: 22px;\n  float: left;\n  margin: -2px 5px 0 0;\n}\n.acf-relationship .list .acf-rel-item .thumbnail img {\n  max-width: 22px;\n  max-height: 22px;\n  margin: 0 auto;\n  display: block;\n}\n.acf-relationship .list .acf-rel-item .thumbnail.-icon {\n  background: #fff;\n}\n.acf-relationship .list .acf-rel-item .thumbnail.-icon img {\n  max-height: 20px;\n  margin-top: 1px;\n}\n.acf-relationship .list .acf-rel-item:hover, .acf-relationship .list .acf-rel-item.relationship-hover {\n  background: #3875d7;\n  color: #fff;\n}\n.acf-relationship .list .acf-rel-item:hover .thumbnail, .acf-relationship .list .acf-rel-item.relationship-hover .thumbnail {\n  background: rgb(162.1610878661, 190.6192468619, 236.3389121339);\n}\n.acf-relationship .list .acf-rel-item:hover .thumbnail.-icon, .acf-relationship .list .acf-rel-item.relationship-hover .thumbnail.-icon {\n  background: #fff;\n}\n.acf-relationship .list .acf-rel-item.disabled {\n  opacity: 0.5;\n}\n.acf-relationship .list .acf-rel-item.disabled:hover {\n  background: transparent;\n  color: #333;\n  cursor: default;\n}\n.acf-relationship .list .acf-rel-item.disabled:hover .thumbnail {\n  background: rgb(223.5, 223.5, 223.5);\n}\n.acf-relationship .list .acf-rel-item.disabled:hover .thumbnail.-icon {\n  background: #fff;\n}\n.acf-relationship .list ul {\n  padding-bottom: 5px;\n}\n.acf-relationship .list ul .acf-rel-label,\n.acf-relationship .list ul .acf-rel-item,\n.acf-relationship .list ul p {\n  padding-left: 20px;\n}\n.acf-relationship .selection {\n  position: relative;\n  /* choices */\n  /* values */\n}\n.acf-relationship .selection:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.acf-relationship .selection .values,\n.acf-relationship .selection .choices {\n  width: 50%;\n  background: #fff;\n  float: left;\n}\n.acf-relationship .selection .choices {\n  background: #f9f9f9;\n}\n.acf-relationship .selection .choices .list {\n  border-right: #dfdfdf solid 1px;\n}\n.acf-relationship .selection .values .acf-icon {\n  position: absolute;\n  top: 4px;\n  right: 7px;\n  display: none;\n  /* rtl */\n}\nhtml[dir=rtl] .acf-relationship .selection .values .acf-icon {\n  right: auto;\n  left: 7px;\n}\n.acf-relationship .selection .values .acf-rel-item:hover .acf-icon, .acf-relationship .selection .values .acf-rel-item.relationship-hover .acf-icon {\n  display: block;\n}\n.acf-relationship .selection .values .acf-rel-item {\n  cursor: move;\n}\n.acf-relationship .selection .values .acf-rel-item b {\n  text-decoration: none;\n}\n\n/* menu item fix */\n.menu-item .acf-relationship ul {\n  width: auto;\n}\n.menu-item .acf-relationship li {\n  display: block;\n}\n\n/*--------------------------------------------------------------------------\n*\n*\tWYSIWYG\n*\n*-------------------------------------------------------------------------*/\n.acf-editor-wrap.delay .acf-editor-toolbar {\n  content: \"\";\n  display: block;\n  background: #f5f5f5;\n  border-bottom: #dddddd solid 1px;\n  color: #555d66;\n  padding: 10px;\n}\n.acf-editor-wrap.delay .wp-editor-area {\n  padding: 10px;\n  border: none;\n  color: inherit !important;\n}\n.acf-editor-wrap iframe {\n  min-height: 200px;\n}\n.acf-editor-wrap .wp-editor-container {\n  border: 1px solid #ccd0d4;\n  box-shadow: none !important;\n}\n.acf-editor-wrap .wp-editor-tabs {\n  box-sizing: content-box;\n}\n.acf-editor-wrap .wp-switch-editor {\n  border-color: #ccd0d4;\n  border-bottom-color: transparent;\n}\n\n#mce_fullscreen_container {\n  z-index: 900000 !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tTab\n*\n*-----------------------------------------------------------------------------*/\n.acf-field-tab {\n  display: none !important;\n}\n\n.hidden-by-tab {\n  display: none !important;\n}\n\n.acf-tab-wrap {\n  clear: both;\n  z-index: 1;\n  overflow: auto;\n}\n\n.acf-tab-group {\n  border-bottom: #ccc solid 1px;\n  padding: 10px 10px 0;\n}\n.acf-tab-group li {\n  margin: 0 0.5em 0 0;\n}\n.acf-tab-group li a {\n  padding: 5px 10px;\n  display: block;\n  color: #555;\n  font-size: 14px;\n  font-weight: 600;\n  line-height: 24px;\n  border: #ccc solid 1px;\n  border-bottom: 0 none;\n  text-decoration: none;\n  background: #e5e5e5;\n  transition: none;\n}\n.acf-tab-group li a:hover {\n  background: #fff;\n}\n.acf-tab-group li a:focus {\n  outline: none;\n  box-shadow: none;\n}\n.acf-tab-group li a:empty {\n  display: none;\n}\nhtml[dir=rtl] .acf-tab-group li {\n  margin: 0 0 0 0.5em;\n}\n.acf-tab-group li.active a {\n  background: #f1f1f1;\n  color: #000;\n  padding-bottom: 6px;\n  margin-bottom: -1px;\n  position: relative;\n  z-index: 1;\n}\n\n.acf-fields > .acf-tab-wrap {\n  background: #f9f9f9;\n}\n.acf-fields > .acf-tab-wrap .acf-tab-group {\n  position: relative;\n  border-top: #ccd0d4 solid 1px;\n  border-bottom: #ccd0d4 solid 1px;\n  z-index: 2;\n  margin-bottom: -1px;\n}\n.acf-admin-3-8 .acf-fields > .acf-tab-wrap .acf-tab-group {\n  border-color: #dfdfdf;\n}\n\n.acf-fields.-left > .acf-tab-wrap .acf-tab-group {\n  padding-left: 20%;\n  /* mobile */\n  /* rtl */\n}\n@media screen and (max-width: 640px) {\n  .acf-fields.-left > .acf-tab-wrap .acf-tab-group {\n    padding-left: 10px;\n  }\n}\nhtml[dir=rtl] .acf-fields.-left > .acf-tab-wrap .acf-tab-group {\n  padding-left: 0;\n  padding-right: 20%;\n  /* mobile */\n}\n@media screen and (max-width: 850px) {\n  html[dir=rtl] .acf-fields.-left > .acf-tab-wrap .acf-tab-group {\n    padding-right: 10px;\n  }\n}\n\n.acf-tab-wrap.-left .acf-tab-group {\n  position: absolute;\n  left: 0;\n  width: 20%;\n  border: 0 none;\n  padding: 0 !important; /* important overrides 'left aligned labels' */\n  margin: 1px 0 0;\n}\n.acf-tab-wrap.-left .acf-tab-group li {\n  float: none;\n  margin: -1px 0 0;\n}\n.acf-tab-wrap.-left .acf-tab-group li a {\n  border: 1px solid #ededed;\n  font-size: 13px;\n  line-height: 18px;\n  color: #0073aa;\n  padding: 10px;\n  margin: 0;\n  font-weight: normal;\n  border-width: 1px 0;\n  border-radius: 0;\n  background: transparent;\n}\n.acf-tab-wrap.-left .acf-tab-group li a:hover {\n  color: #00a0d2;\n}\n.acf-tab-wrap.-left .acf-tab-group li.active a {\n  border-color: #dfdfdf;\n  color: #000;\n  margin-right: -1px;\n  background: #fff;\n}\nhtml[dir=rtl] .acf-tab-wrap.-left .acf-tab-group {\n  left: auto;\n  right: 0;\n}\nhtml[dir=rtl] .acf-tab-wrap.-left .acf-tab-group li.active a {\n  margin-right: 0;\n  margin-left: -1px;\n}\n.acf-field + .acf-tab-wrap.-left:before {\n  content: \"\";\n  display: block;\n  position: relative;\n  z-index: 1;\n  height: 10px;\n  border-top: #dfdfdf solid 1px;\n  border-bottom: #dfdfdf solid 1px;\n  margin-bottom: -1px;\n}\n.acf-tab-wrap.-left:first-child .acf-tab-group li:first-child a {\n  border-top: none;\n}\n\n/* sidebar */\n.acf-fields.-sidebar {\n  padding: 0 0 0 20% !important;\n  position: relative;\n  /* before */\n  /* rtl */\n}\n.acf-fields.-sidebar:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 20%;\n  bottom: 0;\n  border-right: #dfdfdf solid 1px;\n  background: #f9f9f9;\n  z-index: 1;\n}\nhtml[dir=rtl] .acf-fields.-sidebar {\n  padding: 0 20% 0 0 !important;\n}\nhtml[dir=rtl] .acf-fields.-sidebar:before {\n  border-left: #dfdfdf solid 1px;\n  border-right-width: 0;\n  left: auto;\n  right: 0;\n}\n.acf-fields.-sidebar.-left {\n  padding: 0 0 0 180px !important;\n  /* rtl */\n}\nhtml[dir=rtl] .acf-fields.-sidebar.-left {\n  padding: 0 180px 0 0 !important;\n}\n.acf-fields.-sidebar.-left:before {\n  background: #f1f1f1;\n  border-color: #dfdfdf;\n  width: 180px;\n}\n.acf-fields.-sidebar.-left > .acf-tab-wrap.-left .acf-tab-group {\n  width: 180px;\n}\n.acf-fields.-sidebar.-left > .acf-tab-wrap.-left .acf-tab-group li a {\n  border-color: #e4e4e4;\n}\n.acf-fields.-sidebar.-left > .acf-tab-wrap.-left .acf-tab-group li.active a {\n  background: #f9f9f9;\n}\n.acf-fields.-sidebar > .acf-field-tab + .acf-field {\n  border-top: none;\n}\n\n.acf-fields.-clear > .acf-tab-wrap {\n  background: transparent;\n}\n.acf-fields.-clear > .acf-tab-wrap .acf-tab-group {\n  margin-top: 0;\n  border-top: none;\n  padding-left: 0;\n  padding-right: 0;\n}\n.acf-fields.-clear > .acf-tab-wrap .acf-tab-group li a {\n  background: #e5e5e5;\n}\n.acf-fields.-clear > .acf-tab-wrap .acf-tab-group li a:hover {\n  background: #fff;\n}\n.acf-fields.-clear > .acf-tab-wrap .acf-tab-group li.active a {\n  background: #f1f1f1;\n}\n\n/* seamless */\n.acf-postbox.seamless > .acf-fields.-sidebar {\n  margin-left: 0 !important;\n}\n.acf-postbox.seamless > .acf-fields.-sidebar:before {\n  background: transparent;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap {\n  background: transparent;\n  margin-bottom: 10px;\n  padding-left: 12px;\n  padding-right: 12px;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap .acf-tab-group {\n  border-top: 0 none;\n  border-color: #ccd0d4;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap .acf-tab-group li a {\n  background: #e5e5e5;\n  border-color: #ccd0d4;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap .acf-tab-group li a:hover {\n  background: #fff;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap .acf-tab-group li.active a {\n  background: #f1f1f1;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap.-left:before {\n  border-top: none;\n  height: auto;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap.-left .acf-tab-group {\n  margin-bottom: 0;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap.-left .acf-tab-group li a {\n  border-width: 1px 0 1px 1px !important;\n  border-color: #cccccc;\n  background: #e5e5e5;\n}\n.acf-postbox.seamless > .acf-fields > .acf-tab-wrap.-left .acf-tab-group li.active a {\n  background: #f1f1f1;\n}\n\n.menu-edit .acf-fields.-clear > .acf-tab-wrap .acf-tab-group li a,\n.widget .acf-fields.-clear > .acf-tab-wrap .acf-tab-group li a {\n  background: #f1f1f1;\n}\n.menu-edit .acf-fields.-clear > .acf-tab-wrap .acf-tab-group li a:hover, .menu-edit .acf-fields.-clear > .acf-tab-wrap .acf-tab-group li.active a,\n.widget .acf-fields.-clear > .acf-tab-wrap .acf-tab-group li a:hover,\n.widget .acf-fields.-clear > .acf-tab-wrap .acf-tab-group li.active a {\n  background: #fff;\n}\n\n.compat-item .acf-tab-wrap td {\n  display: block;\n}\n\n/* within gallery sidebar */\n.acf-gallery-side .acf-tab-wrap {\n  border-top: 0 none !important;\n}\n\n.acf-gallery-side .acf-tab-wrap .acf-tab-group {\n  margin: 10px 0 !important;\n  padding: 0 !important;\n}\n\n.acf-gallery-side .acf-tab-group li.active a {\n  background: #f9f9f9 !important;\n}\n\n/* withing widget */\n.widget .acf-tab-group {\n  border-bottom-color: #e8e8e8;\n}\n\n.widget .acf-tab-group li a {\n  background: #f1f1f1;\n}\n\n.widget .acf-tab-group li.active a {\n  background: #fff;\n}\n\n/* media popup (edit image) */\n.media-modal.acf-expanded .compat-attachment-fields > tbody > tr.acf-tab-wrap .acf-tab-group {\n  padding-left: 23%;\n  border-bottom-color: #dddddd;\n}\n\n/* table */\n.form-table > tbody > tr.acf-tab-wrap .acf-tab-group {\n  padding: 0 5px 0 210px;\n}\n\n/* rtl */\nhtml[dir=rtl] .form-table > tbody > tr.acf-tab-wrap .acf-tab-group {\n  padding: 0 210px 0 5px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\toembed\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-oembed {\n  position: relative;\n  border: #ccd0d4 solid 1px;\n  background: #fff;\n}\n.acf-oembed .title {\n  position: relative;\n  border-bottom: #ccd0d4 solid 1px;\n  padding: 5px 10px;\n}\n.acf-oembed .title .input-search {\n  margin: 0;\n  font-size: 14px;\n  line-height: 30px;\n  height: 30px;\n  padding: 0;\n  border: 0 none;\n  box-shadow: none;\n  border-radius: 0;\n  font-family: inherit;\n  cursor: text;\n}\n.acf-oembed .title .acf-actions {\n  padding: 6px;\n}\n.acf-oembed .canvas {\n  position: relative;\n  min-height: 250px;\n  background: #f9f9f9;\n}\n.acf-oembed .canvas .canvas-media {\n  position: relative;\n  z-index: 1;\n}\n.acf-oembed .canvas iframe {\n  display: block;\n  margin: 0;\n  padding: 0;\n  width: 100%;\n}\n.acf-oembed .canvas .acf-icon.-picture {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 0;\n  height: 42px;\n  width: 42px;\n  font-size: 42px;\n  color: #999;\n}\n.acf-oembed .canvas .acf-loading-overlay {\n  background: rgba(255, 255, 255, 0.9);\n}\n.acf-oembed .canvas .canvas-error {\n  position: absolute;\n  top: 50%;\n  left: 0%;\n  right: 0%;\n  margin: -9px 0 0 0;\n  text-align: center;\n  display: none;\n}\n.acf-oembed .canvas .canvas-error p {\n  padding: 8px;\n  margin: 0;\n  display: inline;\n}\n.acf-oembed.has-value .canvas {\n  min-height: 50px;\n}\n.acf-oembed.has-value .input-search {\n  font-weight: bold;\n}\n.acf-oembed.has-value .title:hover .acf-actions {\n  display: block;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tImage\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-image-uploader {\n  position: relative;\n  /* image wrap*/\n  /* input */\n  /* rtl */\n}\n.acf-image-uploader:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.acf-image-uploader p {\n  margin: 0;\n}\n.acf-image-uploader .image-wrap {\n  position: relative;\n  float: left;\n  /* hover */\n}\n.acf-image-uploader .image-wrap img {\n  max-width: 100%;\n  max-height: 100%;\n  width: auto;\n  height: auto;\n  display: block;\n  min-width: 30px;\n  min-height: 30px;\n  background: #f1f1f1;\n  margin: 0;\n  padding: 0;\n  /* svg */\n}\n.acf-image-uploader .image-wrap img[src$=\".svg\"] {\n  min-height: 100px;\n  min-width: 100px;\n}\n.acf-image-uploader .image-wrap:hover .acf-actions {\n  display: block;\n}\n.acf-image-uploader input.button {\n  width: auto;\n}\nhtml[dir=rtl] .acf-image-uploader .image-wrap {\n  float: right;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tFile\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-file-uploader {\n  position: relative;\n  /* hover */\n  /* rtl */\n}\n.acf-file-uploader p {\n  margin: 0;\n}\n.acf-file-uploader .file-wrap {\n  border: #ccd0d4 solid 1px;\n  min-height: 84px;\n  position: relative;\n  background: #fff;\n}\n.acf-file-uploader .file-icon {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  padding: 10px;\n  background: #f1f1f1;\n  border-right: #d5d9dd solid 1px;\n}\n.acf-file-uploader .file-icon img {\n  display: block;\n  padding: 0;\n  margin: 0;\n  max-width: 48px;\n}\n.acf-file-uploader .file-info {\n  padding: 10px;\n  margin-left: 69px;\n}\n.acf-file-uploader .file-info p {\n  margin: 0 0 2px;\n  font-size: 13px;\n  line-height: 1.4em;\n  word-break: break-all;\n}\n.acf-file-uploader .file-info a {\n  text-decoration: none;\n}\n.acf-file-uploader:hover .acf-actions {\n  display: block;\n}\nhtml[dir=rtl] .acf-file-uploader .file-icon {\n  left: auto;\n  right: 0;\n  border-left: #e5e5e5 solid 1px;\n  border-right: none;\n}\nhtml[dir=rtl] .acf-file-uploader .file-info {\n  margin-right: 69px;\n  margin-left: 0;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tDate Picker\n*\n*-----------------------------------------------------------------------------*/\n.acf-ui-datepicker .ui-datepicker {\n  z-index: 900000 !important;\n}\n.acf-ui-datepicker .ui-datepicker .ui-widget-header a {\n  cursor: pointer;\n  transition: none;\n}\n\n/* fix highlight state overriding hover / active */\n.acf-ui-datepicker .ui-state-highlight.ui-state-hover {\n  border: 1px solid #98b7e8 !important;\n  background: #98b7e8 !important;\n  font-weight: normal !important;\n  color: #ffffff !important;\n}\n\n.acf-ui-datepicker .ui-state-highlight.ui-state-active {\n  border: 1px solid #3875d7 !important;\n  background: #3875d7 !important;\n  font-weight: normal !important;\n  color: #ffffff !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tSeparator field\n*\n*-----------------------------------------------------------------------------*/\n.acf-field-separator {\n  /* fields */\n}\n.acf-field-separator .acf-label {\n  margin-bottom: 0;\n}\n.acf-field-separator .acf-label label {\n  font-weight: normal;\n}\n.acf-field-separator .acf-input {\n  display: none;\n}\n.acf-fields > .acf-field-separator {\n  background: #f9f9f9;\n  border-bottom: 1px solid #dfdfdf;\n  border-top: 1px solid #dfdfdf;\n  margin-bottom: -1px;\n  z-index: 2;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tTaxonomy\n*\n*-----------------------------------------------------------------------------*/\n.acf-taxonomy-field {\n  position: relative;\n  /* hover */\n  /* select */\n}\n.acf-taxonomy-field .categorychecklist-holder {\n  border: #ccd0d4 solid 1px;\n  border-radius: 3px;\n  max-height: 200px;\n  overflow: auto;\n}\n.acf-taxonomy-field .acf-checkbox-list {\n  margin: 0;\n  padding: 10px;\n}\n.acf-taxonomy-field .acf-checkbox-list ul.children {\n  padding-left: 18px;\n}\n.acf-taxonomy-field:hover .acf-actions {\n  display: block;\n}\n.acf-taxonomy-field[data-ftype=select] .acf-actions {\n  padding: 0;\n  margin: -9px;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tRange\n*\n*-----------------------------------------------------------------------------*/\n.acf-range-wrap {\n  /* rtl */\n}\n.acf-range-wrap .acf-append,\n.acf-range-wrap .acf-prepend {\n  display: inline-block;\n  vertical-align: middle;\n  line-height: 28px;\n  margin: 0 7px 0 0;\n}\n.acf-range-wrap .acf-append {\n  margin: 0 0 0 7px;\n}\n.acf-range-wrap input[type=range] {\n  display: inline-block;\n  padding: 0;\n  margin: 0;\n  vertical-align: middle;\n  height: 28px;\n}\n.acf-range-wrap input[type=range]:focus {\n  outline: none;\n}\n.acf-range-wrap input[type=number] {\n  display: inline-block;\n  min-width: 5em;\n  padding-right: 4px;\n  margin-left: 10px;\n  vertical-align: middle;\n}\nhtml[dir=rtl] .acf-range-wrap input[type=number] {\n  margin-right: 10px;\n  margin-left: 0;\n}\nhtml[dir=rtl] .acf-range-wrap .acf-append {\n  margin: 0 7px 0 0;\n}\nhtml[dir=rtl] .acf-range-wrap .acf-prepend {\n  margin: 0 0 0 7px;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  acf-accordion\n*\n*-----------------------------------------------------------------------------*/\n.acf-accordion {\n  margin: -1px 0;\n  padding: 0;\n  background: #fff;\n  border-top: 1px solid #d5d9dd;\n  border-bottom: 1px solid #d5d9dd;\n  z-index: 1;\n}\n.acf-accordion .acf-accordion-title {\n  margin: 0;\n  padding: 12px;\n  font-weight: bold;\n  cursor: pointer;\n  font-size: inherit;\n  font-size: 13px;\n  line-height: 1.4em;\n}\n.acf-accordion .acf-accordion-title:hover {\n  background: #f3f4f5;\n}\n.acf-accordion .acf-accordion-title label {\n  margin: 0;\n  padding: 0;\n  font-size: 13px;\n  line-height: 1.4em;\n}\n.acf-accordion .acf-accordion-title p {\n  font-weight: normal;\n}\n.acf-accordion .acf-accordion-title .acf-accordion-icon {\n  float: right;\n}\n.acf-accordion .acf-accordion-title svg.acf-accordion-icon {\n  position: absolute;\n  right: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #191e23;\n  fill: currentColor;\n}\n.acf-accordion .acf-accordion-content {\n  margin: 0;\n  padding: 0 12px 12px;\n  display: none;\n}\n.acf-accordion.-open > .acf-accordion-content {\n  display: block;\n}\n\n.acf-field.acf-accordion {\n  margin: -1px 0;\n  padding: 0 !important;\n  border-color: #d5d9dd;\n}\n.acf-field.acf-accordion .acf-label.acf-accordion-title {\n  padding: 12px;\n  width: auto;\n  float: none;\n  width: auto;\n}\n.acf-field.acf-accordion .acf-input.acf-accordion-content {\n  padding: 0;\n  float: none;\n  width: auto;\n}\n.acf-field.acf-accordion .acf-input.acf-accordion-content > .acf-fields {\n  border-top: #eeeeee solid 1px;\n}\n.acf-field.acf-accordion .acf-input.acf-accordion-content > .acf-fields.-clear {\n  padding: 0 12px 15px;\n}\n\n/* field specific (left) */\n.acf-fields.-left > .acf-field.acf-accordion:before {\n  display: none;\n}\n.acf-fields.-left > .acf-field.acf-accordion .acf-accordion-title {\n  width: auto;\n  margin: 0 !important;\n  padding: 12px;\n  float: none !important;\n}\n.acf-fields.-left > .acf-field.acf-accordion .acf-accordion-content {\n  padding: 0 !important;\n}\n\n/* field specific (clear) */\n.acf-fields.-clear > .acf-field.acf-accordion {\n  border: #cccccc solid 1px;\n  background: transparent;\n}\n.acf-fields.-clear > .acf-field.acf-accordion + .acf-field.acf-accordion {\n  margin-top: -16px;\n}\n\n/* table */\ntr.acf-field.acf-accordion {\n  background: transparent;\n}\ntr.acf-field.acf-accordion > .acf-input {\n  padding: 0 !important;\n  border: #cccccc solid 1px;\n}\ntr.acf-field.acf-accordion .acf-accordion-content {\n  padding: 0 12px 12px;\n}\n\n/* #addtag */\n#addtag div.acf-field.error {\n  border: 0 none;\n  padding: 8px 0;\n}\n\n#addtag > .acf-field.acf-accordion {\n  padding-right: 0;\n  margin-right: 5%;\n}\n#addtag > .acf-field.acf-accordion + p.submit {\n  margin-top: 0;\n}\n\n/* border */\ntr.acf-accordion {\n  margin: 15px 0 !important;\n}\ntr.acf-accordion + tr.acf-accordion {\n  margin-top: -16px !important;\n}\n\n/* seamless */\n.acf-postbox.seamless > .acf-fields > .acf-accordion {\n  margin-left: 12px;\n  margin-right: 12px;\n  border: #ccd0d4 solid 1px;\n}\n\n/* rtl */\n/* menu item */\n/*\n.menu-item-settings > .field-acf > .acf-field.acf-accordion {\n\tborder: #dfdfdf solid 1px;\n\tmargin: 10px -13px 10px -11px;\n\n\t+ .acf-field.acf-accordion {\n\t\tmargin-top: -11px;\n\t}\n}\n*/\n/* widget */\n.widget .widget-content > .acf-field.acf-accordion {\n  border: #dfdfdf solid 1px;\n  margin-bottom: 10px;\n}\n.widget .widget-content > .acf-field.acf-accordion .acf-accordion-title {\n  margin-bottom: 0;\n}\n.widget .widget-content > .acf-field.acf-accordion + .acf-field.acf-accordion {\n  margin-top: -11px;\n}\n\n.media-modal .compat-attachment-fields .acf-field.acf-accordion + .acf-field.acf-accordion {\n  margin-top: -1px;\n}\n.media-modal .compat-attachment-fields .acf-field.acf-accordion > .acf-input {\n  width: 100%;\n}\n.media-modal .compat-attachment-fields .acf-field.acf-accordion .compat-attachment-fields > tbody > tr > td {\n  padding-bottom: 5px;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tBlock Editor\n*\n*-----------------------------------------------------------------------------*/\n.block-editor .edit-post-sidebar .acf-postbox > .postbox-header,\n.block-editor .edit-post-sidebar .acf-postbox > .hndle {\n  border-bottom-width: 0 !important;\n}\n.block-editor .edit-post-sidebar .acf-postbox.closed > .postbox-header,\n.block-editor .edit-post-sidebar .acf-postbox.closed > .hndle {\n  border-bottom-width: 1px !important;\n}\n.block-editor .edit-post-sidebar .acf-fields {\n  min-height: 1px;\n  overflow: auto;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field {\n  border-width: 0;\n  border-color: #e2e4e7;\n  margin: 0px;\n  padding: 10px 16px;\n  width: auto !important;\n  min-height: 0 !important;\n  float: none !important;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field > .acf-label {\n  margin-bottom: 5px;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field > .acf-label label {\n  font-weight: normal;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field.acf-accordion {\n  padding: 0;\n  margin: 0;\n  border-top-width: 1px;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field.acf-accordion:first-child {\n  border-top-width: 0;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field.acf-accordion .acf-accordion-title {\n  margin: 0;\n  padding: 15px;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field.acf-accordion .acf-accordion-title label {\n  font-weight: 500;\n  color: rgb(30, 30, 30);\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field.acf-accordion .acf-accordion-title svg.acf-accordion-icon {\n  right: 16px;\n}\n.block-editor .edit-post-sidebar .acf-fields > .acf-field.acf-accordion .acf-accordion-content > .acf-fields {\n  border-top-width: 0;\n}\n.block-editor .edit-post-sidebar .block-editor-block-inspector .acf-fields > .acf-notice {\n  display: grid;\n  grid-template-columns: 1fr 25px;\n  padding: 10px;\n  margin: 0;\n}\n.block-editor .edit-post-sidebar .block-editor-block-inspector .acf-fields > .acf-notice p:last-of-type {\n  margin: 0;\n}\n.block-editor .edit-post-sidebar .block-editor-block-inspector .acf-fields > .acf-notice > .acf-notice-dismiss {\n  position: relative;\n  top: unset;\n  right: unset;\n}\n.block-editor .edit-post-sidebar .block-editor-block-inspector .acf-fields .acf-field .acf-notice {\n  margin: 0;\n  padding: 0;\n}\n.block-editor .edit-post-sidebar .block-editor-block-inspector .acf-fields .acf-error {\n  margin-bottom: 10px;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Prefix field label & prefix field names\n*\n*-----------------------------------------------------------------------------*/\n.acf-field-setting-prefix_label p.description,\n.acf-field-setting-prefix_name p.description {\n  order: 3;\n  margin-top: 0;\n  margin-left: 16px;\n}\n.acf-field-setting-prefix_label p.description code,\n.acf-field-setting-prefix_name p.description code {\n  padding-top: 4px;\n  padding-right: 6px;\n  padding-bottom: 4px;\n  padding-left: 6px;\n  background-color: #F2F4F7;\n  border-radius: 4px;\n  color: #667085;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Editor tab styles\n*\n*-----------------------------------------------------------------------------*/\n.acf-fields > .acf-tab-wrap:first-child .acf-tab-group {\n  border-top: none;\n}\n\n.acf-fields > .acf-tab-wrap .acf-tab-group li.active a {\n  background: #ffffff;\n}\n\n.acf-fields > .acf-tab-wrap .acf-tab-group li a {\n  background: #f1f1f1;\n  border-color: #ccd0d4;\n}\n\n.acf-fields > .acf-tab-wrap .acf-tab-group li a:hover {\n  background: #fff;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tUser\n*\n*--------------------------------------------------------------------------------------------*/\n.form-table > tbody {\n  /* field */\n  /* tab wrap */\n  /* misc */\n}\n.form-table > tbody > .acf-field {\n  /* label */\n  /* input */\n}\n.form-table > tbody > .acf-field > .acf-label {\n  padding: 20px 10px 20px 0;\n  width: 210px;\n  /* rtl */\n}\nhtml[dir=rtl] .form-table > tbody > .acf-field > .acf-label {\n  padding: 20px 0 20px 10px;\n}\n.form-table > tbody > .acf-field > .acf-label label {\n  font-size: 14px;\n  color: #23282d;\n}\n.form-table > tbody > .acf-field > .acf-input {\n  padding: 15px 10px;\n  /* rtl */\n}\nhtml[dir=rtl] .form-table > tbody > .acf-field > .acf-input {\n  padding: 15px 10px 15px 5%;\n}\n.form-table > tbody > .acf-tab-wrap td {\n  padding: 15px 5% 15px 0;\n  /* rtl */\n}\nhtml[dir=rtl] .form-table > tbody > .acf-tab-wrap td {\n  padding: 15px 0 15px 5%;\n}\n.form-table > tbody .form-table th.acf-th {\n  width: auto;\n}\n\n#your-profile,\n#createuser {\n  /* override for user css */\n  /* allow sub fields to display correctly */\n}\n#your-profile .acf-field input[type=text],\n#your-profile .acf-field input[type=password],\n#your-profile .acf-field input[type=number],\n#your-profile .acf-field input[type=search],\n#your-profile .acf-field input[type=email],\n#your-profile .acf-field input[type=url],\n#your-profile .acf-field select,\n#createuser .acf-field input[type=text],\n#createuser .acf-field input[type=password],\n#createuser .acf-field input[type=number],\n#createuser .acf-field input[type=search],\n#createuser .acf-field input[type=email],\n#createuser .acf-field input[type=url],\n#createuser .acf-field select {\n  max-width: 25em;\n}\n#your-profile .acf-field textarea,\n#createuser .acf-field textarea {\n  max-width: 500px;\n}\n#your-profile .acf-field .acf-field input[type=text],\n#your-profile .acf-field .acf-field input[type=password],\n#your-profile .acf-field .acf-field input[type=number],\n#your-profile .acf-field .acf-field input[type=search],\n#your-profile .acf-field .acf-field input[type=email],\n#your-profile .acf-field .acf-field input[type=url],\n#your-profile .acf-field .acf-field textarea,\n#your-profile .acf-field .acf-field select,\n#createuser .acf-field .acf-field input[type=text],\n#createuser .acf-field .acf-field input[type=password],\n#createuser .acf-field .acf-field input[type=number],\n#createuser .acf-field .acf-field input[type=search],\n#createuser .acf-field .acf-field input[type=email],\n#createuser .acf-field .acf-field input[type=url],\n#createuser .acf-field .acf-field textarea,\n#createuser .acf-field .acf-field select {\n  max-width: none;\n}\n\n#registerform h2 {\n  margin: 1em 0;\n}\n#registerform .acf-field {\n  margin-top: 0;\n  /*\n  \t\t.acf-input {\n  \t\t\tinput {\n  \t\t\t\tfont-size: 24px;\n  \t\t\t\tpadding: 5px;\n  \t\t\t\theight: auto;\n  \t\t\t}\n  \t\t}\n  */\n}\n#registerform .acf-field .acf-label {\n  margin-bottom: 0;\n}\n#registerform .acf-field .acf-label label {\n  font-weight: normal;\n  line-height: 1.5;\n}\n#registerform p.submit {\n  text-align: right;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tTerm\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-term-fields {\n  padding-right: 5%;\n}\n#acf-term-fields > .acf-field > .acf-label {\n  margin: 0;\n}\n#acf-term-fields > .acf-field > .acf-label label {\n  font-size: 12px;\n  font-weight: normal;\n}\n\np.submit .spinner,\np.submit .acf-spinner {\n  vertical-align: top;\n  float: none;\n  margin: 4px 4px 0;\n}\n\n#edittag .acf-fields.-left > .acf-field {\n  padding-left: 220px;\n}\n#edittag .acf-fields.-left > .acf-field:before {\n  width: 209px;\n}\n#edittag .acf-fields.-left > .acf-field > .acf-label {\n  width: 220px;\n  margin-left: -220px;\n  padding: 0 10px;\n}\n#edittag .acf-fields.-left > .acf-field > .acf-input {\n  padding: 0;\n}\n\n#edittag > .acf-fields.-left {\n  width: 96%;\n}\n#edittag > .acf-fields.-left > .acf-field > .acf-label {\n  padding-left: 0;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tComment\n*\n*--------------------------------------------------------------------------------------------*/\n.editcomment td:first-child {\n  white-space: nowrap;\n  width: 131px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tWidget\n*\n*--------------------------------------------------------------------------------------------*/\n#widgets-right .widget .acf-field .description {\n  padding-left: 0;\n  padding-right: 0;\n}\n\n.acf-widget-fields > .acf-field .acf-label {\n  margin-bottom: 5px;\n}\n.acf-widget-fields > .acf-field .acf-label label {\n  font-weight: normal;\n  margin: 0;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tNav Menu\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-menu-settings {\n  border-top: 1px solid #eee;\n  margin-top: 2em;\n}\n.acf-menu-settings.-seamless {\n  border-top: none;\n  margin-top: 15px;\n}\n.acf-menu-settings.-seamless > h2 {\n  display: none;\n}\n.acf-menu-settings .list li {\n  display: block;\n  margin-bottom: 0;\n}\n\n.acf-fields.acf-menu-item-fields {\n  clear: both;\n  padding-top: 1px;\n}\n.acf-fields.acf-menu-item-fields > .acf-field {\n  margin: 5px 0;\n  padding-right: 10px;\n}\n.acf-fields.acf-menu-item-fields > .acf-field .acf-label {\n  margin-bottom: 0;\n}\n.acf-fields.acf-menu-item-fields > .acf-field .acf-label label {\n  font-style: italic;\n  font-weight: normal;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Attachment Form (single)\n*\n*---------------------------------------------------------------------------------------------*/\n#post .compat-attachment-fields .compat-field-acf-form-data {\n  display: none;\n}\n#post .compat-attachment-fields,\n#post .compat-attachment-fields > tbody,\n#post .compat-attachment-fields > tbody > tr,\n#post .compat-attachment-fields > tbody > tr > th,\n#post .compat-attachment-fields > tbody > tr > td {\n  display: block;\n}\n#post .compat-attachment-fields > tbody > .acf-field {\n  margin: 15px 0;\n}\n#post .compat-attachment-fields > tbody > .acf-field > .acf-label {\n  margin: 0;\n}\n#post .compat-attachment-fields > tbody > .acf-field > .acf-label label {\n  margin: 0;\n  padding: 0;\n}\n#post .compat-attachment-fields > tbody > .acf-field > .acf-label label p {\n  margin: 0 0 3px !important;\n}\n#post .compat-attachment-fields > tbody > .acf-field > .acf-input {\n  margin: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Media Model\n*\n*---------------------------------------------------------------------------------------------*/\n/* WP sets tables to act as divs. ACF uses tables, so these muct be reset */\n.media-modal .compat-attachment-fields td.acf-input table {\n  display: table;\n  table-layout: auto;\n}\n.media-modal .compat-attachment-fields td.acf-input table tbody {\n  display: table-row-group;\n}\n.media-modal .compat-attachment-fields td.acf-input table tr {\n  display: table-row;\n}\n.media-modal .compat-attachment-fields td.acf-input table td, .media-modal .compat-attachment-fields td.acf-input table th {\n  display: table-cell;\n}\n\n/* field widths floats */\n.media-modal .compat-attachment-fields > tbody > .acf-field {\n  margin: 5px 0;\n}\n.media-modal .compat-attachment-fields > tbody > .acf-field > .acf-label {\n  min-width: 30%;\n  margin: 0;\n  padding: 0;\n  float: left;\n  text-align: right;\n  display: block;\n  float: left;\n}\n.media-modal .compat-attachment-fields > tbody > .acf-field > .acf-label > label {\n  padding-top: 6px;\n  margin: 0;\n  color: #666666;\n  font-weight: 400;\n  line-height: 16px;\n}\n.media-modal .compat-attachment-fields > tbody > .acf-field > .acf-input {\n  width: 65%;\n  margin: 0;\n  padding: 0;\n  float: right;\n  display: block;\n}\n.media-modal .compat-attachment-fields > tbody > .acf-field p.description {\n  margin: 0;\n}\n\n/* restricted selection (copy of WP .upload-errors)*/\n.acf-selection-error {\n  background: #ffebe8;\n  border: 1px solid #c00;\n  border-radius: 3px;\n  padding: 8px;\n  margin: 20px 0 0;\n}\n.acf-selection-error .selection-error-label {\n  background: #CC0000;\n  border-radius: 3px;\n  color: #fff;\n  font-weight: bold;\n  margin-right: 8px;\n  padding: 2px 4px;\n}\n.acf-selection-error .selection-error-message {\n  color: #b44;\n  display: block;\n  padding-top: 8px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n}\n\n/* disabled attachment */\n.media-modal .attachment.acf-disabled .thumbnail {\n  opacity: 0.25 !important;\n}\n.media-modal .attachment.acf-disabled .attachment-preview:before {\n  background: rgba(0, 0, 0, 0.15);\n  z-index: 1;\n  position: relative;\n}\n\n/* misc */\n.media-modal {\n  /* compat-item */\n  /* allow line breaks in upload error */\n  /* fix required span */\n  /* sidebar */\n  /* mobile md */\n}\n.media-modal .compat-field-acf-form-data,\n.media-modal .compat-field-acf-blank {\n  display: none !important;\n}\n.media-modal .upload-error-message {\n  white-space: pre-wrap;\n}\n.media-modal .acf-required {\n  padding: 0 !important;\n  margin: 0 !important;\n  float: none !important;\n  color: #f00 !important;\n}\n.media-modal .media-sidebar .compat-item {\n  padding-bottom: 20px;\n}\n@media (max-width: 900px) {\n  .media-modal {\n    /* label */\n    /* field */\n  }\n  .media-modal .setting span,\n  .media-modal .compat-attachment-fields > tbody > .acf-field > .acf-label {\n    width: 98%;\n    float: none;\n    text-align: left;\n    min-height: 0;\n    padding: 0;\n  }\n  .media-modal .setting input,\n  .media-modal .setting textarea,\n  .media-modal .compat-attachment-fields > tbody > .acf-field > .acf-input {\n    float: none;\n    height: auto;\n    max-width: none;\n    width: 98%;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Media Model (expand details)\n*\n*---------------------------------------------------------------------------------------------*/\n.media-modal .acf-expand-details {\n  float: right;\n  padding: 8px 10px;\n  margin-right: 6px;\n  font-size: 13px;\n  height: 18px;\n  line-height: 18px;\n  color: #666;\n  text-decoration: none;\n}\n.media-modal .acf-expand-details:focus, .media-modal .acf-expand-details:active {\n  outline: 0 none;\n  box-shadow: none;\n  color: #666;\n}\n.media-modal .acf-expand-details:hover {\n  color: #000;\n}\n.media-modal .acf-expand-details .is-open {\n  display: none;\n}\n.media-modal .acf-expand-details .is-closed {\n  display: block;\n}\n@media (max-width: 640px) {\n  .media-modal .acf-expand-details {\n    display: none;\n  }\n}\n\n/* expanded */\n.media-modal.acf-expanded {\n  /* toggle */\n}\n.media-modal.acf-expanded .acf-expand-details .is-open {\n  display: block;\n}\n.media-modal.acf-expanded .acf-expand-details .is-closed {\n  display: none;\n}\n.media-modal.acf-expanded .attachments-browser .media-toolbar,\n.media-modal.acf-expanded .attachments-browser .attachments {\n  right: 740px;\n}\n.media-modal.acf-expanded .media-sidebar {\n  width: 708px;\n}\n.media-modal.acf-expanded .media-sidebar .attachment-info .thumbnail {\n  float: left;\n  max-height: none;\n}\n.media-modal.acf-expanded .media-sidebar .attachment-info .thumbnail img {\n  max-width: 100%;\n  max-height: 200px;\n}\n.media-modal.acf-expanded .media-sidebar .attachment-info .details {\n  float: right;\n}\n.media-modal.acf-expanded .media-sidebar .attachment-info .thumbnail,\n.media-modal.acf-expanded .media-sidebar .attachment-details .setting .name,\n.media-modal.acf-expanded .media-sidebar .compat-attachment-fields > tbody > .acf-field > .acf-label {\n  min-width: 20%;\n  margin-right: 0;\n}\n.media-modal.acf-expanded .media-sidebar .attachment-info .details,\n.media-modal.acf-expanded .media-sidebar .attachment-details .setting input,\n.media-modal.acf-expanded .media-sidebar .attachment-details .setting textarea,\n.media-modal.acf-expanded .media-sidebar .attachment-details .setting + .description,\n.media-modal.acf-expanded .media-sidebar .compat-attachment-fields > tbody > .acf-field > .acf-input {\n  min-width: 77%;\n}\n@media (max-width: 900px) {\n  .media-modal.acf-expanded .attachments-browser .media-toolbar {\n    display: none;\n  }\n  .media-modal.acf-expanded .attachments {\n    display: none;\n  }\n  .media-modal.acf-expanded .media-sidebar {\n    width: auto;\n    max-width: none !important;\n    bottom: 0 !important;\n  }\n  .media-modal.acf-expanded .media-sidebar .attachment-info .thumbnail {\n    min-width: 0;\n    max-width: none;\n    width: 30%;\n  }\n  .media-modal.acf-expanded .media-sidebar .attachment-info .details {\n    min-width: 0;\n    max-width: none;\n    width: 67%;\n  }\n}\n@media (max-width: 640px) {\n  .media-modal.acf-expanded .media-sidebar .attachment-info .thumbnail, .media-modal.acf-expanded .media-sidebar .attachment-info .details {\n    width: 100%;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Media Model\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-media-modal {\n  /* hide embed settings */\n}\n.acf-media-modal .media-embed .setting.align,\n.acf-media-modal .media-embed .setting.link-to {\n  display: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Media Model (Select Mode)\n*\n*---------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Media Model (Edit Mode)\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-media-modal.-edit {\n  /* resize modal */\n  left: 15%;\n  right: 15%;\n  top: 100px;\n  bottom: 100px;\n  /* hide elements */\n  /* full width */\n  /* tidy up incorrect distance */\n  /* title box shadow (to match media grid) */\n  /* sidebar */\n  /* mobile md */\n  /* mobile sm */\n}\n.acf-media-modal.-edit .media-frame-menu,\n.acf-media-modal.-edit .media-frame-router,\n.acf-media-modal.-edit .media-frame-content .attachments,\n.acf-media-modal.-edit .media-frame-content .media-toolbar {\n  display: none;\n}\n.acf-media-modal.-edit .media-frame-title,\n.acf-media-modal.-edit .media-frame-content,\n.acf-media-modal.-edit .media-frame-toolbar,\n.acf-media-modal.-edit .media-sidebar {\n  width: auto;\n  left: 0;\n  right: 0;\n}\n.acf-media-modal.-edit .media-frame-content {\n  top: 50px;\n}\n.acf-media-modal.-edit .media-frame-title {\n  border-bottom: 1px solid #DFDFDF;\n  box-shadow: 0 4px 4px -4px rgba(0, 0, 0, 0.1);\n}\n.acf-media-modal.-edit .media-sidebar {\n  padding: 0 16px;\n  /* WP details */\n  /* ACF fields */\n  /* WP required message */\n}\n.acf-media-modal.-edit .media-sidebar .attachment-details {\n  overflow: visible;\n  /* hide 'Attachment Details' heading */\n  /* remove overflow */\n  /* move thumbnail */\n}\n.acf-media-modal.-edit .media-sidebar .attachment-details > h3, .acf-media-modal.-edit .media-sidebar .attachment-details > h2 {\n  display: none;\n}\n.acf-media-modal.-edit .media-sidebar .attachment-details .attachment-info {\n  background: #fff;\n  border-bottom: #dddddd solid 1px;\n  padding: 16px;\n  margin: 0 -16px 16px;\n}\n.acf-media-modal.-edit .media-sidebar .attachment-details .thumbnail {\n  margin: 0 16px 0 0;\n}\n.acf-media-modal.-edit .media-sidebar .attachment-details .setting {\n  margin: 0 0 5px;\n}\n.acf-media-modal.-edit .media-sidebar .attachment-details .setting span {\n  margin: 0;\n}\n.acf-media-modal.-edit .media-sidebar .compat-attachment-fields > tbody > .acf-field {\n  margin: 0 0 5px;\n}\n.acf-media-modal.-edit .media-sidebar .compat-attachment-fields > tbody > .acf-field p.description {\n  margin-top: 3px;\n}\n.acf-media-modal.-edit .media-sidebar .media-types-required-info {\n  display: none;\n}\n@media (max-width: 900px) {\n  .acf-media-modal.-edit {\n    top: 30px;\n    right: 30px;\n    bottom: 30px;\n    left: 30px;\n  }\n}\n@media (max-width: 640px) {\n  .acf-media-modal.-edit {\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n  }\n}\n@media (max-width: 480px) {\n  .acf-media-modal.-edit .media-frame-content {\n    top: 40px;\n  }\n}\n\n.acf-temp-remove {\n  position: relative;\n  opacity: 1;\n  -webkit-transition: all 0.25s ease;\n  -moz-transition: all 0.25s ease;\n  -o-transition: all 0.25s ease;\n  transition: all 0.25s ease;\n  overflow: hidden;\n  /* overlay prevents hover */\n}\n.acf-temp-remove:after {\n  display: block;\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 99;\n}\n\n.hidden-by-conditional-logic {\n  display: none !important;\n}\n.hidden-by-conditional-logic.appear-empty {\n  display: table-cell !important;\n}\n.hidden-by-conditional-logic.appear-empty .acf-input {\n  display: none !important;\n}\n\n.acf-postbox.acf-hidden {\n  display: none !important;\n}\n\n.acf-attention {\n  transition: border 0.25s ease-out;\n}\n.acf-attention.-focused {\n  border: #23282d solid 1px !important;\n  transition: none;\n}\n\ntr.acf-attention {\n  transition: box-shadow 0.25s ease-out;\n  position: relative;\n}\ntr.acf-attention.-focused {\n  box-shadow: #23282d 0 0 0px 1px !important;\n}\n\n#editor .edit-post-layout__metaboxes {\n  padding: 0;\n}\n#editor .edit-post-layout__metaboxes .edit-post-meta-boxes-area {\n  margin: 0;\n}\n#editor .metabox-location-side .postbox-container {\n  float: none;\n}\n#editor .postbox {\n  color: #444;\n}\n#editor .postbox > .postbox-header .hndle {\n  border-bottom: none;\n}\n#editor .postbox > .postbox-header .hndle:hover {\n  background: transparent;\n}\n#editor .postbox > .postbox-header .handle-actions .handle-order-higher,\n#editor .postbox > .postbox-header .handle-actions .handle-order-lower {\n  width: 1.62rem;\n}\n#editor .postbox > .postbox-header .handle-actions .acf-hndle-cog {\n  height: 44px;\n  line-height: 44px;\n}\n#editor .postbox > .postbox-header:hover {\n  background: #f0f0f0;\n}\n#editor .postbox:last-child.closed > .postbox-header {\n  border-bottom: none;\n}\n#editor .postbox:last-child > .inside {\n  border-bottom: none;\n}\n#editor .block-editor-writing-flow__click-redirect {\n  min-height: 50px;\n}\n\nbody.is-dragging-metaboxes #acf_after_title-sortables {\n  outline: 3px dashed #646970;\n  display: flow-root;\n  min-height: 60px;\n  margin-bottom: 3px !important;\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* colors */\n$acf_blue: #2a9bd9;\n$acf_notice: #2a9bd9;\n$acf_error: #d94f4f;\n$acf_success: #49ad52;\n$acf_warning: #fd8d3b;\n\n/* acf-field */\n$field_padding: 15px 12px;\n$field_padding_x: 12px;\n$field_padding_y: 15px;\n$fp: 15px 12px;\n$fy: 15px;\n$fx: 12px;\n\n/* responsive */\n$md: 880px;\n$sm: 640px;\n\n// Admin.\n$wp-card-border: #ccd0d4;\t\t\t// Card border.\n$wp-card-border-1: #d5d9dd;\t\t  // Card inner border 1: Structural (darker).\n$wp-card-border-2: #eeeeee;\t\t  // Card inner border 2: Fields (lighter).\n$wp-input-border: #7e8993;\t\t   // Input border.\n\n// Admin 3.8\n$wp38-card-border: #E5E5E5;\t\t  // Card border.\n$wp38-card-border-1: #dfdfdf;\t\t// Card inner border 1: Structural (darker).\n$wp38-card-border-2: #eeeeee;\t\t// Card inner border 2: Fields (lighter).\n$wp38-input-border: #dddddd;\t\t // Input border.\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Grays\n$gray-50:  #F9FAFB;\n$gray-100: #F2F4F7;\n$gray-200: #EAECF0;\n$gray-300: #D0D5DD;\n$gray-400: #98A2B3;\n$gray-500: #667085;\n$gray-600: #475467;\n$gray-700: #344054;\n$gray-800: #1D2939;\n$gray-900: #101828;\n\n// Blues\n$blue-50:  #EBF5FA;\n$blue-100: #D8EBF5;\n$blue-200: #A5D2E7;\n$blue-300: #6BB5D8;\n$blue-400: #399CCB;\n$blue-500: #0783BE;\n$blue-600: #066998;\n$blue-700: #044E71;\n$blue-800: #033F5B;\n$blue-900: #032F45;\n\n// Utility\n$color-info:\t#2D69DA;\n$color-success:\t#52AA59;\n$color-warning:\t#F79009;\n$color-danger:\t#D13737;\n\n$color-primary: $blue-500;\n$color-primary-hover: $blue-600;\n$color-secondary: $gray-500;\n$color-secondary-hover: $gray-400;\n\n// Gradients\n$gradient-pro: radial-gradient(141.77% 141.08% at 100.26% 99.25%, #0ECAD4 0%, #7A45E5 100%);\n\n// Border radius\n$radius-sm:\t4px;\n$radius-md: 6px;\n$radius-lg: 8px;\n$radius-xl: 12px;\n\n// Elevations / Box shadows\n$elevation-01: 0px 1px 2px rgba($gray-900, 0.10);\n\n// Input & button focus outline\n$outline: 3px solid $blue-50;\n\n// Link colours\n$link-color: $blue-500;\n\n// Responsive\n$max-width: 1440px;", "/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n@mixin clearfix() {\n\t&:after {\n\t\tdisplay: block;\n\t\tclear: both;\n\t\tcontent: \"\";\n\t}\n}\n\n@mixin border-box() {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n}\n\n@mixin centered() {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n@mixin animate( $properties: 'all' ) {\n\t-webkit-transition: $properties 0.3s ease;  // Safari 3.2+, Chrome\n    -moz-transition: $properties 0.3s ease;  \t// Firefox 4-15\n    -o-transition: $properties 0.3s ease;  \t\t// Opera 10.5–12.00\n    transition: $properties 0.3s ease;  \t\t// Firefox 16+, Opera 12.50+\n}\n\n@mixin rtl() {\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t\t@content;\n\t}\n}\n\n@mixin wp-admin( $version: '3-8' ) {\n\t.acf-admin-#{$version} & {\n\t\t@content;\n\t}\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t#wpcontent {\n\t\tline-height: 140%;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\ta {\n\t\tcolor: $blue-500;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-h1 {\n\tfont-size: 21px;\n\tfont-weight: 400;\n}\n\n.acf-h2 {\n\tfont-size: 18px;\n\tfont-weight: 400;\n}\n\n.acf-h3 {\n\tfont-size: 16px;\n\tfont-weight: 400;\n}\n\n.acf-admin-page,\n.acf-headerbar {\n\n\th1 {\n\t\t@extend .acf-h1;\n\t}\n\n\th2 {\n\t\t@extend .acf-h2;\n\t}\n\n\th3 {\n\t\t@extend .acf-h3;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-admin-page {\n\n\t.p1 {\n\t\tfont-size: 15px;\n\t}\n\n\t.p2 {\n\t\tfont-size: 14px;\n\t}\n\n\t.p3 {\n\t\tfont-size: 13.5px;\n\t}\n\n\t.p4 {\n\t\tfont-size: 13px;\n\t}\n\n\t.p5 {\n\t\tfont-size: 12.5px;\n\t}\n\n\t.p6 {\n\t\tfont-size: 12px;\n\t}\n\n\t.p7 {\n\t\tfont-size: 11.5px;\n\t}\n\n\t.p8 {\n\t\tfont-size: 11px;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n\t@extend .acf-h2;\n\tcolor: $gray-700;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\t.acf-settings-wrap h1 {\n\t\tdisplay: none !important;\n\t}\n\n\t#acf-admin-tools h1:not(.acf-field-group-pro-features-title, .acf-field-group-pro-features-title-sm) {\n\t\tdisplay: none !important;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-small {\n\t@extend .p6;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\ta:focus {\n\t\tbox-shadow: none;\n\t\toutline: none;\n\t}\n\n\ta:focus-visible {\n\t\tbox-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgb(79 148 212 / 80%);\n\t\toutline: 1px solid transparent;\n\t}\n}\n", "/*--------------------------------------------------------------------------------------------\n*\n*\tacf-field\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-field,\n.acf-field .acf-label,\n.acf-field .acf-input {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n\tposition: relative;\n}\n\n.acf-field {\n\tmargin: 15px 0;\n\n\t// clear is important as it will avoid any layout issues with floating fields\n\t// do not delete (you have tried this)\n\tclear: both;\n\n\t// description\n\tp.description {\n\t\tdisplay: block;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t}\n\n\t// label\n\t.acf-label {\n\t\tvertical-align: top;\n\t\tmargin: 0 0 10px;\n\n\t\tlabel {\n\t\t\tdisplay: block;\n\t\t\tfont-weight: 500;\n\t\t\tmargin: 0 0 3px;\n\t\t\tpadding: 0;\n\t\t}\n\n\t\t&:empty {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t}\n\n\t// input\n\t.acf-input {\n\t\tvertical-align: top;\n\t}\n\n\t// description\n\tp.description {\n\t\tdisplay: block;\n\t\tmargin: {\n\t\t\ttop: 6px;\n\t\t}\n\t\t@extend .p6;\n\t\tcolor: $gray-500;\n\t}\n\n\t// notice\n\t.acf-notice {\n\t\tmargin: 0 0 15px;\n\t\tbackground: #edf2ff;\n\t\tcolor: #0c6ca0;\n\t\tborder-color: #2183b9;\n\n\t\t// error\n\t\t&.-error {\n\t\t\tbackground: #ffe6e6;\n\t\t\tcolor: #cc2727;\n\t\t\tborder-color: #d12626;\n\t\t}\n\n\t\t// success\n\t\t&.-success {\n\t\t\tbackground: #eefbe8;\n\t\t\tcolor: #0e7b17;\n\t\t\tborder-color: #32a23b;\n\t\t}\n\n\t\t// warning\n\t\t&.-warning {\n\t\t\tbackground: #fff3e6;\n\t\t\tcolor: #bd4b0e;\n\t\t\tborder-color: #d16226;\n\t\t}\n\t}\n\n\t// table\n\t@at-root td#{&},\n\t\ttr#{&} {\n\t\tmargin: 0;\n\t}\n}\n\n// width\n.acf-field[data-width] {\n\tfloat: left;\n\tclear: none;\n\n\t// next\n\t+ .acf-field[data-width] {\n\t\tborder-left: 1px solid #eeeeee;\n\t}\n\n\t// rtl\n\thtml[dir=\"rtl\"] & {\n\t\tfloat: right;\n\n\t\t+ .acf-field[data-width] {\n\t\t\tborder-left: none;\n\t\t\tborder-right: 1px solid #eeeeee;\n\t\t}\n\t}\n\n\t// table\n\t@at-root td#{&},\n\t\ttr#{&} {\n\t\tfloat: none;\n\t}\n\n\t// mobile\n\t/*\n\t@media screen and (max-width: $sm) {\n\t\tfloat: none;\n\t\twidth: auto;\n\t\tborder-left-width: 0;\n\t\tborder-right-width: 0;\n\t}\n*/\n}\n\n// float helpers\n.acf-field.-c0 {\n\tclear: both;\n\tborder-left-width: 0 !important;\n\n\t// rtl\n\thtml[dir=\"rtl\"] & {\n\t\tborder-left-width: 1px !important;\n\t\tborder-right-width: 0 !important;\n\t}\n}\n\n.acf-field.-r0 {\n\tborder-top-width: 0 !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-fields\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-fields {\n\tposition: relative;\n\n\t// clearifx\n\t@include clearfix();\n\n\t// border\n\t&.-border {\n\t\tborder: $wp-card-border solid 1px;\n\t\tbackground: #fff;\n\t}\n\n\t// field\n\t> .acf-field {\n\t\tposition: relative;\n\t\tmargin: 0;\n\t\tpadding: 16px;\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t}\n\n\t\t// first\n\t\t&:first-child {\n\t\t\tborder-top: none;\n\t\t\tmargin-top: 0;\n\t\t}\n\t}\n\n\t// table\n\t@at-root td#{&} {\n\t\tpadding: 0 !important;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-fields (clear)\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-fields.-clear > .acf-field {\n\tborder: none;\n\tpadding: 0;\n\tmargin: 15px 0;\n\n\t// width\n\t&[data-width] {\n\t\tborder: none !important;\n\t}\n\n\t// label\n\t> .acf-label {\n\t\tpadding: 0;\n\t}\n\n\t// input\n\t> .acf-input {\n\t\tpadding: 0;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-fields (left)\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-fields.-left > .acf-field {\n\tpadding: $fy 0;\n\n\t// clearifx\n\t@include clearfix();\n\n\t// sidebar\n\t&:before {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\tz-index: 0;\n\t\tbackground: #f9f9f9;\n\t\tborder-color: #e1e1e1;\n\t\tborder-style: solid;\n\t\tborder-width: 0 1px 0 0;\n\t\ttop: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\twidth: 20%;\n\t}\n\n\t// width\n\t&[data-width] {\n\t\tfloat: none;\n\t\twidth: auto !important;\n\t\tborder-left-width: 0 !important;\n\t\tborder-right-width: 0 !important;\n\t}\n\n\t// label\n\t> .acf-label {\n\t\tfloat: left;\n\t\twidth: 20%;\n\t\tmargin: 0;\n\t\tpadding: 0 $fx;\n\t}\n\n\t// input\n\t> .acf-input {\n\t\tfloat: left;\n\t\twidth: 80%;\n\t\tmargin: 0;\n\t\tpadding: 0 $fx;\n\t}\n\n\t// rtl\n\thtml[dir=\"rtl\"] & {\n\t\t// sidebar\n\t\t&:before {\n\t\t\tborder-width: 0 0 0 1px;\n\t\t\tleft: auto;\n\t\t\tright: 0;\n\t\t}\n\n\t\t// label\n\t\t> .acf-label {\n\t\t\tfloat: right;\n\t\t}\n\n\t\t// input\n\t\t> .acf-input {\n\t\t\tfloat: right;\n\t\t}\n\t}\n\n\t// In sidebar.\n\t#side-sortables & {\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\t\t> .acf-label {\n\t\t\twidth: 100%;\n\t\t\tmargin-bottom: 10px;\n\t\t}\n\t\t> .acf-input {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t// mobile\n\t@media screen and (max-width: $sm) {\n\t\t// sidebar\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t// label\n\t\t> .acf-label {\n\t\t\twidth: 100%;\n\t\t\tmargin-bottom: 10px;\n\t\t}\n\n\t\t// input\n\t\t> .acf-input {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n}\n\n/* clear + left */\n.acf-fields.-clear.-left > .acf-field {\n\tpadding: 0;\n\tborder: none;\n\n\t// sidebar\n\t&:before {\n\t\tdisplay: none;\n\t}\n\n\t// label\n\t> .acf-label {\n\t\tpadding: 0;\n\t}\n\n\t// input\n\t> .acf-input {\n\t\tpadding: 0;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-table\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-table tr.acf-field {\n\t// label\n\t> td.acf-label {\n\t\tpadding: $fp;\n\t\tmargin: 0;\n\t\tbackground: #f9f9f9;\n\t\twidth: 20%;\n\t}\n\n\t// input\n\t> td.acf-input {\n\t\tpadding: $fp;\n\t\tmargin: 0;\n\t\tborder-left-color: #e1e1e1;\n\t}\n}\n\n.acf-sortable-tr-helper {\n\tposition: relative !important;\n\tdisplay: table-row !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-postbox\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-postbox {\n\tposition: relative;\n\n\t// inside\n\t> .inside {\n\t\tmargin: 0 !important; /* override WP style - do not delete - you have tried this before */\n\t\tpadding: 0 !important; /* override WP style - do not delete - you have tried this before */\n\t}\n\n\t// Edit cog.\n\t.acf-hndle-cog {\n\t\tcolor: #72777c;\n\t\tfont-size: 16px;\n\t\tline-height: 36px;\n\t\theight: 36px; // Mimic WP 5.5\n\t\twidth: 1.62rem; // Mimic WP 5.5\n\t\tposition: relative;\n\t\tdisplay: none;\n\t\t&:hover {\n\t\t\tcolor: #191e23;\n\t\t}\n\t}\n\n\t// Show on hover.\n\t> .hndle:hover,\n\t> .postbox-header:hover {\n\t\t.acf-hndle-cog {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n\n\t// WP < 5.5 styling\n\t> .hndle {\n\t\t.acf-hndle-cog {\n\t\t\theight: 20px;\n\t\t\tline-height: 20px;\n\t\t\tfloat: right;\n\t\t\twidth: auto;\n\t\t\t&:hover {\n\t\t\t\tcolor: #777777;\n\t\t\t}\n\t\t}\n\t}\n\n\t// replace\n\t.acf-replace-with-fields {\n\t\tpadding: 15px;\n\t\ttext-align: center;\n\t}\n}\n\n// Correct margin around #acf_after_title\n#post-body-content #acf_after_title-sortables {\n\tmargin: 20px 0 -20px;\n}\n\n/* seamless */\n.acf-postbox.seamless {\n\tborder: 0 none;\n\tbackground: transparent;\n\tbox-shadow: none;\n\n\t/* hide hndle */\n\t> .postbox-header,\n\t> .hndle,\n\t> .handlediv {\n\t\tdisplay: none !important;\n\t}\n\n\t/* inside */\n\t> .inside {\n\t\tdisplay: block !important; /* stop metabox from hiding when closed */\n\t\tmargin-left: -$field_padding_x !important;\n\t\tmargin-right: -$field_padding_x !important;\n\n\t\t> .acf-field {\n\t\t\tborder-color: transparent;\n\t\t}\n\t}\n}\n\n/* seamless (left) */\n.acf-postbox.seamless > .acf-fields.-left {\n\t/* hide sidebar bg */\n\t> .acf-field:before {\n\t\tdisplay: none;\n\t}\n\n\t/* mobile */\n\t@media screen and (max-width: 782px) {\n\t\t/* remove padding */\n\t\t& > .acf-field > .acf-label,\n\t\t& > .acf-field > .acf-input {\n\t\t\tpadding: 0;\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Inputs\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-field {\n\tinput[type=\"text\"],\n\tinput[type=\"password\"],\n\tinput[type=\"date\"],\n\tinput[type=\"datetime\"],\n\tinput[type=\"datetime-local\"],\n\tinput[type=\"email\"],\n\tinput[type=\"month\"],\n\tinput[type=\"number\"],\n\tinput[type=\"search\"],\n\tinput[type=\"tel\"],\n\tinput[type=\"time\"],\n\tinput[type=\"url\"],\n\tinput[type=\"week\"],\n\ttextarea,\n\tselect {\n\t\twidth: 100%;\n\t\tpadding: 4px 8px;\n\t\tmargin: 0;\n\t\tbox-sizing: border-box;\n\t\tfont-size: 14px;\n\t\tline-height: 1.4;\n\n\t\t// WP Admin 3.8\n\t\t@include wp-admin(\"3-8\") {\n\t\t\tpadding: 3px 5px;\n\t\t}\n\t}\n\ttextarea {\n\t\tresize: vertical;\n\t}\n}\n\n// Fix extra padding in Firefox.\nbody.acf-browser-firefox .acf-field select {\n\tpadding: 4px 5px;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Text\n*\n*-----------------------------------------------------------------------------*/\n.acf-input-prepend,\n.acf-input-append,\n.acf-input-wrap {\n\tbox-sizing: border-box;\n}\n\n.acf-input-prepend,\n.acf-input-append {\n\tfont-size: 13px;\n\tline-height: 1.4;\n\tpadding: 4px 8px;\n\tbackground: #f5f5f5;\n\tborder: $wp-input-border solid 1px;\n\tmin-height: 30px;\n\n\t// WP Admin 3.8\n\t@include wp-admin(\"3-8\") {\n\t\tpadding: 3px 5px;\n\t\tborder-color: $wp38-input-border;\n\t\tmin-height: 28px;\n\t}\n}\n\n.acf-input-prepend {\n\tfloat: left;\n\tborder-right-width: 0;\n\tborder-radius: 3px 0 0 3px;\n}\n\n.acf-input-append {\n\tfloat: right;\n\tborder-left-width: 0;\n\tborder-radius: 0 3px 3px 0;\n}\n\n.acf-input-wrap {\n\tposition: relative;\n\toverflow: hidden;\n\t.acf-is-prepended {\n\t\tborder-radius: 0 $radius-md $radius-md 0 !important;\n\t}\n\t.acf-is-appended {\n\t\tborder-radius: $radius-md 0 0 $radius-md !important;\n\t}\n\t.acf-is-prepended.acf-is-appended {\n\t\tborder-radius: 0 !important;\n\t}\n}\n\n/* rtl */\nhtml[dir=\"rtl\"] .acf-input-prepend {\n\tborder-left-width: 0;\n\tborder-right-width: 1px;\n\tborder-radius: 0 3px 3px 0;\n\n\tfloat: right;\n}\n\nhtml[dir=\"rtl\"] .acf-input-append {\n\tborder-left-width: 1px;\n\tborder-right-width: 0;\n\tborder-radius: 3px 0 0 3px;\n\tfloat: left;\n}\n\nhtml[dir=\"rtl\"] input.acf-is-prepended {\n\tborder-radius: 3px 0 0 3px !important;\n}\n\nhtml[dir=\"rtl\"] input.acf-is-appended {\n\tborder-radius: 0 3px 3px 0 !important;\n}\n\nhtml[dir=\"rtl\"] input.acf-is-prepended.acf-is-appended {\n\tborder-radius: 0 !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Color Picker\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-color-picker {\n\t.wp-color-result {\n\t\tborder-color: $wp-input-border;\n\t\t@include wp-admin(\"3-8\") {\n\t\t\tborder-color: $wp-card-border;\n\t\t}\n\t}\n\t.wp-picker-active {\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Url\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-url {\n\ti {\n\t\tposition: absolute;\n\t\ttop: 5px;\n\t\tleft: 5px;\n\t\topacity: 0.5;\n\t\tcolor: #7e8993;\n\t}\n\n\tinput[type=\"url\"] {\n\t\tpadding-left: 27px !important;\n\t}\n\n\t&.-valid i {\n\t\topacity: 1;\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Select2 (v3)\n*\n*-----------------------------------------------------------------------------*/\n\n.select2-container.-acf {\n\tz-index: 1001;\n\t\n\t.select2-choices {\n\t\tbackground: #fff;\n\t\tborder-color: #ddd;\n\t\tbox-shadow: 0 1px 2px rgba(0, 0, 0, 0.07) inset;\n\t\tmin-height: 31px;\n\n\t\t.select2-search-choice {\n\t\t\tmargin: 5px 0 5px 5px;\n\t\t\tpadding: 3px 5px 3px 18px;\n\t\t\tborder-color: #bbb;\n\t\t\tbackground: #f9f9f9;\n\t\t\tbox-shadow: 0 1px 0 rgba(255, 255, 255, 0.25) inset;\n\n\t\t\t/* sortable item*/\n\t\t\t&.ui-sortable-helper {\n\t\t\t\tbackground: #5897fb;\n\t\t\t\tborder-color: darken(#5897fb, 5%);\n\t\t\t\tcolor: #fff !important;\n\t\t\t\tbox-shadow: 0 0 3px rgba(0, 0, 0, 0.1);\n\n\t\t\t\ta {\n\t\t\t\t\tvisibility: hidden;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* sortable shadow */\n\t\t\t&.ui-sortable-placeholder {\n\t\t\t\tbackground-color: #f7f7f7;\n\t\t\t\tborder-color: #f7f7f7;\n\t\t\t\tvisibility: visible !important;\n\t\t\t}\n\t\t}\n\n\t\t.select2-search-choice-focus {\n\t\t\tborder-color: #999;\n\t\t}\n\n\t\t.select2-search-field input {\n\t\t\theight: 31px;\n\t\t\tline-height: 22px;\n\t\t\tmargin: 0;\n\t\t\tpadding: 5px 5px 5px 7px;\n\t\t}\n\t}\n\n\t.select2-choice {\n\t\tborder-color: #bbbbbb;\n\n\t\t.select2-arrow {\n\t\t\tbackground: transparent;\n\t\t\tborder-left-color: #dfdfdf;\n\t\t\tpadding-left: 1px;\n\t\t}\n\n\t\t.select2-result-description {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t/* open */\n\t&.select2-container-active .select2-choices,\n\t&.select2-dropdown-open .select2-choices {\n\t\tborder-color: #5b9dd9;\n\t\tborder-radius: 3px 3px 0 0;\n\t}\n\n\t/* single open */\n\t&.select2-dropdown-open .select2-choice {\n\t\tbackground: #fff;\n\t\tborder-color: #5b9dd9;\n\t}\n}\n\n/* rtl */\nhtml[dir=\"rtl\"] .select2-container.-acf {\n\t.select2-search-choice-close {\n\t\tleft: 24px;\n\t}\n\n\t.select2-choice > .select2-chosen {\n\t\tmargin-left: 42px;\n\t}\n\n\t.select2-choice .select2-arrow {\n\t\tpadding-left: 0;\n\t\tpadding-right: 1px;\n\t}\n}\n\n/* description */\n.select2-drop {\n\t/* search*/\n\t.select2-search {\n\t\tpadding: 4px 4px 0;\n\t}\n\n\t/* result */\n\t.select2-result {\n\t\t.select2-result-description {\n\t\t\tcolor: #999;\n\t\t\tfont-size: 12px;\n\t\t\tmargin-left: 5px;\n\t\t}\n\n\t\t/* hover*/\n\t\t&.select2-highlighted {\n\t\t\t.select2-result-description {\n\t\t\t\tcolor: #fff;\n\t\t\t\topacity: 0.75;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Select2 (v4)\n*\n*-----------------------------------------------------------------------------*/\n.select2-container.-acf {\n\t// Reset WP default style.\n\tli {\n\t\tmargin-bottom: 0;\n\t}\n\n\t// select2 4.1 specific targeting for plugin conflict resolution.\n\t&[data-select2-id^=\"select2-data\"] {\n\t\t.select2-selection--multiple {\n\t\t\toverflow: hidden;\n\t\t}\n\t}\n\n\t// Customize border color to match WP admin.\n\t.select2-selection {\n\t\tborder-color: $wp-input-border;\n\n\t\t// WP Admin 3.8\n\t\t@include wp-admin(\"3-8\") {\n\t\t\tborder-color: #aaa;\n\t\t}\n\t}\n\n\t// Multiple wrap.\n\t.select2-selection--multiple {\n\t\t// If no value, increase hidden search input full width.\n\t\t// Overrides calculated px width issues.\n\t\t.select2-search--inline:first-child {\n\t\t\tfloat: none;\n\t\t\tinput {\n\t\t\t\twidth: 100% !important;\n\t\t\t}\n\t\t}\n\n\t\t// ul: Remove padding because li already has margin-right.\n\t\t.select2-selection__rendered {\n\t\t\tpadding-right: 0;\n\t\t}\n\n\t\t// incredibly specific targeting of an ID that only gets applied in select2 4.1 to solve plugin conflicts\n\t\t.select2-selection__rendered[id^=\"select2-acf-field\"] {\n\t\t\tdisplay: inline;\n\t\t\tpadding: 0;\n\t\t\tmargin: 0;\n\n\t\t\t.select2-selection__choice {\n\t\t\t\tmargin-right: 0;\n\t\t\t}\n\t\t}\n\n\t\t// li\n\t\t.select2-selection__choice {\n\t\t\tbackground-color: #f7f7f7;\n\t\t\tborder-color: #cccccc;\n\n\t\t\t// Allow choice to wrap multiple lines.\n\t\t\tmax-width: 100%;\n\t\t\toverflow: hidden;\n\t\t\tword-wrap: normal !important;\n\t\t\twhite-space: normal;\n\n\t\t\t// Sortable.\n\t\t\t&.ui-sortable-helper {\n\t\t\t\tbackground: $blue-500;\n\t\t\t\tborder-color: $blue-600;\n\t\t\t\tcolor: #fff !important;\n\t\t\t\tbox-shadow: 0 0 3px rgba(0, 0, 0, 0.1);\n\n\t\t\t\tspan {\n\t\t\t\t\tvisibility: hidden;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Fixed for select2's 4.1 css changes when loaded by another plugin.\n\t\t\t.select2-selection__choice__remove {\n\t\t\t\tposition: static;\n\t\t\t\tborder-right: none;\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t\t// Sortable shadow\n\t\t\t&.ui-sortable-placeholder {\n\t\t\t\tbackground-color: $gray-100;\n\t\t\t\tborder-color: $gray-100;\n\t\t\t\tvisibility: visible !important;\n\t\t\t}\n\t\t}\n\n\t\t// search\n\t\t.select2-search__field {\n\t\t\tbox-shadow: none !important;\n\t\t\tmin-height: 0;\n\t\t}\n\t}\n\n\t// Fix single select pushing out repeater field table width.\n\t.acf-row & .select2-selection--single {\n\t\toverflow: hidden;\n\t\t.select2-selection__rendered {\n\t\t\twhite-space: normal;\n\t\t}\n\t}\n}\n\n.acf-admin-single-field-group .select2-dropdown {\n\tborder-color: $blue-300 !important;\n\tmargin-top: -5px;\n\toverflow: hidden;\n\tbox-shadow: $elevation-01;\n}\n\n.select2-dropdown.select2-dropdown--above {\n\tmargin-top: 0;\n}\n\n.acf-admin-single-field-group .select2-container--default .select2-results__option[aria-selected=\"true\"] {\n\tbackground-color: $gray-50 !important;\n\tcolor: $gray-500;\n\n\t&:hover {\n\t\tcolor: $blue-400;\n\t}\n}\n\n.acf-admin-single-field-group .select2-container--default\n\t.select2-results__option--highlighted[aria-selected] {\n\tcolor: #fff !important;\n\tbackground-color: $blue-500 !important;\n}\n\n// remove bottom margin on options\n.select2-dropdown .select2-results__option {\n\tmargin-bottom: 0;\n}\n\n// z-index helper.\n.select2-container {\n\t.select2-dropdown {\n\t\tz-index: 900000;\n\n\t\t// Reset input height.\n\t\t.select2-search__field {\n\t\t\tline-height: 1.4;\n\t\t\tmin-height: 0;\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Link\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-link {\n\t.link-wrap {\n\t\tdisplay: none;\n\t\tborder: $wp-card-border solid 1px;\n\t\tborder-radius: 3px;\n\t\tpadding: 5px;\n\t\tline-height: 26px;\n\t\tbackground: #fff;\n\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\n\t\t.link-title {\n\t\t\tpadding: 0 5px;\n\t\t}\n\t}\n\n\t// Has value.\n\t&.-value {\n\t\t.button {\n\t\t\tdisplay: none;\n\t\t}\n\t\t.acf-icon.-link-ext {\n\t\t\tdisplay: none;\n\t\t}\n\t\t.link-wrap {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n\n\t// Is external.\n\t&.-external {\n\t\t.acf-icon.-link-ext {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n}\n\n#wp-link-backdrop {\n\tz-index: 900000 !important;\n}\n#wp-link-wrap {\n\tz-index: 900001 !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Radio\n*\n*-----------------------------------------------------------------------------*/\n\nul.acf-radio-list,\nul.acf-checkbox-list {\n\tbackground: transparent;\n\tborder: 1px solid transparent;\n\tposition: relative;\n\tpadding: 1px;\n\tmargin: 0;\n\n\t&:focus-within {\n\t\tborder: 1px solid $blue-200;\n\t\tborder-radius: $radius-md;\n\t}\n\n\tli {\n\t\tfont-size: 13px;\n\t\tline-height: 22px;\n\t\tmargin: 0;\n\t\tposition: relative;\n\t\tword-wrap: break-word;\n\n\t\tlabel {\n\t\t\tdisplay: inline;\n\t\t}\n\n\t\tinput[type=\"checkbox\"],\n\t\tinput[type=\"radio\"] {\n\t\t\tmargin: -1px 4px 0 0;\n\t\t\tvertical-align: middle;\n\t\t}\n\n\t\tinput[type=\"text\"] {\n\t\t\twidth: auto;\n\t\t\tvertical-align: middle;\n\t\t\tmargin: 2px 0;\n\t\t}\n\n\t\t/* attachment sidebar fix*/\n\t\tspan {\n\t\t\tfloat: none;\n\t\t}\n\n\t\ti {\n\t\t\tvertical-align: middle;\n\t\t}\n\t}\n\n\t/* hl */\n\t&.acf-hl {\n\t\tli {\n\t\t\tmargin-right: 20px;\n\t\t\tclear: none;\n\t\t}\n\t}\n\n\t/* rtl */\n\thtml[dir=\"rtl\"] & {\n\t\tinput[type=\"checkbox\"],\n\t\tinput[type=\"radio\"] {\n\t\t\tmargin-left: 4px;\n\t\t\tmargin-right: 0;\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Button Group\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-button-group {\n\tdisplay: inline-block;\n\n\tlabel {\n\t\tdisplay: inline-block;\n\t\tborder: $wp-input-border solid 1px;\n\t\tposition: relative;\n\t\tz-index: 1;\n\t\tpadding: 5px 10px;\n\t\tbackground: #fff;\n\n\t\t&:hover {\n\t\t\tcolor: #016087;\n\t\t\tbackground: #f3f5f6;\n\t\t\tborder-color: #0071a1;\n\t\t\tz-index: 2;\n\t\t}\n\n\t\t&.selected {\n\t\t\tborder-color: #007cba;\n\t\t\tbackground: lighten(#007cba, 5%);\n\t\t\tcolor: #fff;\n\t\t\tz-index: 2;\n\t\t}\n\t}\n\n\tinput {\n\t\tdisplay: none !important;\n\t}\n\n\t/* default (horizontal) */\n\t& {\n\t\tpadding-left: 1px;\n\t\tdisplay: inline-flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: nowrap;\n\n\t\tlabel {\n\t\t\tmargin: 0 0 0 -1px;\n\t\t\tflex: 1;\n\t\t\ttext-align: center;\n\t\t\twhite-space: nowrap;\n\n\t\t\t// corners\n\t\t\t&:first-child {\n\t\t\t\tborder-radius: 3px 0 0 3px;\n\t\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\t\tborder-radius: 0 3px 3px 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t&:last-child {\n\t\t\t\tborder-radius: 0 3px 3px 0;\n\t\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\t\tborder-radius: 3px 0 0 3px;\n\t\t\t\t}\n\t\t\t}\n\t\t\t&:only-child {\n\t\t\t\tborder-radius: 3px;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* vertical */\n\t&.-vertical {\n\t\tpadding-left: 0;\n\t\tpadding-top: 1px;\n\t\tflex-direction: column;\n\n\t\tlabel {\n\t\t\tmargin: -1px 0 0 0;\n\n\t\t\t// corners\n\t\t\t&:first-child {\n\t\t\t\tborder-radius: 3px 3px 0 0;\n\t\t\t}\n\t\t\t&:last-child {\n\t\t\t\tborder-radius: 0 0 3px 3px;\n\t\t\t}\n\t\t\t&:only-child {\n\t\t\t\tborder-radius: 3px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin(\"3-8\") {\n\t\tlabel {\n\t\t\tborder-color: $wp-card-border;\n\t\t\t&:hover {\n\t\t\t\tborder-color: #0071a1;\n\t\t\t}\n\t\t\t&.selected {\n\t\t\t\tborder-color: #007cba;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.acf-admin-page {\n\t.acf-button-group {\n\t\tdisplay: flex;\n\t\talign-items: stretch;\n\t\talign-content: center;\n\t\theight: 40px;\n\t\tborder-radius: $radius-md;\n\t\tbox-shadow: $elevation-01;\n\n\t\tlabel {\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\talign-content: center;\n\t\t\tborder: $gray-300 solid 1px;\n\t\t\tpadding: 6px 16px;\n\t\t\tcolor: $gray-600;\n\t\t\tfont-weight: 500;\n\n\t\t\t&:hover {\n\t\t\t\tcolor: $color-primary;\n\t\t\t}\n\n\t\t\t&.selected {\n\t\t\t\tbackground: $gray-50;\n\t\t\t\tcolor: $color-primary;\n\t\t\t}\n\t\t}\n\t}\n\n\t.select2-container.-acf {\n\t\t.select2-selection--multiple {\n\t\t\t.select2-selection__choice {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 8px;\n\t\t\t\t\tleft: 2px;\n\t\t\t\t};\n\t\t\t\tposition: relative;\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 4px;\n\t\t\t\t\tright: auto;\n\t\t\t\t\tbottom: 4px;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t}\n\t\t\t\tbackground-color: $blue-50;\n\t\t\t\tborder-color: $blue-200;\n\t\t\t\tcolor: $blue-500;\n\n\t\t\t\t.select2-selection__choice__remove {\n\t\t\t\t\torder: 2;\n\t\t\t\t\twidth: 14px;\n\t\t\t\t\theight: 14px;\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: 4px;\n\t\t\t\t\t}\n\t\t\t\t\tcolor: $blue-300;\n\t\t\t\t\ttext-indent: 100%;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: $blue-500;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tcontent: \"\";\n\t\t\t\t\t\t$icon-size: 14px;\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\t\theight: $icon-size;\n\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\tbackground-color: currentColor;\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\t\t\tmask-size: contain;\n\t\t\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t\t\t-webkit-mask-position: center;\n\t\t\t\t\t\tmask-position: center;\n\t\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n\t\t\t\t\t\tmask-image: url(\"../../images/icons/icon-close.svg\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Checkbox\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-checkbox-list {\n\t.button {\n\t\tmargin: 10px 0 0;\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  True / False\n*\n*-----------------------------------------------------------------------------*/\n.acf-switch {\n\tdisplay: grid;\n\tgrid-template-columns: 1fr 1fr;\n\twidth: fit-content;\n\tmax-width: 100%;\n\tborder-radius: 5px;\n\tcursor: pointer;\n\tposition: relative;\n\tbackground: #f5f5f5;\n\theight: 30px;\n\tvertical-align: middle;\n\tborder: $wp-input-border solid 1px;\n\n\t-webkit-transition: background 0.25s ease;\n\t-moz-transition: background 0.25s ease;\n\t-o-transition: background 0.25s ease;\n\ttransition: background 0.25s ease;\n\n\tspan {\n\t\tdisplay: inline-block;\n\t\tfloat: left;\n\t\ttext-align: center;\n\n\t\tfont-size: 13px;\n\t\tline-height: 22px;\n\n\t\tpadding: 4px 10px;\n\t\tmin-width: 15px;\n\n\t\ti {\n\t\t\tvertical-align: middle;\n\t\t}\n\t}\n\n\t.acf-switch-on {\n\t\tcolor: #fff;\n\t\ttext-shadow: #007cba 0 1px 0;\n\t\toverflow: hidden;\n\t}\n\n\t.acf-switch-off {\n\t\toverflow: hidden;\n\t}\n\n\t.acf-switch-slider {\n\t\tposition: absolute;\n\t\ttop: 2px;\n\t\tleft: 2px;\n\t\tbottom: 2px;\n\t\tright: 50%;\n\t\tz-index: 1;\n\t\tbackground: #fff;\n\t\tborder-radius: 3px;\n\t\tborder: $wp-input-border solid 1px;\n\n\t\t-webkit-transition: all 0.25s ease;\n\t\t-moz-transition: all 0.25s ease;\n\t\t-o-transition: all 0.25s ease;\n\t\ttransition: all 0.25s ease;\n\n\t\ttransition-property: left, right;\n\t}\n\n\t/* hover */\n\t&:hover,\n\t&.-focus {\n\t\tborder-color: #0071a1;\n\t\tbackground: #f3f5f6;\n\t\tcolor: #016087;\n\t\t.acf-switch-slider {\n\t\t\tborder-color: #0071a1;\n\t\t}\n\t}\n\n\t/* active */\n\t&.-on {\n\t\tbackground: #0d99d5;\n\t\tborder-color: #007cba;\n\n\t\t.acf-switch-slider {\n\t\t\tleft: 50%;\n\t\t\tright: 2px;\n\t\t\tborder-color: #007cba;\n\t\t}\n\n\t\t/* hover */\n\t\t&:hover {\n\t\t\tborder-color: #007cba;\n\t\t}\n\t}\n\n\t/* message */\n\t+ span {\n\t\tmargin-left: 6px;\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin(\"3-8\") {\n\t\tborder-color: $wp-card-border;\n\t\t.acf-switch-slider {\n\t\t\tborder-color: $wp-card-border;\n\t\t}\n\n\t\t&:hover,\n\t\t&.-focus {\n\t\t\tborder-color: #0071a1;\n\t\t\t.acf-switch-slider {\n\t\t\t\tborder-color: #0071a1;\n\t\t\t}\n\t\t}\n\n\t\t&.-on {\n\t\t\tborder-color: #007cba;\n\t\t\t.acf-switch-slider {\n\t\t\t\tborder-color: #007cba;\n\t\t\t}\n\t\t\t&:hover {\n\t\t\t\tborder-color: #007cba;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* checkbox */\n.acf-switch-input {\n\topacity: 0;\n\tposition: absolute;\n\tmargin: 0;\n}\n\n.acf-admin-single-field-group .acf-true-false {\n\tborder: 1px solid transparent;\n\n\t&:focus-within {\n\t\tborder: 1px solid $blue-400;\n\t\tborder-radius: 120px;\n\t}\n}\n\n.acf-true-false:has(.acf-switch) {\n\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-items: center;\n\t}\n}\n\n/* in media modal */\n.compat-item .acf-true-false {\n\t.message {\n\t\tfloat: none;\n\t\tpadding: 0;\n\t\tvertical-align: middle;\n\t}\n}\n\n/*--------------------------------------------------------------------------\n*\n*\tGoogle Map\n*\n*-------------------------------------------------------------------------*/\n\n.acf-google-map {\n\tposition: relative;\n\tborder: $wp-card-border solid 1px;\n\tbackground: #fff;\n\n\t.title {\n\t\tposition: relative;\n\t\tborder-bottom: $wp-card-border solid 1px;\n\n\t\t.search {\n\t\t\tmargin: 0;\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 30px;\n\t\t\theight: 40px;\n\t\t\tpadding: 5px 10px;\n\t\t\tborder: 0 none;\n\t\t\tbox-shadow: none;\n\t\t\tborder-radius: 0;\n\t\t\tfont-family: inherit;\n\t\t\tcursor: text;\n\t\t}\n\n\t\t.acf-loading {\n\t\t\tposition: absolute;\n\t\t\ttop: 10px;\n\t\t\tright: 11px;\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t// Avoid icons disapearing when click/blur events conflict.\n\t\t.acf-icon:active {\n\t\t\tdisplay: inline-block !important;\n\t\t}\n\t}\n\n\t.canvas {\n\t\theight: 400px;\n\t}\n\n\t// Show actions on hover.\n\t&:hover .title .acf-actions {\n\t\tdisplay: block;\n\t}\n\n\t// Default state (show locate, hide search and cancel).\n\t.title {\n\t\t.acf-icon.-location {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t\t.acf-icon.-cancel,\n\t\t.acf-icon.-search {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t// Has value (hide locate, show cancel).\n\t&.-value .title {\n\t\t.search {\n\t\t\tfont-weight: bold;\n\t\t}\n\t\t.acf-icon.-location {\n\t\t\tdisplay: none;\n\t\t}\n\t\t.acf-icon.-cancel {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n\n\t// Is searching (hide locate, show search and cancel).\n\t&.-searching .title {\n\t\t.acf-icon.-location {\n\t\t\tdisplay: none;\n\t\t}\n\t\t.acf-icon.-cancel,\n\t\t.acf-icon.-search {\n\t\t\tdisplay: inline-block;\n\t\t}\n\n\t\t// Show actions.\n\t\t.acf-actions {\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t// Change search font-weght.\n\t\t.search {\n\t\t\tfont-weight: normal !important;\n\t\t}\n\t}\n\n\t// Loading.\n\t&.-loading .title {\n\t\ta {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\ti {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n}\n\n/* autocomplete */\n.pac-container {\n\tborder-width: 1px 0;\n\tbox-shadow: none;\n}\n\n.pac-container:after {\n\tdisplay: none;\n}\n\n.pac-container .pac-item:first-child {\n\tborder-top: 0 none;\n}\n.pac-container .pac-item {\n\tpadding: 5px 10px;\n\tcursor: pointer;\n}\n\nhtml[dir=\"rtl\"] .pac-container .pac-item {\n\ttext-align: right;\n}\n\n/*--------------------------------------------------------------------------\n*\n*\tRelationship\n*\n*-------------------------------------------------------------------------*/\n\n.acf-relationship {\n\tbackground: #fff;\n\tborder: $wp-card-border solid 1px;\n\n\t// Filters.\n\t.filters {\n\t\t@include clearfix();\n\t\tborder-bottom: $wp-card-border solid 1px;\n\t\tbackground: #fff;\n\n\t\t.filter {\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tfloat: left;\n\t\t\twidth: 100%;\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: 7px 7px 7px 0;\n\t\t\t&:first-child {\n\t\t\t\tpadding-left: 7px;\n\t\t\t}\n\n\t\t\t// inputs\n\t\t\tinput,\n\t\t\tselect {\n\t\t\t\tmargin: 0;\n\t\t\t\tfloat: none; /* potential fix for media popup? */\n\n\t\t\t\t&:focus,\n\t\t\t\t&:active {\n\t\t\t\t\toutline: none;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\t\t\tinput {\n\t\t\t\tborder-color: transparent;\n\t\t\t\tbox-shadow: none;\n\t\t\t\tpadding-left: 3px;\n\t\t\t\tpadding-right: 3px;\n\t\t\t}\n\t\t}\n\n\t\t/* widths */\n\t\t&.-f2 {\n\t\t\t.filter {\n\t\t\t\twidth: 50%;\n\t\t\t}\n\t\t}\n\t\t&.-f3 {\n\t\t\t.filter {\n\t\t\t\twidth: 25%;\n\t\t\t}\n\t\t\t.filter.-search {\n\t\t\t\twidth: 50%;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* list */\n\t.list {\n\t\tmargin: 0;\n\t\tpadding: 5px;\n\t\theight: 160px;\n\t\toverflow: auto;\n\n\t\t.acf-rel-label,\n\t\t.acf-rel-item,\n\t\tp {\n\t\t\tpadding: 5px;\n\t\t\tmargin: 0;\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\t\t\tmin-height: 18px;\n\t\t}\n\n\t\t.acf-rel-label {\n\t\t\tfont-weight: bold;\n\t\t}\n\n\t\t.acf-rel-item {\n\t\t\tcursor: pointer;\n\n\t\t\tb {\n\t\t\t\ttext-decoration: underline;\n\t\t\t\tfont-weight: normal;\n\t\t\t}\n\n\t\t\t.thumbnail {\n\t\t\t\tbackground: darken(#f9f9f9, 10%);\n\t\t\t\twidth: 22px;\n\t\t\t\theight: 22px;\n\t\t\t\tfloat: left;\n\t\t\t\tmargin: -2px 5px 0 0;\n\n\t\t\t\timg {\n\t\t\t\t\tmax-width: 22px;\n\t\t\t\t\tmax-height: 22px;\n\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t&.-icon {\n\t\t\t\t\tbackground: #fff;\n\n\t\t\t\t\timg {\n\t\t\t\t\t\tmax-height: 20px;\n\t\t\t\t\t\tmargin-top: 1px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* hover */\n\t\t\t&:hover, &.relationship-hover {\n\t\t\t\tbackground: #3875d7;\n\t\t\t\tcolor: #fff;\n\n\t\t\t\t.thumbnail {\n\t\t\t\t\tbackground: lighten(#3875d7, 25%);\n\n\t\t\t\t\t&.-icon {\n\t\t\t\t\t\tbackground: #fff;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* disabled */\n\t\t\t&.disabled {\n\t\t\t\topacity: 0.5;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground: transparent;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tcursor: default;\n\n\t\t\t\t\t.thumbnail {\n\t\t\t\t\t\tbackground: darken(#f9f9f9, 10%);\n\n\t\t\t\t\t\t&.-icon {\n\t\t\t\t\t\t\tbackground: #fff;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tul {\n\t\t\tpadding-bottom: 5px;\n\n\t\t\t.acf-rel-label,\n\t\t\t.acf-rel-item,\n\t\t\tp {\n\t\t\t\tpadding-left: 20px;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* selection (bottom) */\n\t.selection {\n\t\t@include clearfix();\n\t\tposition: relative;\n\n\t\t.values,\n\t\t.choices {\n\t\t\twidth: 50%;\n\t\t\tbackground: #fff;\n\t\t\tfloat: left;\n\t\t}\n\n\t\t/* choices */\n\t\t.choices {\n\t\t\tbackground: #f9f9f9;\n\n\t\t\t.list {\n\t\t\t\tborder-right: #dfdfdf solid 1px;\n\t\t\t}\n\t\t}\n\n\t\t/* values */\n\t\t.values {\n\t\t\t.acf-icon {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 4px;\n\t\t\t\tright: 7px;\n\t\t\t\tdisplay: none;\n\n\t\t\t\t/* rtl */\n\t\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\t\tright: auto;\n\t\t\t\t\tleft: 7px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.acf-rel-item:hover .acf-icon, .acf-rel-item.relationship-hover .acf-icon {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t\t.acf-rel-item {\n\t\t\t\tcursor: move;\n\n\t\t\t\tb {\n\t\t\t\t\ttext-decoration: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* menu item fix */\n.menu-item {\n\t.acf-relationship {\n\t\tul {\n\t\t\twidth: auto;\n\t\t}\n\n\t\tli {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------\n*\n*\tWYSIWYG\n*\n*-------------------------------------------------------------------------*/\n\n.acf-editor-wrap {\n\t// Delay.\n\t&.delay {\n\t\t.acf-editor-toolbar {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tbackground: #f5f5f5;\n\t\t\tborder-bottom: #dddddd solid 1px;\n\t\t\tcolor: #555d66;\n\t\t\tpadding: 10px;\n\t\t}\n\n\t\t.wp-editor-area {\n\t\t\tpadding: 10px;\n\t\t\tborder: none;\n\t\t\tcolor: inherit !important; // Fixes white text bug.\n\t\t}\n\t}\n\n\tiframe {\n\t\tmin-height: 200px;\n\t}\n\n\t.wp-editor-container {\n\t\tborder: 1px solid $wp-card-border;\n\t\tbox-shadow: none !important;\n\t}\n\n\t.wp-editor-tabs {\n\t\tbox-sizing: content-box;\n\t}\n\n\t.wp-switch-editor {\n\t\tborder-color: $wp-card-border;\n\t\tborder-bottom-color: transparent;\n\t}\n}\n\n// Full Screen Mode.\n#mce_fullscreen_container {\n\tz-index: 900000 !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tTab\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-field-tab {\n\tdisplay: none !important;\n}\n\n// class to hide fields\n.hidden-by-tab {\n\tdisplay: none !important;\n}\n\n// ensure floating fields do not disturb tab wrap\n.acf-tab-wrap {\n\tclear: both;\n\tz-index: 1;\n\toverflow: auto;\n}\n\n// tab group\n.acf-tab-group {\n\tborder-bottom: #ccc solid 1px;\n\tpadding: 10px 10px 0;\n\n\tli {\n\t\tmargin: 0 0.5em 0 0;\n\n\t\ta {\n\t\t\tpadding: 5px 10px;\n\t\t\tdisplay: block;\n\n\t\t\tcolor: #555;\n\t\t\tfont-size: 14px;\n\t\t\tfont-weight: 600;\n\t\t\tline-height: 24px;\n\n\t\t\tborder: #ccc solid 1px;\n\t\t\tborder-bottom: 0 none;\n\t\t\ttext-decoration: none;\n\t\t\tbackground: #e5e5e5;\n\t\t\ttransition: none;\n\n\t\t\t&:hover {\n\t\t\t\tbackground: #fff;\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\n\t\t\t&:empty {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\n\t\t// rtl\n\t\thtml[dir=\"rtl\"] & {\n\t\t\tmargin: 0 0 0 0.5em;\n\t\t}\n\n\t\t// active\n\t\t&.active a {\n\t\t\tbackground: #f1f1f1;\n\t\t\tcolor: #000;\n\t\t\tpadding-bottom: 6px;\n\t\t\tmargin-bottom: -1px;\n\t\t\tposition: relative;\n\t\t\tz-index: 1;\n\t\t}\n\t}\n}\n\n// inside acf-fields\n.acf-fields > .acf-tab-wrap {\n\tbackground: #f9f9f9;\n\n\t// group\n\t.acf-tab-group {\n\t\tposition: relative;\n\t\tborder-top: $wp-card-border solid 1px;\n\t\tborder-bottom: $wp-card-border solid 1px;\n\n\t\t// Pull next element (field) up and underneith.\n\t\tz-index: 2;\n\t\tmargin-bottom: -1px;\n\n\t\t// \t\tli a {\n\t\t// \t\t\tbackground: #f1f1f1;\n\t\t// \t\t\tborder-color: $wp-card-border;\n\t\t//\n\t\t// \t\t\t&:hover {\n\t\t// \t\t\t\tbackground: #FFF;\n\t\t// \t\t\t}\n\t\t// \t\t}\n\t\t//\n\t\t// \t\tli.active a {\n\t\t// \t\t\tbackground: #FFFFFF;\n\t\t// \t\t}\n\n\t\t// WP Admin 3.8\n\t\t@include wp-admin(\"3-8\") {\n\t\t\tborder-color: $wp38-card-border-1;\n\t\t}\n\t}\n\n\t// first child\n\t// fixes issue causing double border-top due to WP postbox .handlediv\n\t// &:first-child .acf-tab-group {\n\t// \tborder-top: none;\n\t// }\n}\n\n// inside acf-fields.-left\n.acf-fields.-left > .acf-tab-wrap {\n\t// group\n\t.acf-tab-group {\n\t\tpadding-left: 20%;\n\n\t\t/* mobile */\n\t\t@media screen and (max-width: $sm) {\n\t\t\tpadding-left: 10px;\n\t\t}\n\n\t\t/* rtl */\n\t\thtml[dir=\"rtl\"] & {\n\t\t\tpadding-left: 0;\n\t\t\tpadding-right: 20%;\n\n\t\t\t/* mobile */\n\t\t\t@media screen and (max-width: 850px) {\n\t\t\t\tpadding-right: 10px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// left\n.acf-tab-wrap.-left {\n\t// group\n\t.acf-tab-group {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\twidth: 20%;\n\t\tborder: 0 none;\n\t\tpadding: 0 !important; /* important overrides 'left aligned labels' */\n\t\tmargin: 1px 0 0;\n\n\t\t// li\n\t\tli {\n\t\t\tfloat: none;\n\t\t\tmargin: -1px 0 0;\n\n\t\t\ta {\n\t\t\t\tborder: 1px solid #ededed;\n\t\t\t\tfont-size: 13px;\n\t\t\t\tline-height: 18px;\n\t\t\t\tcolor: #0073aa;\n\t\t\t\tpadding: 10px;\n\t\t\t\tmargin: 0;\n\t\t\t\tfont-weight: normal;\n\t\t\t\tborder-width: 1px 0;\n\t\t\t\tborder-radius: 0;\n\t\t\t\tbackground: transparent;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tcolor: #00a0d2;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.active a {\n\t\t\t\tborder-color: #dfdfdf;\n\t\t\t\tcolor: #000;\n\t\t\t\tmargin-right: -1px;\n\t\t\t\tbackground: #fff;\n\t\t\t}\n\t\t}\n\n\t\t// rtl\n\t\thtml[dir=\"rtl\"] & {\n\t\t\tleft: auto;\n\t\t\tright: 0;\n\n\t\t\tli.active a {\n\t\t\t\tmargin-right: 0;\n\t\t\t\tmargin-left: -1px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// space before field\n\t.acf-field + &:before {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\tz-index: 1;\n\t\theight: 10px;\n\t\tborder-top: #dfdfdf solid 1px;\n\t\tborder-bottom: #dfdfdf solid 1px;\n\t\tmargin-bottom: -1px;\n\t}\n\n\t// first child has negative margin issues\n\t&:first-child {\n\t\t.acf-tab-group {\n\t\t\tli:first-child a {\n\t\t\t\tborder-top: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* sidebar */\n.acf-fields.-sidebar {\n\tpadding: 0 0 0 20% !important;\n\tposition: relative;\n\n\t/* before */\n\t&:before {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 20%;\n\t\tbottom: 0;\n\t\tborder-right: #dfdfdf solid 1px;\n\t\tbackground: #f9f9f9;\n\t\tz-index: 1;\n\t}\n\n\t/* rtl */\n\thtml[dir=\"rtl\"] & {\n\t\tpadding: 0 20% 0 0 !important;\n\n\t\t&:before {\n\t\t\tborder-left: #dfdfdf solid 1px;\n\t\t\tborder-right-width: 0;\n\t\t\tleft: auto;\n\t\t\tright: 0;\n\t\t}\n\t}\n\n\t// left\n\t&.-left {\n\t\tpadding: 0 0 0 180px !important;\n\n\t\t/* rtl */\n\t\thtml[dir=\"rtl\"] & {\n\t\t\tpadding: 0 180px 0 0 !important;\n\t\t}\n\n\t\t&:before {\n\t\t\tbackground: #f1f1f1;\n\t\t\tborder-color: #dfdfdf;\n\t\t\twidth: 180px;\n\t\t}\n\n\t\t> .acf-tab-wrap.-left .acf-tab-group {\n\t\t\twidth: 180px;\n\n\t\t\tli a {\n\t\t\t\tborder-color: #e4e4e4;\n\t\t\t}\n\n\t\t\tli.active a {\n\t\t\t\tbackground: #f9f9f9;\n\t\t\t}\n\t\t}\n\t}\n\n\t// fix double border\n\t> .acf-field-tab + .acf-field {\n\t\tborder-top: none;\n\t}\n}\n\n// clear\n.acf-fields.-clear > .acf-tab-wrap {\n\tbackground: transparent;\n\n\t// group\n\t.acf-tab-group {\n\t\tmargin-top: 0;\n\t\tborder-top: none;\n\t\tpadding-left: 0;\n\t\tpadding-right: 0;\n\n\t\tli a {\n\t\t\tbackground: #e5e5e5;\n\n\t\t\t&:hover {\n\t\t\t\tbackground: #fff;\n\t\t\t}\n\t\t}\n\n\t\tli.active a {\n\t\t\tbackground: #f1f1f1;\n\t\t}\n\t}\n}\n\n/* seamless */\n.acf-postbox.seamless {\n\t// sidebar\n\t> .acf-fields.-sidebar {\n\t\tmargin-left: 0 !important;\n\n\t\t&:before {\n\t\t\tbackground: transparent;\n\t\t}\n\t}\n\n\t// default\n\t> .acf-fields > .acf-tab-wrap {\n\t\tbackground: transparent;\n\t\tmargin-bottom: 10px;\n\t\tpadding-left: $fx;\n\t\tpadding-right: $fx;\n\n\t\t.acf-tab-group {\n\t\t\tborder-top: 0 none;\n\t\t\tborder-color: $wp-card-border;\n\n\t\t\tli a {\n\t\t\t\tbackground: #e5e5e5;\n\t\t\t\tborder-color: $wp-card-border;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground: #fff;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tli.active a {\n\t\t\t\tbackground: #f1f1f1;\n\t\t\t}\n\t\t}\n\t}\n\n\t// left tabs\n\t> .acf-fields > .acf-tab-wrap.-left {\n\t\t&:before {\n\t\t\tborder-top: none;\n\t\t\theight: auto;\n\t\t}\n\n\t\t.acf-tab-group {\n\t\t\tmargin-bottom: 0;\n\n\t\t\tli a {\n\t\t\t\tborder-width: 1px 0 1px 1px !important;\n\t\t\t\tborder-color: #cccccc;\n\t\t\t\tbackground: #e5e5e5;\n\t\t\t}\n\n\t\t\tli.active a {\n\t\t\t\tbackground: #f1f1f1;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// menu\n.menu-edit,\n.widget {\n\t.acf-fields.-clear > .acf-tab-wrap .acf-tab-group li {\n\t\ta {\n\t\t\tbackground: #f1f1f1;\n\t\t}\n\t\ta:hover,\n\t\t&.active a {\n\t\t\tbackground: #fff;\n\t\t}\n\t}\n}\n\n.compat-item .acf-tab-wrap td {\n\tdisplay: block;\n}\n\n/* within gallery sidebar */\n.acf-gallery-side .acf-tab-wrap {\n\tborder-top: 0 none !important;\n}\n\n.acf-gallery-side .acf-tab-wrap .acf-tab-group {\n\tmargin: 10px 0 !important;\n\tpadding: 0 !important;\n}\n\n.acf-gallery-side .acf-tab-group li.active a {\n\tbackground: #f9f9f9 !important;\n}\n\n/* withing widget */\n.widget .acf-tab-group {\n\tborder-bottom-color: #e8e8e8;\n}\n\n.widget .acf-tab-group li a {\n\tbackground: #f1f1f1;\n}\n\n.widget .acf-tab-group li.active a {\n\tbackground: #fff;\n}\n\n/* media popup (edit image) */\n.media-modal.acf-expanded\n\t.compat-attachment-fields\n\t> tbody\n\t> tr.acf-tab-wrap\n\t.acf-tab-group {\n\tpadding-left: 23%;\n\tborder-bottom-color: #dddddd;\n}\n\n/* table */\n\n.form-table > tbody > tr.acf-tab-wrap .acf-tab-group {\n\tpadding: 0 5px 0 210px;\n}\n\n/* rtl */\nhtml[dir=\"rtl\"] .form-table > tbody > tr.acf-tab-wrap .acf-tab-group {\n\tpadding: 0 210px 0 5px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\toembed\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-oembed {\n\tposition: relative;\n\tborder: $wp-card-border solid 1px;\n\tbackground: #fff;\n\n\t.title {\n\t\tposition: relative;\n\t\tborder-bottom: $wp-card-border solid 1px;\n\t\tpadding: 5px 10px;\n\n\t\t.input-search {\n\t\t\tmargin: 0;\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 30px;\n\t\t\theight: 30px;\n\t\t\tpadding: 0;\n\t\t\tborder: 0 none;\n\t\t\tbox-shadow: none;\n\t\t\tborder-radius: 0;\n\t\t\tfont-family: inherit;\n\t\t\tcursor: text;\n\t\t}\n\n\t\t.acf-actions {\n\t\t\tpadding: 6px;\n\t\t}\n\t}\n\n\t.canvas {\n\t\tposition: relative;\n\t\tmin-height: 250px;\n\t\tbackground: #f9f9f9;\n\n\t\t.canvas-media {\n\t\t\tposition: relative;\n\t\t\tz-index: 1;\n\t\t}\n\n\t\tiframe {\n\t\t\tdisplay: block;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\twidth: 100%;\n\t\t}\n\n\t\t.acf-icon.-picture {\n\t\t\t@include centered();\n\t\t\tz-index: 0;\n\n\t\t\theight: 42px;\n\t\t\twidth: 42px;\n\t\t\tfont-size: 42px;\n\t\t\tcolor: #999;\n\t\t}\n\n\t\t.acf-loading-overlay {\n\t\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\t}\n\n\t\t.canvas-error {\n\t\t\tposition: absolute;\n\t\t\ttop: 50%;\n\t\t\tleft: 0%;\n\t\t\tright: 0%;\n\t\t\tmargin: -9px 0 0 0;\n\t\t\ttext-align: center;\n\t\t\tdisplay: none;\n\n\t\t\tp {\n\t\t\t\tpadding: 8px;\n\t\t\t\tmargin: 0;\n\t\t\t\tdisplay: inline;\n\t\t\t}\n\t\t}\n\t}\n\n\t// has value\n\t&.has-value {\n\t\t.canvas {\n\t\t\tmin-height: 50px;\n\t\t}\n\n\t\t.input-search {\n\t\t\tfont-weight: bold;\n\t\t}\n\n\t\t.title:hover .acf-actions {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tImage\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-image-uploader {\n\t@include clearfix();\n\tposition: relative;\n\n\tp {\n\t\tmargin: 0;\n\t}\n\n\t/* image wrap*/\n\t.image-wrap {\n\t\tposition: relative;\n\t\tfloat: left;\n\n\t\timg {\n\t\t\tmax-width: 100%;\n\t\t\tmax-height: 100%;\n\t\t\twidth: auto;\n\t\t\theight: auto;\n\t\t\tdisplay: block;\n\t\t\tmin-width: 30px;\n\t\t\tmin-height: 30px;\n\t\t\tbackground: #f1f1f1;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\n\t\t\t/* svg */\n\t\t\t&[src$=\".svg\"] {\n\t\t\t\tmin-height: 100px;\n\t\t\t\tmin-width: 100px;\n\t\t\t}\n\t\t}\n\n\t\t/* hover */\n\t\t&:hover .acf-actions {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\n\t/* input */\n\tinput.button {\n\t\twidth: auto;\n\t}\n\n\t/* rtl */\n\thtml[dir=\"rtl\"] & {\n\t\t.image-wrap {\n\t\t\tfloat: right;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tFile\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-file-uploader {\n\tposition: relative;\n\n\tp {\n\t\tmargin: 0;\n\t}\n\n\t.file-wrap {\n\t\tborder: $wp-card-border solid 1px;\n\t\tmin-height: 84px;\n\t\tposition: relative;\n\t\tbackground: #fff;\n\t}\n\n\t.file-icon {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tbottom: 0;\n\t\tpadding: 10px;\n\t\tbackground: #f1f1f1;\n\t\tborder-right: $wp-card-border-1 solid 1px;\n\n\t\timg {\n\t\t\tdisplay: block;\n\t\t\tpadding: 0;\n\t\t\tmargin: 0;\n\t\t\tmax-width: 48px;\n\t\t}\n\t}\n\n\t.file-info {\n\t\tpadding: 10px;\n\t\tmargin-left: 69px;\n\n\t\tp {\n\t\t\tmargin: 0 0 2px;\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 1.4em;\n\t\t\tword-break: break-all;\n\t\t}\n\n\t\ta {\n\t\t\ttext-decoration: none;\n\t\t}\n\t}\n\n\t/* hover */\n\t&:hover .acf-actions {\n\t\tdisplay: block;\n\t}\n\n\t/* rtl */\n\thtml[dir=\"rtl\"] & {\n\t\t.file-icon {\n\t\t\tleft: auto;\n\t\t\tright: 0;\n\t\t\tborder-left: #e5e5e5 solid 1px;\n\t\t\tborder-right: none;\n\t\t}\n\n\t\t.file-info {\n\t\t\tmargin-right: 69px;\n\t\t\tmargin-left: 0;\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tDate Picker\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-ui-datepicker .ui-datepicker {\n\tz-index: 900000 !important;\n\n\t.ui-widget-header a {\n\t\tcursor: pointer;\n\t\ttransition: none;\n\t}\n}\n\n/* fix highlight state overriding hover / active */\n.acf-ui-datepicker .ui-state-highlight.ui-state-hover {\n\tborder: 1px solid #98b7e8 !important;\n\tbackground: #98b7e8 !important;\n\tfont-weight: normal !important;\n\tcolor: #ffffff !important;\n}\n\n.acf-ui-datepicker .ui-state-highlight.ui-state-active {\n\tborder: 1px solid #3875d7 !important;\n\tbackground: #3875d7 !important;\n\tfont-weight: normal !important;\n\tcolor: #ffffff !important;\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tSeparator field\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-field-separator {\n\t.acf-label {\n\t\tmargin-bottom: 0;\n\n\t\tlabel {\n\t\t\tfont-weight: normal;\n\t\t}\n\t}\n\n\t.acf-input {\n\t\tdisplay: none;\n\t}\n\n\t/* fields */\n\t.acf-fields > & {\n\t\tbackground: #f9f9f9;\n\t\tborder-bottom: 1px solid #dfdfdf;\n\t\tborder-top: 1px solid #dfdfdf;\n\t\tmargin-bottom: -1px;\n\t\tz-index: 2;\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tTaxonomy\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-taxonomy-field {\n\tposition: relative;\n\n\t.categorychecklist-holder {\n\t\tborder: $wp-card-border solid 1px;\n\t\tborder-radius: 3px;\n\t\tmax-height: 200px;\n\t\toverflow: auto;\n\t}\n\n\t.acf-checkbox-list {\n\t\tmargin: 0;\n\t\tpadding: 10px;\n\n\t\tul.children {\n\t\t\tpadding-left: 18px;\n\t\t}\n\t}\n\n\t/* hover */\n\t&:hover {\n\t\t.acf-actions {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\n\t/* select */\n\t&[data-ftype=\"select\"] {\n\t\t.acf-actions {\n\t\t\tpadding: 0;\n\t\t\tmargin: -9px;\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tRange\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-range-wrap {\n\t.acf-append,\n\t.acf-prepend {\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tline-height: 28px;\n\t\tmargin: 0 7px 0 0;\n\t}\n\n\t.acf-append {\n\t\tmargin: 0 0 0 7px;\n\t}\n\n\tinput[type=\"range\"] {\n\t\tdisplay: inline-block;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\tvertical-align: middle;\n\t\theight: 28px;\n\n\t\t&:focus {\n\t\t\toutline: none;\n\t\t}\n\t}\n\n\tinput[type=\"number\"] {\n\t\tdisplay: inline-block;\n\t\tmin-width: 5em;\n\t\tpadding-right: 4px;\n\t\tmargin-left: 10px;\n\t\tvertical-align: middle;\n\t}\n\n\t/* rtl */\n\thtml[dir=\"rtl\"] & {\n\t\tinput[type=\"number\"] {\n\t\t\tmargin-right: 10px;\n\t\t\tmargin-left: 0;\n\t\t}\n\n\t\t.acf-append {\n\t\t\tmargin: 0 7px 0 0;\n\t\t}\n\t\t.acf-prepend {\n\t\t\tmargin: 0 0 0 7px;\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  acf-accordion\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-accordion {\n\tmargin: -1px 0;\n\tpadding: 0;\n\tbackground: #fff;\n\tborder-top: 1px solid $wp-card-border-1;\n\tborder-bottom: 1px solid $wp-card-border-1;\n\tz-index: 1; // Display above following field.\n\n\t// Title.\n\t.acf-accordion-title {\n\t\tmargin: 0;\n\t\tpadding: 12px;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t\tfont-size: inherit;\n\t\tfont-size: 13px;\n\t\tline-height: 1.4em;\n\n\t\t&:hover {\n\t\t\tbackground: #f3f4f5;\n\t\t}\n\n\t\tlabel {\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 1.4em;\n\t\t}\n\n\t\tp {\n\t\t\tfont-weight: normal;\n\t\t}\n\n\t\t.acf-accordion-icon {\n\t\t\tfloat: right;\n\t\t}\n\n\t\t// Gutenberg uses SVG.\n\t\tsvg.acf-accordion-icon {\n\t\t\tposition: absolute;\n\t\t\tright: 10px;\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%);\n\t\t\tcolor: #191e23;\n\t\t\tfill: currentColor;\n\t\t}\n\t}\n\n\t.acf-accordion-content {\n\t\tmargin: 0;\n\t\tpadding: 0 12px 12px;\n\t\tdisplay: none;\n\t}\n\n\t// Open.\n\t&.-open {\n\t\t> .acf-accordion-content {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n// Field specific overrides\n.acf-field.acf-accordion {\n\tmargin: -1px 0;\n\tpadding: 0 !important; // !important needed to avoid Gutenberg sidebar issues.\n\tborder-color: $wp-card-border-1;\n\n\t.acf-label.acf-accordion-title {\n\t\tpadding: 12px;\n\t\twidth: auto;\n\t\tfloat: none;\n\t\twidth: auto;\n\t}\n\n\t.acf-input.acf-accordion-content {\n\t\tpadding: 0;\n\t\tfloat: none;\n\t\twidth: auto;\n\n\t\t> .acf-fields {\n\t\t\tborder-top: $wp-card-border-2 solid 1px;\n\n\t\t\t&.-clear {\n\t\t\t\tpadding: 0 $fx $fy;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* field specific (left) */\n.acf-fields.-left > .acf-field.acf-accordion {\n\t&:before {\n\t\tdisplay: none;\n\t}\n\n\t.acf-accordion-title {\n\t\twidth: auto;\n\t\tmargin: 0 !important;\n\t\tpadding: 12px;\n\t\tfloat: none !important;\n\t}\n\n\t.acf-accordion-content {\n\t\tpadding: 0 !important;\n\t}\n}\n\n/* field specific (clear) */\n.acf-fields.-clear > .acf-field.acf-accordion {\n\tborder: #cccccc solid 1px;\n\tbackground: transparent;\n\n\t+ .acf-field.acf-accordion {\n\t\tmargin-top: -16px;\n\t}\n}\n\n/* table */\ntr.acf-field.acf-accordion {\n\tbackground: transparent;\n\n\t> .acf-input {\n\t\tpadding: 0 !important;\n\t\tborder: #cccccc solid 1px;\n\t}\n\n\t.acf-accordion-content {\n\t\tpadding: 0 12px 12px;\n\t}\n}\n\n/* #addtag */\n#addtag div.acf-field.error {\n\tborder: 0 none;\n\tpadding: 8px 0;\n}\n\n#addtag > .acf-field.acf-accordion {\n\tpadding-right: 0;\n\tmargin-right: 5%;\n\n\t+ p.submit {\n\t\tmargin-top: 0;\n\t}\n}\n\n/* border */\ntr.acf-accordion {\n\tmargin: 15px 0 !important;\n\n\t+ tr.acf-accordion {\n\t\tmargin-top: -16px !important;\n\t}\n}\n\n/* seamless */\n.acf-postbox.seamless > .acf-fields > .acf-accordion {\n\tmargin-left: $field_padding_x;\n\tmargin-right: $field_padding_x;\n\tborder: $wp-card-border solid 1px;\n}\n\n/* rtl */\nhtml[dir=\"rtl\"] .acf-accordion {\n}\n\n/* menu item */\n/*\n.menu-item-settings > .field-acf > .acf-field.acf-accordion {\n\tborder: #dfdfdf solid 1px;\n\tmargin: 10px -13px 10px -11px;\n\n\t+ .acf-field.acf-accordion {\n\t\tmargin-top: -11px;\n\t}\n}\n*/\n\n/* widget */\n.widget .widget-content > .acf-field.acf-accordion {\n\tborder: #dfdfdf solid 1px;\n\tmargin-bottom: 10px;\n\n\t.acf-accordion-title {\n\t\tmargin-bottom: 0;\n\t}\n\n\t+ .acf-field.acf-accordion {\n\t\tmargin-top: -11px;\n\t}\n}\n\n// media modal\n.media-modal .compat-attachment-fields .acf-field.acf-accordion {\n\t// siblings\n\t+ .acf-field.acf-accordion {\n\t\tmargin-top: -1px;\n\t}\n\n\t// input\n\t> .acf-input {\n\t\twidth: 100%;\n\t}\n\n\t// table\n\t.compat-attachment-fields > tbody > tr > td {\n\t\tpadding-bottom: 5px;\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*\tBlock Editor\n*\n*-----------------------------------------------------------------------------*/\n.block-editor {\n\t// Sidebar\n\t.edit-post-sidebar {\n\t\t// Remove metabox hndle border to simulate component panel.\n\t\t.acf-postbox {\n\t\t\t> .postbox-header,\n\t\t\t> .hndle {\n\t\t\t\tborder-bottom-width: 0 !important;\n\t\t\t}\n\t\t\t&.closed {\n\t\t\t\t> .postbox-header,\n\t\t\t\t> .hndle {\n\t\t\t\t\tborder-bottom-width: 1px !important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Field wrap.\n\t\t.acf-fields {\n\t\t\tmin-height: 1px;\n\t\t\toverflow: auto; // Fixes margin-collapse issue in WP 5.3.\n\n\t\t\t> .acf-field {\n\t\t\t\tborder-width: 0;\n\t\t\t\tborder-color: #e2e4e7;\n\t\t\t\tmargin: 0px;\n\t\t\t\tpadding: 10px 16px;\n\n\t\t\t\t// Force full width.\n\t\t\t\twidth: auto !important;\n\t\t\t\tmin-height: 0 !important;\n\t\t\t\tfloat: none !important;\n\n\t\t\t\t// Field labels.\n\t\t\t\t> .acf-label {\n\t\t\t\t\tmargin-bottom: 5px;\n\t\t\t\t\tlabel {\n\t\t\t\t\t\tfont-weight: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Accordions.\n\t\t\t\t&.acf-accordion {\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tborder-top-width: 1px;\n\n\t\t\t\t\t&:first-child {\n\t\t\t\t\t\tborder-top-width: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.acf-accordion-title {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tpadding: 15px;\n\t\t\t\t\t\tlabel {\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tcolor: rgb(30, 30, 30);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tsvg.acf-accordion-icon {\n\t\t\t\t\t\t\tright: 16px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.acf-accordion-content {\n\t\t\t\t\t\t> .acf-fields {\n\t\t\t\t\t\t\tborder-top-width: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.block-editor-block-inspector{\n\t\t\t// The Top level notice for all fields.\n\t\t\t.acf-fields > .acf-notice {\n\t\t\t\tdisplay: grid;\n\t\t\t\tgrid-template-columns: 1fr 25px;\n\t\t\t\tpadding: 10px;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t\t.acf-fields > .acf-notice p:last-of-type {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t\t.acf-fields > .acf-notice > .acf-notice-dismiss {\n\t\t\t\tposition: relative;\n\t\t\t\ttop: unset;\n\t\t\t\tright: unset;\n\t\t\t}\n\n\t\t\t// The notice below each field.\n\t\t\t.acf-fields .acf-field .acf-notice {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t\t.acf-fields .acf-error {\n\t\t\t\tmargin-bottom: 10px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Prefix field label & prefix field names\n*\n*-----------------------------------------------------------------------------*/\n.acf-field-setting-prefix_label,\n.acf-field-setting-prefix_name {\n\tp.description {\n\t\torder: 3;\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tleft: 16px;\n\t\t}\n\n\t\tcode {\n\t\t\tpadding: {\n\t\t\t\ttop: 4px;\n\t\t\t\tright: 6px;\n\t\t\t\tbottom: 4px;\n\t\t\t\tleft: 6px;\n\t\t\t}\n\t\t\tbackground-color: $gray-100;\n\t\t\tborder-radius: 4px;\n\t\t\t@extend .p7;\n\t\t\tcolor: $gray-500;\n\t\t}\n\t}\n}\n\n/*-----------------------------------------------------------------------------\n*\n*  Editor tab styles\n*\n*-----------------------------------------------------------------------------*/\n\n.acf-fields > .acf-tab-wrap:first-child .acf-tab-group {\n\tborder-top: none;\n}\n\n.acf-fields > .acf-tab-wrap .acf-tab-group li.active a {\n\tbackground: #ffffff;\n}\n\n.acf-fields > .acf-tab-wrap .acf-tab-group li a {\n\tbackground: #f1f1f1;\n\tborder-color: #ccd0d4;\n}\n\n.acf-fields > .acf-tab-wrap .acf-tab-group li a:hover {\n\tbackground: #fff;\n}\n", "/*--------------------------------------------------------------------------------------------\n*\n*\tUser\n*\n*--------------------------------------------------------------------------------------------*/\n\n.form-table > tbody {\n\n\t/* field */\n\t> .acf-field {\n\n\t\t/* label */\n\t\t> .acf-label {\n\t\t\tpadding: 20px 10px 20px 0;\n\t\t    width: 210px;\n\n\t\t    /* rtl */\n\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\tpadding: 20px 0 20px 10px;\n\t\t\t}\n\n\t\t    label {\n\t\t\t\tfont-size: 14px;\n\t\t\t\tcolor: #23282d;\n\t\t\t}\n\n\t\t}\n\n\n\t\t/* input */\n\t\t> .acf-input {\n\t\t\tpadding: 15px 10px;\n\n\t\t\t/* rtl */\n\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\tpadding: 15px 10px 15px 5%;\n\t\t\t}\n\t\t}\n\n\t}\n\n\n\t/* tab wrap */\n\t> .acf-tab-wrap td {\n\t\tpadding: 15px 5% 15px 0;\n\n\t\t/* rtl */\n\t\thtml[dir=\"rtl\"] & {\n\t\t\tpadding: 15px 0 15px 5%;\n\t\t}\n\n\t}\n\n\n\t/* misc */\n\t.form-table th.acf-th {\n\t\twidth: auto;\n\t}\n\n}\n\n#your-profile,\n#createuser {\n\n\t/* override for user css */\n\t.acf-field input[type=\"text\"],\n\t.acf-field input[type=\"password\"],\n\t.acf-field input[type=\"number\"],\n\t.acf-field input[type=\"search\"],\n\t.acf-field input[type=\"email\"],\n\t.acf-field input[type=\"url\"],\n\t.acf-field select {\n\t    max-width: 25em;\n\t}\n\n\t.acf-field textarea {\n\t\tmax-width: 500px;\n\t}\n\n\n\t/* allow sub fields to display correctly */\n\t.acf-field .acf-field input[type=\"text\"],\n\t.acf-field .acf-field input[type=\"password\"],\n\t.acf-field .acf-field input[type=\"number\"],\n\t.acf-field .acf-field input[type=\"search\"],\n\t.acf-field .acf-field input[type=\"email\"],\n\t.acf-field .acf-field input[type=\"url\"],\n\t.acf-field .acf-field textarea,\n\t.acf-field .acf-field select {\n\t    max-width: none;\n\t}\n}\n\n#registerform {\n\n\th2 {\n\t\tmargin: 1em 0;\n\t}\n\n\t.acf-field  {\n\t\tmargin-top: 0;\n\n\t\t.acf-label {\n\t\t\tmargin-bottom: 0;\n\n\t\t\tlabel {\n\t\t\t\tfont-weight: normal;\n\t\t\t\tline-height: 1.5;\n\t\t\t}\n\t\t}\n\n/*\n\t\t.acf-input {\n\t\t\tinput {\n\t\t\t\tfont-size: 24px;\n\t\t\t\tpadding: 5px;\n\t\t\t\theight: auto;\n\t\t\t}\n\t\t}\n*/\n\t}\n\n\tp.submit {\n\t\ttext-align: right;\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tTerm\n*\n*--------------------------------------------------------------------------------------------*/\n\n// add term\n#acf-term-fields {\n\tpadding-right: 5%;\n\n\t> .acf-field {\n\n\t\t> .acf-label {\n\t\t\tmargin: 0;\n\n\t\t\tlabel {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tfont-weight: normal;\n\t\t\t}\n\t\t}\n\t}\n\n}\n\np.submit .spinner,\np.submit .acf-spinner {\n\tvertical-align: top;\n\tfloat: none;\n\tmargin: 4px 4px 0;\n}\n\n\n// edit term\n#edittag .acf-fields.-left {\n\n\t> .acf-field {\n\t\tpadding-left: 220px;\n\n\t\t&:before {\n\t\t\twidth: 209px;\n\t\t}\n\n\t\t> .acf-label {\n\t\t\twidth: 220px;\n\t\t\tmargin-left: -220px;\n\t\t\tpadding: 0 10px;\n\t\t}\n\n\t\t> .acf-input {\n\t\t\tpadding: 0;\n\t\t}\n\t}\n}\n\n#edittag > .acf-fields.-left {\n\twidth: 96%;\n\n\t> .acf-field {\n\n\t\t> .acf-label {\n\t\t\tpadding-left: 0;\n\t\t}\n\t}\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tComment\n*\n*--------------------------------------------------------------------------------------------*/\n\n.editcomment td:first-child {\n    white-space: nowrap;\n    width: 131px;\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tWidget\n*\n*--------------------------------------------------------------------------------------------*/\n\n#widgets-right .widget .acf-field .description {\n\tpadding-left: 0;\n\tpadding-right: 0;\n}\n\n.acf-widget-fields {\n\n\t> .acf-field {\n\n\t\t.acf-label {\n\t\t\tmargin-bottom: 5px;\n\n\t\t\tlabel {\n\t\t\t\tfont-weight: normal;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tNav Menu\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-menu-settings {\n\tborder-top: 1px solid #eee;\n    margin-top: 2em;\n\n\t// seamless\n\t&.-seamless {\n\t\tborder-top: none;\n\t\tmargin-top: 15px;\n\n\t\t> h2 { display: none; }\n\t}\n\n\t// Fix relationship conflict.\n\t.list li {\n\t\tdisplay: block;\n\t\tmargin-bottom: 0;\n\t}\n}\n\n.acf-fields.acf-menu-item-fields {\n\tclear: both;\n\tpadding-top: 1px; // Fixes margin overlap.\n\n\t> .acf-field {\n\t\tmargin: 5px 0;\n\t\tpadding-right: 10px;\n\n\t\t.acf-label {\n\t\t\tmargin-bottom: 0;\n\t\t\tlabel {\n\t\t\t\tfont-style: italic;\n\t\t\t\tfont-weight: normal;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Attachment Form (single)\n*\n*---------------------------------------------------------------------------------------------*/\n\n#post .compat-attachment-fields {\n\n\t.compat-field-acf-form-data {\n\t\tdisplay: none;\n\t}\n\n\t&,\n\t> tbody,\n\t> tbody > tr,\n\t> tbody > tr > th,\n\t> tbody > tr > td {\n\t\tdisplay: block;\n\t}\n\n\t> tbody > .acf-field {\n\t\tmargin: 15px 0;\n\n\t\t> .acf-label {\n\t\t\tmargin: 0;\n\n\t\t\tlabel {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\n\t\t\t\tp {\n\t\t\t\t\tmargin: 0 0 3px !important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t> .acf-input {\n\t\t\tmargin: 0;\n\t\t}\n\t}\n}\n\n", "/*---------------------------------------------------------------------------------------------\n*\n*  Media Model\n*\n*---------------------------------------------------------------------------------------------*/\n\n/* WP sets tables to act as divs. ACF uses tables, so these muct be reset */\n.media-modal .compat-attachment-fields td.acf-input {\n\t\n\ttable {\n\t\tdisplay: table;\n\t\ttable-layout: auto;\n\t\t\n\t\ttbody {\n\t\t\tdisplay: table-row-group;\n\t\t}\n\t\t\n\t\ttr {\n\t\t\tdisplay: table-row;\n\t\t}\n\t\t\n\t\ttd, th {\n\t\t\tdisplay: table-cell;\n\t\t}\n\t\t\n\t}\n\t\n}\n\n\n/* field widths floats */\n.media-modal .compat-attachment-fields > tbody > .acf-field {\n\tmargin: 5px 0;\n\t\n\t> .acf-label {\n\t\tmin-width: 30%;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\tfloat: left;\n\t    text-align: right;\n\t    display: block;\n\t    float: left;\n\t    \n\t    > label {\n\t\t    padding-top: 6px;\n\t\t\tmargin: 0;\n\t\t\tcolor: #666666;\n\t\t    font-weight: 400;\n\t\t    line-height: 16px;\n\t    }\n\t}\n\t\n\t> .acf-input {\n\t\twidth: 65%;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t    float: right;\n\t    display: block;\n\t}\n\t\n\tp.description {\n\t\tmargin: 0;\n\t}\n}\n\n\n/* restricted selection (copy of WP .upload-errors)*/\n.acf-selection-error {\n\tbackground: #ffebe8;\n    border: 1px solid #c00;\n    border-radius: 3px;\n    padding: 8px;\n    margin: 20px 0 0;\n    \n    .selection-error-label {\n\t\tbackground: #CC0000;\n\t    border-radius: 3px;\n\t    color: #fff;\n\t    font-weight: bold;\n\t    margin-right: 8px;\n\t    padding: 2px 4px;\n\t}\n\t\n\t.selection-error-message {\n\t\tcolor: #b44;\n\t    display: block;\n\t    padding-top: 8px;\n\t    word-wrap: break-word;\n\t    white-space: pre-wrap;\n\t}\n}\n\n\n/* disabled attachment */\n.media-modal .attachment.acf-disabled {\n\t\n\t.thumbnail {\n\t\topacity: 0.25 !important;\n\t}\n\t\t\n\t.attachment-preview:before {\n\t\tbackground: rgba(0,0,0,0.15);\n\t\tz-index: 1;\n\t\tposition: relative;\n\t}\n\n}\n\n\n/* misc */\n.media-modal {\n\t\n\t/* compat-item */\n\t.compat-field-acf-form-data,\n\t.compat-field-acf-blank {\n\t\tdisplay: none !important;\n\t}\n\t\n\t\n\t/* allow line breaks in upload error */\n\t.upload-error-message {\n\t\twhite-space: pre-wrap;\n\t}\n\t\n\t\n\t/* fix required span */\n\t.acf-required {\n\t\tpadding: 0 !important;\n\t\tmargin: 0 !important;\n\t\tfloat: none !important;\n\t\tcolor: #f00 !important;\n\t}\n\t\n\t\n\t/* sidebar */\n\t.media-sidebar {\n\t\t\n\t\t.compat-item{\n\t\t\tpadding-bottom: 20px;\n\t\t}\n\t\t\n\t}\n\t\n\t\n\t/* mobile md */\n\t@media (max-width: 900px) {\n\t\t\n\t\t/* label */\n\t\t.setting span, \n\t\t.compat-attachment-fields > tbody > .acf-field > .acf-label {\n\t\t\twidth: 98%;\n\t\t\tfloat: none;\n\t\t\ttext-align: left;\n\t\t\tmin-height: 0;\n\t\t\tpadding: 0;\n\t\t}\n\t\t\n\t\t\n\t\t/* field */\n\t\t.setting input, \n\t\t.setting textarea, \n\t\t.compat-attachment-fields > tbody > .acf-field > .acf-input {\n\t\t\tfloat: none;\n\t\t    height: auto;\n\t\t    max-width: none;\n\t\t    width: 98%;\n\t\t}\n\n\t}\n\n\t\n}\n\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Media Model (expand details)\n*\n*---------------------------------------------------------------------------------------------*/\n\n.media-modal .acf-expand-details {\n\tfloat: right;\n\tpadding: 8px 10px;\n\tmargin-right: 6px;\n\tfont-size: 13px;\n\theight: 18px;\n\tline-height: 18px;\n\tcolor: #666;\n\ttext-decoration: none;\n\n\t// States.\n\t&:focus, &:active {\n\t\toutline: 0 none;\n\t\tbox-shadow: none;\n\t\tcolor: #666;\n\t}\n\t&:hover {\n\t\tcolor: #000;\n\t}\n\t\n\t// Open & close.\n\t.is-open { display: none; }\n\t.is-closed { display: block; }\n\t\n\t// Hide on mobile.\n\t@media (max-width: $sm) {\n\t\tdisplay: none;\n\t}\n}\n\n\n/* expanded */\n.media-modal.acf-expanded {\n\t\n\t/* toggle */\n\t.acf-expand-details {\n\t\t.is-open { display: block; }\n\t\t.is-closed { display: none; }\n\t\t\n\t}\n\t\n\t// Components.\n\t.attachments-browser .media-toolbar, \n\t.attachments-browser .attachments { right: 740px; }\n\t.media-sidebar { width: 708px; }\n\t\n\t// Sidebar.\n\t.media-sidebar {\n\t\t\n\t\t// Attachment info.\n\t\t.attachment-info {\n\t\t\t.thumbnail {\n\t\t\t\tfloat: left;\n\t\t\t\tmax-height: none;\n\n\t\t\t\timg {\n\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\tmax-height: 200px;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.details {\n\t\t\t\tfloat: right;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// Label\n\t\t.attachment-info .thumbnail,\n\t\t.attachment-details .setting .name, \n\t\t.compat-attachment-fields > tbody > .acf-field > .acf-label {\n\t\t\tmin-width: 20%;\n\t\t\tmargin-right: 0;\n\t\t}\n\t\t\n\t\t// Input\n\t\t.attachment-info .details,\n\t\t.attachment-details .setting input, \n\t\t.attachment-details .setting textarea,\n\t\t.attachment-details .setting + .description,\n\t\t.compat-attachment-fields > tbody > .acf-field > .acf-input {\n\t\t\tmin-width: 77%;\n\t\t}\n\t}\n\t\n\t// Screen: Medium.\n\t@media (max-width: 900px) {\n\t\t\n\t\t// Components.\n\t\t.attachments-browser .media-toolbar { display: none; }\n\t\t.attachments { display: none; }\n\t\t.media-sidebar { width: auto; max-width: none !important; bottom: 0 !important; }\n\t\t\n\t\t// Sidebar.\n\t\t.media-sidebar {\n\t\t\t\n\t\t\t// Attachment info.\n\t\t\t.attachment-info {\n\t\t\t\t.thumbnail {\n\t\t\t\t\tmin-width: 0;\n\t\t\t\t\tmax-width: none;\n\t\t\t\t\twidth: 30%;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.details {\n\t\t\t\t\tmin-width: 0;\n\t\t\t\t\tmax-width: none;\n\t\t\t\t\twidth: 67%;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t}\t\n\t\t}\n\t}\n\t\n\t// Screen: small.\n\t@media (max-width: 640px) {\n\t\t\n\t\t// Sidebar.\n\t\t.media-sidebar {\n\t\t\t\n\t\t\t// Attachment info.\n\t\t\t.attachment-info {\n\t\t\t\t.thumbnail, .details {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\t\t\t}\t\n\t\t}\n\t}\n}\n\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Media Model\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-media-modal {\n\t\n\t/* hide embed settings */\n\t.media-embed {\n\t\t.setting.align,\n\t\t.setting.link-to {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Media Model (Select Mode)\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-media-modal.-select {\n\t\n\t\n\t\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Media Model (Edit Mode)\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-media-modal.-edit {\n\t\n\t/* resize modal */\n\tleft: 15%;\n\tright: 15%;\n\ttop: 100px;\n\tbottom: 100px;\n\t\n\t\n\t/* hide elements */\n\t.media-frame-menu,\n\t.media-frame-router,\n\t.media-frame-content .attachments,\n\t.media-frame-content .media-toolbar {\n\t    display: none;\n\t}\n\t\n\t\n\t/* full width */\n\t.media-frame-title,\n\t.media-frame-content,\n\t.media-frame-toolbar,\n\t.media-sidebar {\n\t\twidth: auto;\n\t\tleft: 0;\n\t\tright: 0;\n\t}\n\t\n\t\n\t/* tidy up incorrect distance */\n\t.media-frame-content {\n\t    top: 50px;\n\t}\n\t\n\t\n\t/* title box shadow (to match media grid) */\n\t.media-frame-title {\n\t    border-bottom: 1px solid #DFDFDF;\n\t    box-shadow: 0 4px 4px -4px rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t\n\t/* sidebar */\n\t.media-sidebar {\n\t\t\n\t\tpadding: 0 16px;\n\t\t\n\t\t/* WP details */\n\t\t.attachment-details {\n\t\t\t\n\t\t\toverflow: visible;\n\t\t\t\n\t\t\t/* hide 'Attachment Details' heading */\n\t\t\t> h3, > h2 {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t\t\n\t\t\t/* remove overflow */\n\t\t\t.attachment-info {\n\t\t\t\tbackground: #fff;\n\t\t\t\tborder-bottom: #dddddd solid 1px;\n\t\t\t\tpadding: 16px;\n\t\t\t\tmargin: 0 -16px 16px;\n\t\t\t}\n\t\t\t\n\t\t\t/* move thumbnail */\n\t\t\t.thumbnail {\n\t\t\t\tmargin: 0 16px 0 0;\n\t\t\t}\n\t\t\t\n\t\t\t.setting {\n\t\t\t\tmargin: 0 0 5px;\n\t\t\t\t\n\t\t\t\tspan {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t\t\n\t\t/* ACF fields */\n\t\t.compat-attachment-fields {\n\t\t\t\n\t\t\t> tbody > .acf-field {\n\t\t\t\tmargin: 0 0 5px;\n\t\t\t\t\n\t\t\t\tp.description {\n\t\t\t\t\tmargin-top: 3px;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t\t\n\t\t/* WP required message */\n\t\t.media-types-required-info { display: none; }\n\t\t\n\t}\n\t\n\t\n\t/* mobile md */\n\t@media (max-width: 900px) {\n\t\ttop: 30px;\n\t\tright: 30px;\n\t\tbottom: 30px;\n\t\tleft: 30px;\n\t}\n\t\n\t\n\t/* mobile sm */\n\t@media (max-width: 640px) {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t}\n\t\n\t@media (max-width: 480px) {\n\t\t.media-frame-content {\n\t\t    top: 40px;\n\t\t}\n\t}\n}\n", "// Temp remove.\n.acf-temp-remove {\n\tposition: relative;\n\topacity: 1;\n\t-webkit-transition: all 0.25s ease;\n\t-moz-transition: all 0.25s ease;\n\t-o-transition: all 0.25s ease;\n\ttransition: all 0.25s ease;\n\toverflow: hidden;\n\t\n\t/* overlay prevents hover */\n\t&:after {\n\t\tdisplay: block;\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 99;\n\t}\n}\n\n// Conditional Logic.\n.hidden-by-conditional-logic {\n\tdisplay: none !important;\n\t\n\t// Table cells may \"appear empty\".\n\t&.appear-empty {\n\t\tdisplay: table-cell !important;\n\t\t.acf-input {\n\t\t\tdisplay: none !important;\n\t\t}\n\t}\n}\n\n// Compat support for \"Tabify\" plugin.\n.acf-postbox.acf-hidden {\n\tdisplay: none !important;\n}\n\n// Focus Attention.\n.acf-attention {\n\ttransition: border 0.250s ease-out;\n\t&.-focused {\n\t\tborder: #23282d solid 1px !important;\n\t\ttransition: none;\n\t}\n}\ntr.acf-attention {\n\ttransition: box-shadow 0.250s ease-out;\n\tposition: relative;\n\t&.-focused {\n\t\tbox-shadow: #23282d 0 0 0px 1px !important;\n\t}\n}", "// Gutenberg specific styles.\n#editor {\n\n\t// Postbox container.\n\t.edit-post-layout__metaboxes {\n\t\tpadding: 0;\n\t\t.edit-post-meta-boxes-area {\n\t\t\tmargin: 0;\n\t\t}\n\t}\n\n\t// Sidebar postbox container.\n\t.metabox-location-side {\n\t\t.postbox-container {\n\t\t\tfloat: none;\n\t\t}\n\t}\n\n\t// Alter postbox to look like panel component.\n\t.postbox {\n\t\tcolor: #444;\n\n\t\t> .postbox-header {\n\t\t\t.hndle {\n\t\t\t\tborder-bottom: none;\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground: transparent;\n\t\t\t\t}\n\t\t\t}\n\t\t\t.handle-actions {\n\t\t\t\t.handle-order-higher,\n\t\t\t\t.handle-order-lower {\n\t\t\t\t\twidth: 1.62rem;\n\t\t\t\t}\n\n\t\t\t\t// Fix \"Edit\" icon height.\n\t\t\t\t.acf-hndle-cog {\n\t\t\t\t\theight: 44px;\n\t\t\t\t\tline-height: 44px;\n\t\t\t\t}\n\t\t\t}\n\t\t\t&:hover {\n\t\t\t\tbackground: #f0f0f0;\n\t\t\t}\n\t\t}\n\n\t\t// Hide bottom border of last postbox.\n\t\t&:last-child.closed > .postbox-header {\n\t\t\tborder-bottom: none;\n\t\t}\n\t\t&:last-child > .inside {\n\t\t\tborder-bottom: none;\n\t\t}\n\t}\n\n\t// Prevent metaboxes being forced offscreen.\n\t.block-editor-writing-flow__click-redirect {\n\t\tmin-height: 50px;\n\t}\n}\n\n// Fix to display \"High\" metabox area when dragging metaboxes.\nbody.is-dragging-metaboxes #acf_after_title-sortables{\n\toutline: 3px dashed #646970;\n\tdisplay: flow-root;\n\tmin-height: 60px;\n\tmargin-bottom: 3px !important\n}\n\n\n\n"], "names": [], "sourceRoot": ""}